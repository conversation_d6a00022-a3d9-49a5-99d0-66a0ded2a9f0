"""
Simple Offerte Service - Uses existing document system
This service creates customers and generates offerte documents using the existing document infrastructure.
"""
import logging
from typing import Dict, Optional
from datetime import datetime

import uuid

from app.repositories.customer_repository import CustomerRepository
from app.services.document_service import DocumentService
from app.services.simple_quote_pdf_service import SimpleQuotePDFService
from app.utils.firebase import upload_bytes_to_storage
from app.models.customer import Customer
from app import db

logger = logging.getLogger(__name__)

class SimpleOfferteService:
    """Service for creating simple offerte documents using existing document system."""

    def __init__(self):
        """Initialize the service with repositories."""
        self.customer_repo = CustomerRepository()
        self.document_service = DocumentService()
        self.pdf_service = SimpleQuotePDFService()

    def create_offerte_with_customer(self, offerte_data: Dict, created_by: int) -> Dict:
        """
        Create or find customer and generate offerte document.

        Args:
            offerte_data: Offerte data including customer info and quote details
            created_by: User ID who created the offerte

        Returns:
            Dict with customer and document info
        """
        try:
            # Extract customer data
            customer_data = {
                'name': offerte_data['customer_name'],
                'email': offerte_data.get('customer_email'),
                'phone': offerte_data.get('customer_phone'),
                'address': offerte_data.get('customer_address', ''),
                'city': offerte_data.get('customer_city', ''),
                'postal_code': offerte_data.get('customer_postal_code', '')
            }

            # Find or create customer
            customer = self._find_or_create_customer(customer_data)

            # Create a simple quote object for PDF generation
            quote_obj = self._create_quote_object(offerte_data, customer)

            # Generate PDF
            pdf_content = self.pdf_service.generate_quote_pdf(quote_obj)

            # Create filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"offerte_{customer.name.replace(' ', '_')}_{timestamp}.pdf"

            # Upload PDF to storage
            destination_path = f"documents/{customer.id}/offerte/{uuid.uuid4()}.pdf"
            file_url, storage_path = upload_bytes_to_storage(
                pdf_content,
                destination_path,
                content_type="application/pdf"
            )

            # Create document using existing document service
            # Use "not_applicable" status so multiple offertes can coexist on active documents page
            document = self.document_service.create_document_from_bytes(
                customer_id=customer.id,
                event_id=None,  # No event needed for offerte
                file_content=pdf_content,
                filename=filename,
                document_type="offerte",
                uploaded_by=created_by,
                file_url=file_url,
                storage_path=storage_path,
                expiry_date=None,  # Offerte doesn't expire
                related_document_id=None,
                version_status="not_applicable"
            )

            logger.info(f"Created offerte document {document['id']} for customer {customer.name}")

            return {
                'customer': customer.to_dict(),
                'document': document,
                'offerte_data': offerte_data,
                'quote_number': quote_obj.quote_number
            }

        except Exception as e:
            logger.error(f"Failed to create offerte: {str(e)}")
            raise Exception(f"Failed to create offerte: {str(e)}")

    def _find_or_create_customer(self, customer_data: Dict):
        """Find existing customer or create new one."""
        try:
            # Try to find existing customer by email or phone using direct database queries
            existing_customer = None

            if customer_data.get('email'):
                # Try to find by email using direct query
                existing_customer = Customer.query.filter_by(email=customer_data['email']).first()

            if not existing_customer and customer_data.get('phone'):
                # Try to find by phone using direct query
                existing_customer = Customer.query.filter_by(phone=customer_data['phone']).first()

            if existing_customer:
                logger.info(f"Found existing customer: {existing_customer.name}")
                return existing_customer

            # Create new customer
            customer_create_data = {
                'name': customer_data['name'],
                'email': customer_data.get('email'),
                'phone': customer_data.get('phone'),
                'address': customer_data.get('address', ''),
                'city': customer_data.get('city', ''),
                'postal_code': customer_data.get('postal_code', '')
            }
            new_customer = self.customer_repo.create(customer_create_data)

            logger.info(f"Created new customer: {new_customer.name}")
            return new_customer

        except Exception as e:
            logger.error(f"Failed to find or create customer: {str(e)}")
            raise Exception(f"Failed to find or create customer: {str(e)}")

    def _create_quote_object(self, offerte_data: Dict, customer):
        """Create a simple object that mimics SimpleQuote for PDF generation."""
        class SimpleQuoteObject:
            def __init__(self, data, customer):
                # Required attributes for PDF generation
                self.id = 0  # Dummy ID for PDF generation
                self.created_at = datetime.now()
                self.signed_at = datetime.now() if data.get('signature_data') else None

                # Customer info
                self.customer_name = customer.name
                self.customer_email = customer.email or ''
                self.customer_phone = customer.phone or ''
                self.customer_address = customer.address or ''
                self.customer_city = customer.city or ''
                self.customer_postal_code = customer.postal_code or ''

                # Quote details
                self.category = data.get('category', 'ALARM')
                self.payment_terms = data.get('payment_terms', 'ONE_TERM')
                self.discount_percentage = float(data.get('discount_percentage', 0))

                # Extra products
                self.extra_magneetcontact = data.get('extra_magneetcontact', 0)
                self.extra_shock_sensor = data.get('extra_shock_sensor', 0)
                self.extra_pir_normaal = data.get('extra_pir_normaal', 0)
                self.extra_rookmelder = data.get('extra_rookmelder', 0)
                self.extra_pircam = data.get('extra_pircam', 0)
                self.extra_bediendeel = data.get('extra_bediendeel', 0)
                self.extra_sirene = data.get('extra_sirene', 0)


                # Also accept 'extra_products' map and plain keys without 'extra_' prefix from frontend
                extra_products_map = data.get('extra_products') or {}
                try:
                    for key, qty in extra_products_map.items():
                        try:
                            qty_int = int(qty)
                        except (ValueError, TypeError):
                            continue
                        if qty_int > 0:
                            attr = key if key.startswith('extra_') else f'extra_{key}'
                            setattr(self, attr, qty_int)
                except Exception:
                    # Be resilient if structure is not as expected
                    pass

                # Handle top-level plain keys (e.g., 'magneetcontact': 2) coming from the frontend
                for plain_key in ['magneetcontact', 'shock_sensor', 'pir_normaal', 'rookmelder', 'pircam', 'bediendeel', 'sirene']:
                    if plain_key in data:
                        try:
                            qty_int = int(data.get(plain_key) or 0)
                        except (ValueError, TypeError):
                            qty_int = 0
                        if qty_int > 0:
                            setattr(self, f'extra_{plain_key}', qty_int)

                # Video doorbell options
                self.videodoorbell_free = data.get('videodoorbell_free', False)
                self.videodoorbell_paid = data.get('videodoorbell_paid', False)

                # Status and signature
                self.status = 'SIGNED' if data.get('signature_data') else 'DRAFT'
                self.signature_data = data.get('signature_data')

                # Generate quote number
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                self.quote_number = f"OFF-{timestamp}"

                # Calculate totals (simplified)
                self.calculate_totals()

            def calculate_totals(self):
                """Calculate quote totals using the same logic as SimpleQuote model."""
                from app.utils.simple_quote_products import (
                    PRICING_CONFIG, get_product_by_key, calculate_installation_cost, calculate_monthly_increase
                )

                # Set basic costs
                self.base_installation_cost = PRICING_CONFIG['base_installation_cost_incl_vat']  # €999.99

                # Calculate extra product costs
                extra_cost_incl_vat = 0
                extra_items_count = 0

                # Process extra products
                for attr_name in dir(self):
                    if attr_name.startswith('extra_') and hasattr(self, attr_name):
                        quantity = getattr(self, attr_name, 0)
                        if quantity > 0:
                            # Remove 'extra_' prefix to get product key
                            product_key = attr_name.replace('extra_', '')
                            product = get_product_by_key(product_key)
                            if product and 'price_incl_vat' in product:
                                extra_cost_incl_vat += product['price_incl_vat'] * quantity
                                extra_items_count += quantity

                # Calculate installation cost with extra products and videodoorbell
                self.total_installation_cost = calculate_installation_cost(
                    extra_cost_incl_vat,
                    extra_items_count,
                    self.videodoorbell_free,
                    self.videodoorbell_paid
                )

                # Calculate monthly costs with extra products
                self.monthly_equipment_cost = calculate_monthly_increase(extra_cost_incl_vat)
                self.monthly_monitoring_cost = PRICING_CONFIG['monthly_monitoring']  # €8.50
                self.monthly_maintenance_cost = PRICING_CONFIG['monthly_maintenance']  # €8.50

                # Set product cost
                base_cost_incl_vat = PRICING_CONFIG['base_package_cost_excl_vat'] * (1 + PRICING_CONFIG['vat_percentage'] / 100)
                self.total_product_cost_excl_vat = base_cost_incl_vat + extra_cost_incl_vat

            def get_max_discount(self):
                """Get maximum allowed discount based on payment terms."""
                if self.payment_terms == 'ONE_TERM':
                    return 25.0
                elif self.payment_terms == 'TWO_TERMS':
                    return 15.0
                elif self.payment_terms == 'THREE_TERMS':
                    return 10.0
                return 0.0

            def get_final_installation_cost(self):
                """Get final installation cost after discount."""
                discount_amount = self.total_installation_cost * (self.discount_percentage / 100)
                final_cost = self.total_installation_cost - discount_amount

                from app.utils.simple_quote_products import PRICING_CONFIG
                minimum_cost = PRICING_CONFIG['base_installation_cost_incl_vat'] * 0.75  # €749.99

                return max(final_cost, minimum_cost)

            def get_total_monthly_cost(self):
                """Get total monthly cost (minimum €49.99)."""
                total = self.monthly_equipment_cost + self.monthly_monitoring_cost + self.monthly_maintenance_cost

                from app.utils.simple_quote_products import PRICING_CONFIG
                minimum_monthly = PRICING_CONFIG['base_monthly_total']  # €49.99

                return max(total, minimum_monthly)

        return SimpleQuoteObject(offerte_data, customer)
