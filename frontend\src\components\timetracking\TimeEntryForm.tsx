import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useConfirmation } from '../../context/ConfirmationContext';
import { TimeEntryFormData } from '../../types/timeEntry';
import { FaClock, FaCalendarAlt, FaUser, FaAlignLeft, FaSave, FaTimes, FaExclamationTriangle } from 'react-icons/fa';
import { getAllUsers } from '../../services/userService';
import { checkTimeEntryOverlap } from '../../services/timeTrackingService';
import { User } from '../../types/user';
import * as Yup from 'yup';

interface TimeEntryFormProps {
  initialData?: Partial<TimeEntryFormData & { id?: number }>;
  onSubmit: (data: TimeEntryFormData) => void;
  onCancel: () => void;
  isAdmin?: boolean;
}

// Validation schema
const timeEntrySchema = Yup.object().shape({
  user_id: Yup.number().required('Gebruiker is verplicht'),
  date: Yup.string().required('Datum is verplicht'),
  start_time: Yup.string().required('Starttijd is verplicht'),
  end_time: Yup.string().required('Eindtijd is verplicht')
    .test('is-after-start', 'Eindtijd moet na starttijd zijn', function(value) {
      const { start_time } = this.parent;
      if (!start_time || !value) return true;
      return value > start_time;
    }),
  description: Yup.string().nullable()
});

const TimeEntryForm: React.FC<TimeEntryFormProps> = ({
  initialData = {},
  onSubmit,
  onCancel,
  isAdmin = false
}) => {
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [overlappingEntries, setOverlappingEntries] = useState<any[]>([]);
  const [showOverlapWarning, setShowOverlapWarning] = useState<boolean>(false);

  // Set default date to today if not provided
  const today = new Date().toISOString().split('T')[0];

  const [formData, setFormData] = useState<TimeEntryFormData>({
    user_id: initialData.user_id || (user?.id || 0),
    date: initialData.date || today,
    start_time: initialData.start_time || '09:00',
    end_time: initialData.end_time || '17:00',
    break_time: initialData.break_time || 0,
    description: initialData.description || ''
  });

  useEffect(() => {
    // Only load users if admin
    if (isAdmin) {
      const loadUsers = async () => {
        try {
          setLoading(true);
          const response = await getAllUsers();
          setUsers(response.users);
        } catch (error) {
          console.error('Error loading users:', error);
        } finally {
          setLoading(false);
        }
      };

      loadUsers();
    }
  }, [isAdmin]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const newFormData = { ...formData, [name]: value };
    setFormData(newFormData);

    // Clear error for this field when user changes it
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }

    // Check for overlaps when date, start_time, or end_time changes
    if (['date', 'start_time', 'end_time', 'user_id'].includes(name)) {
      console.log(`Field ${name} changed to ${value}, checking for overlaps`);

      // Only check if we have all required fields
      if (newFormData.date && newFormData.start_time && newFormData.end_time && newFormData.user_id) {
        console.log('All required fields present, checking for overlaps');

        // Use setTimeout to ensure the state is updated before checking
        setTimeout(async () => {
          await checkForOverlaps(newFormData);
        }, 100);
      } else {
        console.log('Missing required fields for overlap check:');
        console.log('date:', newFormData.date);
        console.log('start_time:', newFormData.start_time);
        console.log('end_time:', newFormData.end_time);
        console.log('user_id:', newFormData.user_id);
      }
    }
  };

  const checkForOverlaps = async (data: TimeEntryFormData): Promise<boolean> => {
    try {
      setLoading(true);
      console.log('Checking for overlaps with data:', data);

      // Make sure we have all required fields
      if (!data.user_id || !data.date || !data.start_time || !data.end_time) {
        console.log('Missing required fields for overlap check');
        return false;
      }

      const entryId = initialData.id; // This might be undefined for new entries
      console.log('Entry ID for exclusion:', entryId);

      const result = await checkTimeEntryOverlap(
        data.user_id,
        data.date,
        data.start_time,
        data.end_time,
        entryId
      );

      console.log('Overlap check result:', result);

      if (result.overlaps) {
        console.log('Overlapping entries found:', result.entries);
        setOverlappingEntries(result.entries);
        setShowOverlapWarning(true);
        return true; // Return true if overlaps found
      } else {
        console.log('No overlapping entries found');
        setOverlappingEntries([]);
        setShowOverlapWarning(false);
        return false; // Return false if no overlaps found
      }
    } catch (error) {
      console.error('Error checking for overlaps:', error);
      showConfirmation({
        title: 'Fout bij controle op overlappingen',
        message: 'Er is een fout opgetreden bij het controleren op overlappende tijdsregistraties. Probeer het opnieuw of neem contact op met de beheerder.',
        confirmText: 'OK',
        showCancel: false,
        onConfirm: () => {
          // Do nothing, just close the dialog
        }
      });
      return false; // Return false on error
    } finally {
      setLoading(false);
    }
  };

  const validateForm = async (): Promise<boolean> => {
    try {
      await timeEntrySchema.validate(formData, { abortEarly: false });
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const newErrors: Record<string, string> = {};
        error.inner.forEach(err => {
          if (err.path) {
            newErrors[err.path] = err.message;
          }
        });
        setErrors(newErrors);

        // Validation errors are shown inline, no need for a dialog
      } else {
        // Show confirmation dialog for unexpected errors
        showConfirmation({
          title: 'Onverwachte fout',
          message: 'Er is een onverwachte fout opgetreden. Probeer het opnieuw of neem contact op met de beheerder.',
          confirmText: 'OK',
          showCancel: false,
          onConfirm: () => {
            // Do nothing, just close the dialog
          }
        });
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted with data:', formData);

    const isValid = await validateForm();
    if (!isValid) {
      console.log('Form validation failed');
      return;
    }

    console.log('Form validation passed, checking for overlaps');

    // Check for overlaps one last time before submitting
    const hasOverlaps = await checkForOverlaps(formData);

    // If there are overlaps, show an error message and prevent submission
    if (hasOverlaps) {
      console.log('Overlapping entries found, preventing submission');
      showConfirmation({
        title: 'Overlappende tijdsregistraties',
        message: 'Er zijn overlappende tijdsregistraties gevonden. Het is niet mogelijk om overlappende tijdsregistraties op te slaan. Pas de tijden aan zodat ze niet overlappen met bestaande registraties.',
        confirmText: 'OK',
        showCancel: false,
        confirmButtonClass: 'bg-red-600 hover:bg-red-700',
        onConfirm: () => {
          // Do nothing, just close the dialog
        }
      });
      return;
    }

    console.log('No overlaps found, submitting form');
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4">
          {initialData.user_id ? 'Uren bewerken' : 'Uren toevoegen'}
        </h2>

        {isAdmin && (
          <div className="mb-4">
            <label htmlFor="user_id" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
              <FaUser className="inline mr-2" /> Gebruiker
            </label>
            <select
              id="user_id"
              name="user_id"
              value={formData.user_id}
              onChange={handleChange}
              className={`input w-full ${errors.user_id ? 'border-red-500' : ''}`}
              disabled={loading}
            >
              <option value="">Selecteer gebruiker</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name || user.email}
                </option>
              ))}
            </select>
            {errors.user_id && <p className="text-red-500 text-xs mt-1">{errors.user_id}</p>}
          </div>
        )}

        <div className="mb-4">
          <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
            <FaCalendarAlt className="inline mr-2" /> Datum
          </label>
          <input
            type="date"
            id="date"
            name="date"
            value={formData.date}
            onChange={handleChange}
            className={`input w-full ${errors.date ? 'border-red-500' : ''}`}
            max={today}
          />
          {errors.date && <p className="text-red-500 text-xs mt-1">{errors.date}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label htmlFor="start_time" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
              <FaClock className="inline mr-2" /> Starttijd
            </label>
            <input
              type="time"
              id="start_time"
              name="start_time"
              value={formData.start_time}
              onChange={handleChange}
              className={`input w-full ${errors.start_time ? 'border-red-500' : ''}`}
            />
            {errors.start_time && <p className="text-red-500 text-xs mt-1">{errors.start_time}</p>}
          </div>

          <div>
            <label htmlFor="end_time" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
              <FaClock className="inline mr-2" /> Eindtijd
            </label>
            <input
              type="time"
              id="end_time"
              name="end_time"
              value={formData.end_time}
              onChange={handleChange}
              className={`input w-full ${errors.end_time ? 'border-red-500' : ''}`}
            />
            {errors.end_time && <p className="text-red-500 text-xs mt-1">{errors.end_time}</p>}
          </div>

          <div>
            <label htmlFor="break_time" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
              <FaClock className="inline mr-2" /> Pauze tijd
            </label>
            <select
              id="break_time"
              name="break_time"
              value={formData.break_time}
              onChange={handleChange}
              className="input w-full"
            >
              <option value={0}>Geen pauze</option>
              <option value={15}>15 minuten</option>
              <option value={30}>30 minuten</option>
              <option value={45}>45 minuten</option>
              <option value={60}>1 uur</option>
              <option value={90}>1,5 uur</option>
              <option value={120}>2 uur</option>
            </select>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Pauze tijd wordt automatisch afgetrokken van de totale werktijd
            </p>
          </div>
        </div>

        {showOverlapWarning && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-start">
              <FaExclamationTriangle className="text-red-500 mt-1 mr-2" />
              <div>
                <h4 className="text-sm font-medium text-red-800">Overlappende tijdsregistraties gevonden</h4>
                <p className="text-xs text-red-700 mt-1">
                  <strong>Het is niet mogelijk om overlappende tijdsregistraties op te slaan, ook niet als ze nog in behandeling zijn.</strong> Er zijn al tijdsregistraties voor deze gebruiker op deze datum die overlappen met de gekozen tijden:
                </p>
                <ul className="mt-2 text-xs text-red-700 list-disc list-inside">
                  {overlappingEntries.map((entry, index) => (
                    <li key={index}>
                      {entry.date}: {entry.start_time.substring(0, 5)} - {entry.end_time.substring(0, 5)}
                      {entry.description ? ` (${entry.description})` : ''}
                    </li>
                  ))}
                </ul>
                <p className="text-xs text-red-700 mt-2">
                  Pas de tijden aan zodat ze niet overlappen met bestaande registraties.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="mb-4">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
            <FaAlignLeft className="inline mr-2" /> Omschrijving (optioneel)
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="input w-full"
            placeholder="Beschrijf de uitgevoerde werkzaamheden"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="btn btn-secondary flex items-center"
        >
          <FaTimes className="mr-2" /> Annuleren
        </button>
        <button
          type="submit"
          className="btn btn-primary flex items-center"
        >
          <FaSave className="mr-2" /> Opslaan
        </button>
      </div>
    </form>
  );
};

export default TimeEntryForm;