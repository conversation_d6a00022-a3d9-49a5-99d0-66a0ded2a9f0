import api from "../api";
import { Event } from "../types/event";

export interface DashboardMetrics {
  events: {
    total: number;
    pending: number;
    completed: number;
    upcoming: number;
    by_type: Record<string, number>;
  };
  users: {
    total: number;
  };
  customers: {
    total: number;
  };
  documents: {
    total_active: number;
    expiring_soon: number;
    expired: number;
    by_status: {
      red: number;
      orange: number;
      green: number;
    };
  };
}

interface GetAllEventsResponse {
  events: Event[];
  total: number;
  page: number;
  per_page: number;
}

export const getAllEvents = async (page: number = 1, perPage: number = 20): Promise<GetAllEventsResponse> => {
  const response = await api.get(`/events?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getPendingEvents = async (page: number = 1, perPage: number = 20, cacheBust?: boolean): Promise<GetAllEventsResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    per_page: perPage.toString()
  });
  
  // Add cache-busting parameter if requested
  if (cacheBust) {
    params.append('_t', Date.now().toString());
  }
  
  const response = await api.get(`/events/pending?${params.toString()}`);
  return response.data;
};

export const getCompletedEvents = async (page: number = 1, perPage: number = 20): Promise<GetAllEventsResponse> => {
  const response = await api.get(`/events/completed?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getMyEvents = async (page: number = 1, perPage: number = 100): Promise<GetAllEventsResponse> => {
  // Increased perPage to 100 to ensure we get all events
  const response = await api.get(`/events/my-events?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getEventById = async (eventId: number): Promise<Event> => {
  const response = await api.get(`/events/${eventId}`);
  return response.data;
};

export const createEvent = async (
  customer_id: number | null,
  event_type: string,
  description: string,
  scheduled_date: string,
  user_ids?: number[] | null,
  document_id?: number | null
): Promise<Event> => {
  const response = await api.post("/events", {
    customer_id: customer_id || null,
    event_type,
    description,
    scheduled_date,
    user_ids: user_ids || null,
    document_id: document_id || null
  });
  return response.data;
};

// Legacy function for backward compatibility
export const createEventLegacy = async (
  customer_id: number | null,
  event_type: string,
  description: string,
  scheduled_date: string,
  user_id?: number | null,
  document_id?: number | null
): Promise<Event> => {
  const user_ids = user_id ? [user_id] : null;
  return createEvent(customer_id, event_type, description, scheduled_date, user_ids, document_id);
};

export const updateEvent = async (
  eventId: number,
  customer_id: number,
  event_type: string,
  description: string,
  scheduled_date: string,
  user_ids?: number[],
  document_id?: number
): Promise<Event> => {
  const response = await api.put(`/events/${eventId}`, {
    customer_id,
    event_type,
    description,
    scheduled_date,
    user_ids,
    document_id
  });
  return response.data;
};

// Legacy function for backward compatibility
export const updateEventLegacy = async (
  eventId: number,
  customer_id: number,
  event_type: string,
  description: string,
  scheduled_date: string,
  user_id?: number,
  document_id?: number
): Promise<Event> => {
  const user_ids = user_id ? [user_id] : undefined;
  return updateEvent(eventId, customer_id, event_type, description, scheduled_date, user_ids, document_id);
};

export const completeEvent = async (eventId: number): Promise<Event> => {
  const response = await api.post(`/events/${eventId}/complete`);
  return response.data;
};

export const deleteEvent = async (eventId: number): Promise<void> => {
  await api.delete(`/events/${eventId}`);
};

export const getDashboardMetrics = async (): Promise<DashboardMetrics> => {
  const response = await api.get('/events/dashboard-metrics');
  return response.data;
};

export interface CalendarEventsResponse {
  events: Event[];
}

export const getCalendarEvents = async (
  startDate?: string,
  endDate?: string,
  userId?: number,
  customerId?: number
): Promise<CalendarEventsResponse> => {
  let url = '/events/calendar';
  const params = new URLSearchParams();

  if (startDate) params.append('start_date', startDate);
  if (endDate) params.append('end_date', endDate);
  if (userId) params.append('user_id', userId.toString());
  if (customerId) params.append('customer_id', customerId.toString());

  if (params.toString()) {
    url += `?${params.toString()}`;
  }

  const response = await api.get(url);
  return response.data;
};

// Haal events op die gekoppeld zijn aan een offerte
export const getEventsByQuotationId = async (quotationId: number): Promise<Event[]> => {
  const response = await api.get(`/events/quotation/${quotationId}`);
  return response.data.events;
};
