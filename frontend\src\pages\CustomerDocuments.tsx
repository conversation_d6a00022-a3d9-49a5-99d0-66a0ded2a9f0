import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { getDocumentsByCustomer, createDocument, deleteDocument } from "../services/documentService";
import { getCustomerById } from "../services/customerService";
import { Document } from "../types/document";
import { Customer } from "../types/customer";
import { useAuth } from "../context/AuthContext";
import { useConfirmation } from "../context/ConfirmationContext";
import { auth } from "../firebase";
import api from "../api";
import LoadingSpinner from "../components/LoadingSpinner";
import DocumentPreview from "../components/DocumentPreview";
import DocumentUploadWithTemplate from "../components/DocumentUploadWithTemplate";
import DocumentCategorySection from "../components/DocumentCategorySection";
import Breadcrumbs from "../components/Breadcrumbs";
import {
  FaPlus,
  FaFileAlt,
  FaHistory,
  FaArrowLeft,
  FaFileWord,
  FaShieldAlt,
  FaCertificate,
  FaClipboardList,
  FaTools,
  FaBuilding,
  FaFileContract,
  FaReceipt,
  FaFolder,
  FaFolderOpen,
  FaDownload,
  FaTrash,
  FaEye,
  FaCalendarAlt,
  FaExclamationTriangle,
  FaCheckCircle,
  FaInfoCircle
} from "react-icons/fa";
import { documentUploadSchema, validateData } from "../utils/validation";
import { useMobile } from "../hooks/useMobile";
import { getTwelveMonthExpiryDate } from '../utils/dateUtils';

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('en-US', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const CustomerDocuments: React.FC = () => {
  const { customerId } = useParams<{ customerId: string | undefined }>();
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const { isMobile } = useMobile();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [currentStep, setCurrentStep] = useState<string | null>(null);
  const [parentDocumentId, setParentDocumentId] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [expiryType, setExpiryType] = useState<"date" | "niet_van_toepassing">("date");
  const [documentNotApplicable, setDocumentNotApplicable] = useState(false);
  const [activeTab, setActiveTab] = useState<'active' | 'history' | 'upload'>('active');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showTemplateUpload, setShowTemplateUpload] = useState<boolean>(false);
  const [useVersionStatus, setUseVersionStatus] = useState<boolean>(true);
  const [versionStatus, setVersionStatus] = useState<"active" | "inactive">("active");

  const ADMIN_DOCUMENT_TYPES = [
    "offerte",
    "werkbon",
    "onderhoudsbon",
    "onderhoudscontract",
    "meldkamercontract",
    "beveiligingscertificaat",
    "intakedocument",
    "projectietekening",
    "beveiligingsplan",
    "kabeltekeningen",
    "vrije_documenten",
    "checklist oplevering installatie",
    "factuur"
  ];

  const BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS = [
    "intakedocument",
    "projectietekening",
    "beveiligingsplan",
    "kabeltekeningen"
  ];

  // Document type categories for better organization
  const DOCUMENT_CATEGORIES = {
    security: {
      title: "Beveiliging & Certificaten",
      icon: FaShieldAlt,
      color: "text-red-600",
      bgColor: "bg-red-50 dark:bg-red-900/20",
      borderColor: "border-red-200 dark:border-red-800",
      types: ["beveiligingscertificaat", "beveiligingsplan", "intakedocument"]
    },
    contracts: {
      title: "Contracten & Overeenkomsten",
      icon: FaFileContract,
      color: "text-blue-600",
      bgColor: "bg-blue-50 dark:bg-blue-900/20",
      borderColor: "border-blue-200 dark:border-blue-800",
      types: ["onderhoudscontract", "meldkamercontract", "offerte"]
    },
    maintenance: {
      title: "Onderhoud & Service",
      icon: FaTools,
      color: "text-green-600",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      borderColor: "border-green-200 dark:border-green-800",
      types: ["werkbon", "onderhoudsbon", "checklist oplevering installatie"]
    },
    technical: {
      title: "Technische Documenten",
      icon: FaBuilding,
      color: "text-purple-600",
      bgColor: "bg-purple-50 dark:bg-purple-900/20",
      borderColor: "border-purple-200 dark:border-purple-800",
      types: ["projectietekening", "kabeltekeningen"]
    },
    financial: {
      title: "Financieel",
      icon: FaReceipt,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
      borderColor: "border-yellow-200 dark:border-yellow-800",
      types: ["factuur"]
    },
    general: {
      title: "Algemene Documenten",
      icon: FaFileAlt,
      color: "text-gray-600",
      bgColor: "bg-gray-50 dark:bg-gray-900/20",
      borderColor: "border-gray-200 dark:border-gray-800",
      types: ["vrije_documenten"]
    }
  };

  // Helper function to get category for a document type
  const getCategoryForDocumentType = (documentType: string) => {
    for (const [categoryKey, category] of Object.entries(DOCUMENT_CATEGORIES)) {
      if (category.types.includes(documentType)) {
        return { key: categoryKey, ...category };
      }
    }
    return { key: 'general', ...DOCUMENT_CATEGORIES.general };
  };

  // Helper function to format document type names
  const formatDocumentTypeName = (type: string) => {
    const typeNames: { [key: string]: string } = {
      'beveiligingscertificaat': 'Beveiligingscertificaat',
      'onderhoudscontract': 'Onderhoudscontract',
      'meldkamercontract': 'Meldkamercontract',
      'checklist oplevering installatie': 'Checklist Oplevering',
      'vrije_documenten': 'Vrije Documenten',
      'projectietekening': 'Projectietekening',
      'beveiligingsplan': 'Beveiligingsplan',
      'kabeltekeningen': 'Kabeltekeningen',
      'intakedocument': 'Intakedocument',
      'onderhoudsbon': 'Onderhoudsbon',
      'werkbon': 'Werkbon',
      'offerte': 'Offerte',
      'factuur': 'Factuur'
    };
    return typeNames[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const groupAndSortDocuments = (docs: Document[]) => {
    // First, separate active and inactive documents
    const activeDocuments = docs.filter(doc => doc.status === "active" || doc.status === "not_applicable");
    const inactiveDocuments = docs.filter(doc => doc.status === "inactive");

    // Sort both arrays by date (newest first)
    const sortByDate = (a: Document, b: Document) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime();

    activeDocuments.sort(sortByDate);
    inactiveDocuments.sort(sortByDate);

    // Group by category instead of just document type
    const groupByCategory = (documents: Document[]) => {
      const categoryGroups: { [key: string]: { category: any, documents: { [key: string]: Document[] } } } = {};

      documents.forEach(doc => {
        if (!doc.related_document_id) {  // Only group main documents, not sub-documents
          const category = getCategoryForDocumentType(doc.document_type);

          if (!categoryGroups[category.key]) {
            categoryGroups[category.key] = {
              category: category,
              documents: {}
            };
          }

          if (!categoryGroups[category.key].documents[doc.document_type]) {
            categoryGroups[category.key].documents[doc.document_type] = [];
          }

          categoryGroups[category.key].documents[doc.document_type].push(doc);
        }
      });

      return categoryGroups;
    };

    return {
      active: groupByCategory(activeDocuments),
      inactive: groupByCategory(inactiveDocuments)
    };
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!customerId) return;
      setInitialLoading(true);
      try {
        // Fetch documents
        const documentsResponse = await getDocumentsByCustomer(parseInt(customerId));
        setDocuments(documentsResponse || []);

        // Fetch customer details
        const customerResponse = await getCustomerById(parseInt(customerId));
        setCustomer(customerResponse);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || "Failed to fetch data.");
      } finally {
        setInitialLoading(false);
      }
    };
    fetchData();
  }, [customerId]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFile(e.target.files[0]);
    }
  };

  const handleDocumentTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedType = e.target.value;
    setDocumentType(selectedType);
    
    // Auto-set 12-month expiration for onderhoudsbon documents
    if (selectedType === 'onderhoudsbon') {
      setExpiryType('date');
      setExpiryDate(getTwelveMonthExpiryDate());
    }
  };

  // Removed handleNewFileChange function

  const startBeveiligingscertificaatProcess = () => {
    setCurrentStep("beveiligingscertificaat");
    setDocumentType("beveiligingscertificaat");
    setDocumentNotApplicable(false);
    setExpiryType("date");
    setFile(null);
    setExpiryDate("");
  };

  const handleUpload = async (isMock: boolean = false, isStep: boolean = false): Promise<boolean> => {
    setValidationErrors([]);

    // Validate input data using Yup
    const { isValid, errors } = await validateData(documentUploadSchema, {
      document_type: documentType,
      file: file,
      expiryType: expiryType,
      expiry_date: expiryDate ? new Date(expiryDate) : null,
      documentNotApplicable: documentNotApplicable
    });

    if (!isValid) {
      setValidationErrors(errors);
      return false;
    }

    if (!customerId) {
      setError("Customer ID is missing.");
      return false;
    }

    setSubmitting(true);
    try {
      const formattedExpiryDate = expiryType === "date" ? expiryDate : undefined;
      if (isStep && currentStep) {
        if (!documentNotApplicable && file) {
          const document = await createDocument(
            parseInt(customerId),
            null,
            file,
            currentStep,
            expiryType,
            formattedExpiryDate,
            parentDocumentId || undefined,
            true,
            documentNotApplicable
          );
          if (currentStep === "beveiligingscertificaat") {
            setParentDocumentId(document.id);
            setCurrentStep(BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS[0]);
            setDocumentType(BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS[0]);
          } else {
            const nextStepIndex = BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS.indexOf(currentStep) + 1;
            if (nextStepIndex < BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS.length) {
              setCurrentStep(BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS[nextStepIndex]);
              setDocumentType(BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS[nextStepIndex]);
            } else {
              setCurrentStep(null);
              setParentDocumentId(null);
              setDocumentType("");
            }
          }
        } else if (documentNotApplicable) {
          // Skip document creation if marked as not applicable
          console.log(`Step ${currentStep} marked as not applicable, skipping creation`);
          // Move to next step
          if (currentStep === "beveiligingscertificaat") {
            setCurrentStep(BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS[0]);
            setDocumentType(BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS[0]);
          } else {
            const nextStepIndex = BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS.indexOf(currentStep) + 1;
            if (nextStepIndex < BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS.length) {
              setCurrentStep(BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS[nextStepIndex]);
              setDocumentType(BEVEILIGINGSCERTIFICAAT_SUBDOCUMENTS[nextStepIndex]);
            } else {
              setCurrentStep(null);
              setParentDocumentId(null);
              setDocumentType("");
            }
          }
        } else {
          // No file provided and not marked as not applicable
          throw new Error("Please select a file or mark the document as not applicable");
        }
      } else {
        if (!documentNotApplicable && file) {
          await createDocument(
            parseInt(customerId),
            null,
            file,
            documentType,
            expiryType,
            formattedExpiryDate,
            undefined,
            true,
            documentNotApplicable,
            useVersionStatus,
            versionStatus
          );
        } else if (documentNotApplicable) {
          // Skip document creation if marked as not applicable
          console.log("Document marked as not applicable, skipping creation");
        } else {
          // No file provided and not marked as not applicable
          throw new Error("Please select a file or mark the document as not applicable");
        }
        setDocumentType("");
      }
      setFile(null);
      setExpiryDate("");
      setDocumentNotApplicable(false);
      setExpiryType("date");
      setUseVersionStatus(true);
      setVersionStatus("active");
      setError(null);
      const response = await getDocumentsByCustomer(parseInt(customerId));
      setDocuments(response || []);
      return true;
    } catch (err: any) {
      setError(err.response?.data?.error || err.message || "Failed to process document.");
      return false;
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (documentId: number) => {
    const document = documents.find(doc => doc.id === documentId);
    if (!document) return;

    const isMainDocument = document.document_type === "beveiligingscertificaat" && document.sub_documents.length > 0;

    showConfirmation({
      title: "Delete Document",
      message: isMainDocument
        ? `Are you sure you want to delete this ${document.document_type} document? This will also delete all associated sub-documents.`
        : `Are you sure you want to delete this ${document.document_type} document? This action cannot be undone.`,
      confirmText: "Delete",
      confirmButtonClass: "bg-red-600 hover:bg-red-700",
      onConfirm: async () => {
        setSubmitting(true);
        try {
          await deleteDocument(documentId);
          const response = await getDocumentsByCustomer(parseInt(customerId!));
          setDocuments(response || []);
          setError(null);
        } catch (err: any) {
          setError(err.response?.data?.error || err.message || "Failed to delete document.");
        } finally {
          setSubmitting(false);
        }
      }
    });
  };

  const [showPreview, setShowPreview] = useState(false);
  const [previewDocument, setPreviewDocument] = useState<Document | null>(null);

  const handlePreview = (document: Document) => {
    // Validate document data before opening preview
    if (!document.id || (!document.file_url || !document.file_url.trim())) {
      console.error('Invalid document data for preview:', document);
      setError('Cannot preview document: Missing document ID or file URL');
      return;
    }

    setPreviewDocument(document);
    setShowPreview(true);
  };

  const handleDownload = async (document: Document) => {
    try {
      console.log('Starting download for document:', document.id, document.name);

      // Use the API service with proper blob handling
      const response = await api.get(`/documents/${document.id}/file`, {
        responseType: 'blob',
        headers: {
          'Authorization': `Bearer ${await auth.currentUser?.getIdToken()}`
        }
      });

      // Create blob URL and trigger download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = document.name || `document_${document.id}`;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log('Download completed successfully');
    } catch (err: any) {
      console.error('Download failed:', err);
      setError(err.response?.data?.error || err.message || 'Failed to download document');
    }
  };

  if (user?.role !== "administrator") {
    return <p className="text-red-500">Access denied. Only administrators can manage documents.</p>;
  }

  return (
    <div className="container py-6 px-4">
      {/* Breadcrumbs */}
      <Breadcrumbs
        customerId={customerId}
        customerName={customer?.name}
        customTitle="Documents"
      />

      {/* Header with back button */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-3xl font-bold text-amspm-text mb-2 md:mb-0">
          Customer Documents
        </h1>
        <div className="flex space-x-2">
          <Link to={`/customers/${customerId}`} className="btn btn-outline">
            <FaArrowLeft className="mr-2" /> Back to Customer
          </Link>
        </div>
      </div>

      {error && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{error}</div>}

      {validationErrors.length > 0 && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <ul className="list-disc pl-5">
            {validationErrors.map((err, index) => (
              <li key={index}>{err}</li>
            ))}
          </ul>
        </div>
      )}

      {initialLoading ? (
        <LoadingSpinner message="Loading documents..." fullScreen={false} />
      ) : (
        <div className="container py-6 px-4">
          {/* Tabs for navigation */}
          <div className="mb-6">
            <div className={`flex border-b border-gray-200 ${isMobile ? 'overflow-x-auto mobile-scroll-container' : ''}`}>
              <button
                className={`py-2 px-4 font-medium text-sm mobile-touch-target ${isMobile ? 'flex-shrink-0 whitespace-nowrap' : ''} ${activeTab === 'active' ? 'border-b-2 border-amspm-primary text-amspm-primary' : 'text-gray-500 hover:text-amspm-primary'}`}
                onClick={() => {
                  setActiveTab('active');
                  // Reset upload state when switching tabs
                  if (activeTab === 'upload') {
                    setShowTemplateUpload(false);
                    setDocumentType("");
                  }
                }}
              >
                <FaFileAlt className="inline mr-2" /> {isMobile ? 'Active' : 'Active Documents'}
              </button>
              <button
                className={`py-2 px-4 font-medium text-sm mobile-touch-target ${isMobile ? 'flex-shrink-0 whitespace-nowrap' : ''} ${activeTab === 'history' ? 'border-b-2 border-amspm-primary text-amspm-primary' : 'text-gray-500 hover:text-amspm-primary'}`}
                onClick={() => {
                  setActiveTab('history');
                  // Reset upload state when switching tabs
                  if (activeTab === 'upload') {
                    setShowTemplateUpload(false);
                    setDocumentType("");
                  }
                }}
              >
                <FaHistory className="inline mr-2" /> {isMobile ? 'History' : 'Document History'}
              </button>
              <button
                className={`py-2 px-4 font-medium text-sm mobile-touch-target ${isMobile ? 'flex-shrink-0 whitespace-nowrap' : ''} ${activeTab === 'upload' ? 'border-b-2 border-amspm-primary text-amspm-primary' : 'text-gray-500 hover:text-amspm-primary'}`}
                onClick={() => setActiveTab('upload')}
              >
                <FaPlus className="inline mr-2" /> {isMobile ? 'Upload' : 'Upload New Document'}
              </button>
            </div>
          </div>

          {/* Active Documents Section */}
          {activeTab === 'active' && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-amspm-text">
                  Active Documents
                </h2>
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => {
                    setActiveTab('upload');
                    setShowTemplateUpload(false);
                    setDocumentType("");
                  }}
                >
                  <FaPlus className="mr-2" /> Add Document
                </button>
              </div>

              {Object.keys(groupAndSortDocuments(documents).active).length === 0 ? (
                <div className="bg-white dark:bg-dark-card rounded-lg shadow p-8 text-center">
                  <div className="mb-4">
                    <FaFileAlt className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
                    <p className="text-gray-600 dark:text-gray-400 mb-4">Geen actieve documenten gevonden voor deze klant.</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500 mb-6">
                      Begin met het uploaden van uw eerste document om een overzicht te krijgen.
                    </p>
                  </div>
                  <button
                    className="btn btn-primary"
                    onClick={() => {
                      setActiveTab('upload');
                      setShowTemplateUpload(false);
                      setDocumentType("");
                    }}
                  >
                    <FaPlus className="mr-2" /> Voeg Uw Eerste Document Toe
                  </button>
                </div>
              ) : (
                <div className="space-y-6">
                  {Object.entries(groupAndSortDocuments(documents).active).map(([categoryKey, categoryData]) => (
                    <DocumentCategorySection
                      key={categoryKey}
                      categoryKey={categoryKey}
                      category={categoryData.category}
                      documents={categoryData.documents}
                      onDelete={handleDelete}
                      onPreview={handlePreview}
                      onDownload={handleDownload}
                      submitting={submitting}
                    />
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Document History Section */}
          {activeTab === 'history' && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-amspm-text mb-4">
                Document History
              </h2>
              {Object.keys(groupAndSortDocuments(documents).inactive).length === 0 ? (
                <div className="bg-white dark:bg-dark-card rounded-lg shadow p-8 text-center">
                  <div className="mb-4">
                    <FaHistory className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
                    <p className="text-gray-600 dark:text-gray-400">Geen documentgeschiedenis gevonden voor deze klant.</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                      Inactieve en vervangen documenten verschijnen hier.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {Object.entries(groupAndSortDocuments(documents).inactive).map(([categoryKey, categoryData]) => (
                    <DocumentCategorySection
                      key={categoryKey}
                      categoryKey={categoryKey}
                      category={categoryData.category}
                      documents={categoryData.documents}
                      onDelete={handleDelete}
                      onPreview={handlePreview}
                      onDownload={handleDownload}
                      submitting={submitting}
                    />
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Rest of the component (upload form, modals, etc.) */}
          {/* ... */}
        </div>
      )}
      {/* Removed editingDocument modal */}
          {/* Upload New Document Section */}
          {activeTab === 'upload' && (
            <div className="mb-8">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-amspm-text mb-4">Upload New Document</h2>
                <p className="text-gray-600 mb-4">Select a document type and follow the instructions to upload a new document for this customer.</p>

                <div className="form-group mb-4">
                  <label className="block text-amspm-text font-medium mb-2">Document Type</label>
                  <select
                    value={documentType}
                    onChange={handleDocumentTypeChange}
                    className="input w-full"
                    disabled={submitting}
                  >
                    <option value="">Select Document Type</option>
                    {ADMIN_DOCUMENT_TYPES.map((type) => (
                      <option key={type} value={type}>
                        {type.replace(/_/g, " ")}
                      </option>
                    ))}
                  </select>
                  <p className="text-sm text-gray-500 mt-1">Choose the type of document you want to upload</p>
                </div>

                {documentType === "beveiligingscertificaat" && !currentStep && (
                  <div className="mt-6 border-t pt-4">
                    <h3 className="text-lg font-medium text-amspm-text mb-2">Beveiligingscertificaat Process</h3>
                    <p className="text-gray-600 mb-4">This will start a guided process to create a beveiligingscertificaat with all required sub-documents.</p>

                    <button
                      onClick={startBeveiligingscertificaatProcess}
                      className="btn btn-primary w-full mt-2"
                      disabled={submitting}
                    >
                      Start Beveiligingscertificaat Process
                    </button>
                  </div>
                )}

                {documentType && !currentStep && (
                  <div className="mt-6 border-t pt-4">
                    <h3 className="text-lg font-medium text-amspm-text mb-2">Upload Options</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div
                        className={`border rounded-lg p-4 transition-colors cursor-pointer ${
                          !showTemplateUpload
                            ? 'border-amspm-primary bg-amspm-light dark:bg-dark-secondary'
                            : 'border-gray-200 hover:border-amspm-primary'
                        }`}
                        onClick={() => setShowTemplateUpload(false)}
                      >
                        <div className="flex items-center mb-2">
                          <FaFileAlt className={`${!showTemplateUpload ? 'text-amspm-primary' : 'text-gray-500'} mr-2`} size={20} />
                          <h4 className="font-medium">Standard Upload</h4>
                        </div>
                        <p className="text-sm text-gray-600">Upload a file directly from your device.</p>
                      </div>
                      <div
                        className={`border rounded-lg p-4 transition-colors cursor-pointer ${
                          showTemplateUpload
                            ? 'border-amspm-primary bg-amspm-light dark:bg-dark-secondary'
                            : 'border-gray-200 hover:border-amspm-primary'
                        }`}
                        onClick={() => setShowTemplateUpload(true)}
                      >
                        <div className="flex items-center mb-2">
                          <FaFileWord className={`${showTemplateUpload ? 'text-amspm-primary' : 'text-gray-500'} mr-2`} size={20} />
                          <h4 className="font-medium">Use Template</h4>
                        </div>
                        <p className="text-sm text-gray-600">Select a template, fill it in, and upload.</p>
                      </div>
                    </div>
                  </div>
                )}

                {!documentType && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                    <p className="text-blue-700 text-sm">
                      <strong>Tip:</strong> Select a document type above to see available templates or upload options.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {documentType && !currentStep && activeTab === 'upload' ? (
            <div className="mt-4">
              {showTemplateUpload ? (
                <DocumentUploadWithTemplate
                  customerId={parseInt(customerId!)}
                  documentType={documentType}
                  onSuccess={() => {
                    // Refresh documents
                    getDocumentsByCustomer(parseInt(customerId!)).then(response => {
                      setDocuments(response || []);
                      setDocumentType("");
                      setShowTemplateUpload(false);
                      setActiveTab('active'); // Switch to active documents after successful upload
                    });
                  }}
                  onCancel={() => {
                    setShowTemplateUpload(false);
                  }}
                />
              ) : (
                <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 space-y-4">
                  <h2 className="text-xl font-semibold text-amspm-text mb-4">Standard Upload</h2>
                  <div className="form-group">
                    <label className="block text-amspm-text font-medium mb-1 uppercase">Upload File</label>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="input"
                      disabled={submitting || documentNotApplicable}
                    />
                    <div className="mt-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={documentNotApplicable}
                          onChange={(e) => {
                            setDocumentNotApplicable(e.target.checked);
                            if (e.target.checked) setFile(null);
                          }}
                          disabled={submitting}
                          className="form-checkbox"
                        />
                        <span className="ml-2 text-amspm-text">Niet van toepassing</span>
                      </label>
                    </div>
                  </div>
                  <div className="form-group">
                    <label className="block text-amspm-text font-medium mb-1 uppercase">Expiry Type</label>
                    <select
                      value={expiryType}
                      onChange={(e) => {
                        setExpiryType(e.target.value as "date" | "niet_van_toepassing");
                        if (e.target.value === "niet_van_toepassing") {
                          setExpiryDate("");
                        } else if (e.target.value === "date" && documentType === 'onderhoudsbon') {
                          // Auto-set 12-month expiration for onderhoudsbon
                          setExpiryDate(getTwelveMonthExpiryDate());
                        }
                      }}
                      className="input"
                      disabled={submitting || documentNotApplicable}
                    >
                      <option value="date">Date</option>
                      <option value="niet_van_toepassing">Niet van toepassing</option>
                    </select>
                  </div>
                  {expiryType === "date" && !documentNotApplicable && (
                    <div className="form-group">
                      <label className="block text-amspm-text font-medium mb-1 uppercase">Expiry Date</label>
                      <input
                        type="datetime-local"
                        value={expiryDate}
                        onChange={(e) => setExpiryDate(e.target.value)}
                        className="input"
                        disabled={submitting}
                      />
                      {documentType === 'onderhoudsbon' && (
                        <p className="text-xs text-blue-600 mt-1">
                          Automatically set to 12 months from today for onderhoudsbon documents
                        </p>
                      )}
                    </div>
                  )}
                  <div className="form-group">
                    <div className="mb-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={useVersionStatus}
                          onChange={(e) => setUseVersionStatus(e.target.checked)}
                          disabled={submitting}
                          className="form-checkbox"
                        />
                        <span className="ml-2 text-amspm-text font-medium uppercase">Use Version Status</span>
                      </label>
                    </div>
                    {useVersionStatus && (
                      <select
                        value={versionStatus}
                        onChange={(e) => setVersionStatus(e.target.value as "active" | "inactive")}
                        className="input"
                        disabled={submitting}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    )}
                  </div>
                  <div className="flex space-x-4">
                    <button
                      onClick={() => {
                        handleUpload().then(() => {
                          // Switch to active documents after successful upload
                          if (!error) setActiveTab('active');
                        });
                      }}
                      className="btn btn-primary flex-1"
                      disabled={submitting || (!file && !documentNotApplicable)}
                    >
                      {submitting ? "Submitting..." : documentNotApplicable ? "Mark as Not Applicable" : "Upload Document"}
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : currentStep ? (
            <div className="fixed inset-0 modal-overlay">
              <div className="modal-content">
                <h3 className="text-xl font-medium text-amspm-text mb-4">Step: {currentStep.replace(/_/g, " ")}</h3>
                <div className="space-y-4">
                  <div className="form-group">
                    <label className="block text-amspm-text font-medium mb-1 uppercase">
                      Upload {currentStep.replace(/_/g, " ")}
                    </label>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="input"
                      disabled={submitting || documentNotApplicable}
                    />
                    <div className="mt-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={documentNotApplicable}
                          onChange={(e) => {
                            setDocumentNotApplicable(e.target.checked);
                            if (e.target.checked) setFile(null);
                          }}
                          disabled={submitting}
                          className="form-checkbox"
                        />
                        <span className="ml-2 text-amspm-text">Niet van toepassing</span>
                      </label>
                    </div>
                  </div>
                  <div className="form-group">
                    <label className="block text-amspm-text font-medium mb-1 uppercase">Expiry Type</label>
                    <select
                      value={expiryType}
                      onChange={(e) => {
                        setExpiryType(e.target.value as "date" | "niet_van_toepassing");
                        if (e.target.value === "niet_van_toepassing") {
                          setExpiryDate("");
                        } else if (e.target.value === "date" && documentType === 'onderhoudsbon') {
                          // Auto-set 12-month expiration for onderhoudsbon
                          setExpiryDate(getTwelveMonthExpiryDate());
                        }
                      }}
                      className="input"
                      disabled={submitting || documentNotApplicable}
                    >
                      <option value="date">Date</option>
                      <option value="niet_van_toepassing">Niet van toepassing</option>
                    </select>
                  </div>
                  {expiryType === "date" && !documentNotApplicable && (
                    <div className="form-group">
                      <label className="block text-amspm-text font-medium mb-1 uppercase">Expiry Date</label>
                      <input
                        type="datetime-local"
                        value={expiryDate}
                        onChange={(e) => setExpiryDate(e.target.value)}
                        className="input"
                        disabled={submitting}
                      />
                    </div>
                  )}
                  <div className="form-group">
                    <div className="mb-2">
                      <label className="inline-flex items-center">
                        <input
                          type="checkbox"
                          checked={useVersionStatus}
                          onChange={(e) => setUseVersionStatus(e.target.checked)}
                          disabled={submitting}
                          className="form-checkbox"
                        />
                        <span className="ml-2 text-amspm-text font-medium uppercase">Use Version Status</span>
                      </label>
                    </div>
                    {useVersionStatus && (
                      <select
                        value={versionStatus}
                        onChange={(e) => setVersionStatus(e.target.value as "active" | "inactive")}
                        className="input"
                        disabled={submitting}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    )}
                  </div>
                  <div className="flex space-x-4">
                    <button
                      onClick={() => handleUpload(false, true)}
                      className="btn btn-primary flex-1"
                      disabled={submitting || (!file && !documentNotApplicable)}
                    >
                      {submitting ? "Submitting..." : documentNotApplicable ? "Skip Step (Not Applicable)" : "Next Step"}
                    </button>
                  </div>
                  <button
                    onClick={() => {
                      setCurrentStep(null);
                      setParentDocumentId(null);
                      setDocumentType("");
                      setFile(null);
                      setExpiryDate("");
                      setDocumentNotApplicable(false);
                      setExpiryType("date");
                      setUseVersionStatus(true);
                      setVersionStatus("active");
                    }}
                    className="btn btn-danger w-full"
                    disabled={submitting}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          ) : null}

    </div>
  );
};

// Create a new DocumentCard component for better code organization
interface DocumentCardProps {
  document: Document;
  onDelete: (id: number) => void;
  submitting: boolean;
}

const DocumentCard: React.FC<DocumentCardProps> = ({ document, onDelete, submitting }) => {
  const [showPreview, setShowPreview] = useState(false);
  const [previewDocument, setPreviewDocument] = useState<Document | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "red":
        return "text-red-600";
      case "orange":
        return "text-orange-600";
      case "green":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  const handlePreview = (doc?: Document) => {
    const targetDocument = doc || document;
    // Validate document data before opening preview
    if (!targetDocument.id || (!targetDocument.file_url || !targetDocument.file_url.trim())) {
      console.error('Invalid document data for preview:', targetDocument);
      alert('Cannot preview document: Missing document ID or file URL');
      return;
    }

    setPreviewDocument(targetDocument);
    setShowPreview(true);
  };

  const handleDownload = async (doc?: Document) => {
    const targetDocument = doc || document;
    try {
      console.log('Starting download for document:', targetDocument.id, targetDocument.name);

      // Use the API service with proper blob handling
      const response = await api.get(`/documents/${targetDocument.id}/file`, {
        responseType: 'blob',
      });

      // Create a blob URL and trigger download
      const blob = new Blob([response.data], {
        type: response.headers['content-type'] || 'application/octet-stream'
      });

      const url = URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = targetDocument.name || `document_${targetDocument.id}.pdf`;

      // Add to DOM, click, and remove
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);

      // Clean up the blob URL
      URL.revokeObjectURL(url);

      console.log('Download completed successfully');
    } catch (error: any) {
      console.error('Error downloading document:', error);
      alert(`Failed to download document: ${error.message}`);
    }
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setPreviewDocument(null);
  };

  return (
    <div className="card">
      <div className="card-content">
        <p className="text-amspm-text dark:text-dark-text">
          <span className="font-medium">Type:</span> {document.document_type}
        </p>
        <p className="text-amspm-text dark:text-dark-text">
          <span className="font-medium">Filename:</span> {document.name}
        </p>
        <p className="text-amspm-text dark:text-dark-text">
          <span className="font-medium">Version Status:</span>{" "}
          <span className={`${
            document.status === "active" ? "text-green-600" :
            document.status === "not_applicable" ? "text-blue-600" : "text-gray-600"
          }`}>
            {document.status === "active" ? "Active" :
             document.status === "not_applicable" ? "No Version Status" : "Inactive"}
          </span>
        </p>
        {(document.status === "active" || document.status === "not_applicable") && (
          <>
            <p className="text-amspm-text dark:text-dark-text">
              <span className="font-medium">Expiry Status:</span>{" "}
              <span className={getStatusColor(document.expiry_status)}>
                {document.expiry_status}
              </span>
            </p>
            <p className="text-amspm-text dark:text-dark-text">
              <span className="font-medium">Expiry Date:</span>{" "}
              {document.expiry_date ? new Date(document.expiry_date).toLocaleString() : "N/A"}
            </p>
          </>
        )}
        <p className="text-amspm-text dark:text-dark-text">
          <span className="font-medium">Upload Date:</span>{" "}
          {formatDate(document.created_at)}
        </p>

        {/* Sub-documents section */}
        {document.document_type === "beveiligingscertificaat" && document.sub_documents.length > 0 && (
          <div className="mt-4">
            <h4 className="text-lg font-semibold text-amspm-text mb-2">Sub-Documents:</h4>
            {document.sub_documents.map((subDoc) => (
              <div key={subDoc.id} className="border-l-4 border-amspm-primary pl-4 mb-2">
                <div className="bg-gray-50 p-3 rounded">
                  <div className="flex justify-between items-start mb-2">
                    <h5 className="font-medium text-amspm-text capitalize">
                      {subDoc.document_type.replace(/_/g, " ")}
                    </h5>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      subDoc.expiry_status === "green" ? "bg-green-100 text-green-800" :
                      subDoc.expiry_status === "orange" ? "bg-orange-100 text-orange-800" :
                      subDoc.expiry_status === "red" ? "bg-red-100 text-red-800" :
                      "bg-gray-100 text-gray-800"
                    }`}>
                      {subDoc.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">
                    <span className="font-medium">Upload Date:</span>{" "}
                    {formatDate(subDoc.created_at)}
                  </p>
                  {subDoc.expiry_date && (
                    <p className="text-sm text-gray-600 mb-2">
                      <span className="font-medium">Expiry Date:</span>{" "}
                      {new Date(subDoc.expiry_date).toLocaleString()}
                    </p>
                  )}
                  <div className="flex space-x-2 mt-2">
                    <button
                      onClick={() => handlePreview(subDoc)}
                      className="btn btn-sm btn-primary"
                      title="Preview document"
                    >
                      Preview
                    </button>
                    <button
                      onClick={() => onDelete(subDoc.id)}
                      className="btn btn-sm btn-danger"
                      disabled={submitting}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="grid grid-cols-3 gap-2 mt-4">
          <button
            onClick={() => handlePreview(document)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
            disabled={submitting}
            title="Preview document"
          >
            Preview
          </button>
          <button
            onClick={() => handleDownload()}
            className="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
            disabled={submitting}
            title="Download document"
          >
            Download
          </button>
          <button
            onClick={() => onDelete(document.id)}
            className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
            disabled={submitting}
          >
            Delete
          </button>
        </div>
      </div>

      {/* Document Preview Modal */}
      {showPreview && previewDocument && (
        <DocumentPreview
          documentUrl={previewDocument.file_url}
          documentId={previewDocument.id}
          documentName={previewDocument.name}
          onClose={handleClosePreview}
        />
      )}
    </div>
  );
};

export default CustomerDocuments;
