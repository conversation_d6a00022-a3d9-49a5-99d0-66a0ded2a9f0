import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { getCalendarEvents, createEvent, updateEvent, completeEvent, deleteEvent, getDashboardMetrics, DashboardMetrics } from '../services/eventService';
import { getAllUsers } from '../services/userService';
import { getAllCustomersNoPage } from '../services/customerService';
import { Event } from '../types/event';
import { User } from '../types/user';
import { Customer } from '../types/customer';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import interactionPlugin from '@fullcalendar/interaction';
import LoadingSpinner from '../components/LoadingSpinner';
import EventModal from '../components/EventModal';
import EventDetailsModal from '../components/EventDetailsModal';
import MetricsCard from '../components/dashboard/MetricsCard';

import {
  FaCalendarAlt,
  FaFilter,
  FaPlus,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle,
  FaUsers,
  FaChartBar,
  FaEye,
  FaList,
  FaTh,
  FaCalendarWeek,
  FaSearch,
  FaTimes,
  FaBuilding,
  FaUserTie,
  FaCalendarDay,
  FaCalendarCheck,
  FaArrowLeft,
  FaArrowRight,
  FaUser,
  FaFileAlt
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import { formatDateTimeForBackend, getCurrentDateTimeForInput } from '../utils/dateUtils';
import { useConfirmation } from '../context/ConfirmationContext';
import { useLocation, useNavigate } from 'react-router-dom';
import { useMobile } from '../hooks/useMobile';

const Calendar: React.FC = () => {
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const { isMobile } = useMobile();
  const location = useLocation();
  const navigate = useNavigate();
  const calendarRef = useRef<FullCalendar>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showEventDetails, setShowEventDetails] = useState(false);
  const [newEvent, setNewEvent] = useState<{
    customer_id: number | null;
    customer_name?: string | null;
    customer_address?: string | null;
    event_type: string | null;
    description: string;
    scheduled_date: string;
    user_ids: number[];
    user_id: number | null;
  }>({
    customer_id: null,
    customer_name: undefined,
    customer_address: undefined,
    event_type: '',
    description: '',
    scheduled_date: '',
    user_ids: [],
    user_id: null
  });
  const [selectedUserId, setSelectedUserId] = useState<number | null>(
    user?.role !== 'administrator' ? (user?.id || null) : null
  );
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Enhanced calendar features
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [calendarView, setCalendarView] = useState('dayGridMonth');
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [isMobileView, setIsMobileView] = useState(false);
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(new Date());

  // Auto-detect mobile view and force list view on mobile
  useEffect(() => {
    const handleResize = () => {
      const isMobileDevice = isMobile || window.innerWidth < 768;
      setIsMobileView(isMobileDevice);
      
      // Force list view on mobile devices
      if (isMobileDevice && calendarRef.current) {
        const calendarApi = calendarRef.current.getApi();
        if (calendarApi) {
          calendarApi.changeView('listWeek');
        }
      }
    };

    // Initial check
    handleResize();
    
    // Add resize listener
    window.addEventListener('resize', handleResize);
    
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobile]);

  // Initialize current week start (Monday of current week)
  useEffect(() => {
    const now = new Date();
    const monday = new Date(now);
    const dayOfWeek = now.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Sunday = 0, so Monday = 1
    monday.setDate(now.getDate() - daysToMonday);
    monday.setHours(0, 0, 0, 0);
    setCurrentWeekStart(monday);
  }, []);

  // Get customerId from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const customerIdParam = urlParams.get('customerId');
    if (customerIdParam) {
      setSelectedCustomerId(parseInt(customerIdParam));
    }
  }, [location.search]);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [usersResponse, customersResponse, metricsResponse] = await Promise.all([
          getAllUsers(),
          getAllCustomersNoPage(),
          getDashboardMetrics()
        ]);

          setUsers(usersResponse.users);
        setCustomers(customersResponse.customers);
            setMetrics(metricsResponse);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.response?.data?.error || 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch calendar events
  const getCalendarEventsData = async () => {
    try {
      const eventsData = await getCalendarEvents(
        undefined, // startDate
        undefined, // endDate
        selectedUserId !== null ? selectedUserId : undefined,
        selectedCustomerId !== null ? selectedCustomerId : undefined
      );
      setEvents(eventsData.events);
    } catch (err: any) {
      console.error('Error fetching calendar events:', err);
      setError(err.response?.data?.error || 'Failed to fetch calendar events');
    }
  };

  useEffect(() => {
    getCalendarEventsData();
  }, [selectedUserId, selectedCustomerId]);

  // Calendar event handlers
  const handleDateSelect = (selectInfo: any) => {
    setNewEvent({
      ...newEvent,
      scheduled_date: formatDateTimeForBackend(selectInfo.start)
    });
    setShowModal(true);
  };

  const handleEventClick = (clickInfo: any) => {
    const event = events.find(e => e.id === parseInt(clickInfo.event.id));
    if (event) {
      setSelectedEvent(event);
      setShowEventDetails(true);
    }
  };

  const handleEventDrop = async (dropInfo: any) => {
    const event = events.find(e => e.id === parseInt(dropInfo.event.id));
    if (!event) return;

    try {
      const updatedEvent = await updateEvent(
        event.id,
        event.customer_id || 0,
        event.event_type || '',
        event.description,
        formatDateTimeForBackend(dropInfo.event.start),
        event.user_ids
      );
      
      setEvents(events.map(e => e.id === event.id ? updatedEvent : e));
      toast.success('Event updated successfully');
    } catch (err: any) {
      console.error('Error updating event:', err);
      toast.error(err.response?.data?.error || 'Failed to update event');
      getCalendarEventsData(); // Refresh to revert changes
    }
  };

  const handleEventResize = async (resizeInfo: any) => {
    const event = events.find(e => e.id === parseInt(resizeInfo.event.id));
    if (!event) return;

    try {
      const updatedEvent = await updateEvent(
        event.id,
        event.customer_id || 0,
        event.event_type || '',
        event.description,
        formatDateTimeForBackend(resizeInfo.event.start),
        event.user_ids
      );
      
      setEvents(events.map(e => e.id === event.id ? updatedEvent : e));
      toast.success('Event updated successfully');
    } catch (err: any) {
      console.error('Error updating event:', err);
      toast.error(err.response?.data?.error || 'Failed to update event');
      getCalendarEventsData(); // Refresh to revert changes
    }
  };

  const handleCreateEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newEvent.event_type || !newEvent.description || !newEvent.scheduled_date) {
      toast.error('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    try {
      const event = await createEvent(
        newEvent.customer_id,
        newEvent.event_type,
        newEvent.description,
        newEvent.scheduled_date,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : undefined
      );
      setEvents([...events, event]);
      setShowModal(false);
      setNewEvent({
        customer_id: null,
        customer_name: undefined,
        customer_address: undefined,
        event_type: '',
        description: '',
        scheduled_date: '',
        user_ids: [],
        user_id: null
      });
      toast.success('Event created successfully');
    } catch (err: any) {
      console.error('Error creating event:', err);
      toast.error(err.response?.data?.error || 'Failed to create event');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingEvent) return;

    setSubmitting(true);
    try {
      const updatedEvent = await updateEvent(
        editingEvent.id,
        newEvent.customer_id || 0,
        newEvent.event_type || '',
        newEvent.description,
        newEvent.scheduled_date,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : undefined
      );
      setEvents(events.map(e => e.id === editingEvent.id ? updatedEvent : e));
      setShowModal(false);
      setEditingEvent(null);
      setNewEvent({
        customer_id: null,
        customer_name: undefined,
        customer_address: undefined,
        event_type: '',
        description: '',
        scheduled_date: '',
        user_ids: [],
        user_id: null
      });
      toast.success('Event updated successfully');
    } catch (err: any) {
      console.error('Error updating event:', err);
      toast.error(err.response?.data?.error || 'Failed to update event');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteEvent = async () => {
    if (!selectedEvent) return;

    showConfirmation({
      title: 'Delete Event',
      message: `Are you sure you want to delete the event "${selectedEvent.description}"?`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      confirmButtonClass: 'bg-red-600 hover:bg-red-700',
      onConfirm: async () => {
        try {
          await deleteEvent(selectedEvent.id);
          setEvents(events.filter(e => e.id !== selectedEvent.id));
          setShowEventDetails(false);
          setSelectedEvent(null);
          toast.success('Event deleted successfully');
        } catch (err: any) {
          console.error('Error deleting event:', err);
          toast.error(err.response?.data?.error || 'Failed to delete event');
        }
      }
    });
  };

  const handleCompleteEvent = async () => {
    if (!selectedEvent) return;

    try {
      await completeEvent(selectedEvent.id);
      setEvents(events.map(e => e.id === selectedEvent.id ? { ...e, status: 'completed' } : e));
      setShowEventDetails(false);
      setSelectedEvent(null);
      toast.success('Event completed successfully');
    } catch (err: any) {
      console.error('Error completing event:', err);
      toast.error(err.response?.data?.error || 'Failed to complete event');
    }
  };

  // Filter events based on search and filters
  const filteredEvents = events.filter(event => {
    const matchesSearch = !searchTerm || 
      event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (event.event_type && event.event_type.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = !eventTypeFilter || event.event_type === eventTypeFilter;
    const matchesStatus = !statusFilter || event.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // Group events by date for list view
  const groupEventsByDate = (events: Event[]) => {
    const grouped: { [key: string]: Event[] } = {};
    
    events.forEach(event => {
      const date = new Date(event.scheduled_date);
      const dateKey = date.toISOString().split('T')[0];
      
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(event);
    });
    
    // Sort events within each date by time
    Object.keys(grouped).forEach(dateKey => {
      grouped[dateKey].sort((a, b) => {
        const timeA = new Date(a.scheduled_date).getTime();
        const timeB = new Date(b.scheduled_date).getTime();
        return timeA - timeB;
      });
    });
    
    return grouped;
  };

  // Get events for current week
  const getCurrentWeekEvents = (events: Event[]) => {
    const weekStart = new Date(currentWeekStart);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    
    return events.filter(event => {
      const eventDate = new Date(event.scheduled_date);
      return eventDate >= weekStart && eventDate <= weekEnd;
    });
  };

  // Navigate to previous week
  const goToPreviousWeek = () => {
    const newWeekStart = new Date(currentWeekStart);
    newWeekStart.setDate(newWeekStart.getDate() - 7);
    setCurrentWeekStart(newWeekStart);
  };

  // Navigate to next week
  const goToNextWeek = () => {
    const newWeekStart = new Date(currentWeekStart);
    newWeekStart.setDate(newWeekStart.getDate() + 7);
    setCurrentWeekStart(newWeekStart);
  };

  // Navigate to current week
  const goToCurrentWeek = () => {
    const now = new Date();
    const monday = new Date(now);
    const dayOfWeek = now.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    monday.setDate(now.getDate() - daysToMonday);
    monday.setHours(0, 0, 0, 0);
    setCurrentWeekStart(monday);
  };

  // Format time for display
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  // Format week range for display
  const formatWeekRange = () => {
    const weekStart = new Date(currentWeekStart);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    
    const startMonth = weekStart.toLocaleDateString('en-US', { month: 'short' });
    const endMonth = weekEnd.toLocaleDateString('en-US', { month: 'short' });
    const startDay = weekStart.getDate();
    const endDay = weekEnd.getDate();
    
    if (startMonth === endMonth) {
      return `${startMonth} ${startDay} - ${endDay}`;
    } else {
      return `${startMonth} ${startDay} - ${endMonth} ${endDay}`;
    }
  };

  // Get event type display name
  const getEventTypeDisplay = (eventType: string | null) => {
    if (!eventType) return 'General';
    
    switch (eventType) {
      case 'offerte': return 'Offerte';
      case 'werkbon': return 'Werkbon';
      case 'onderhoudsbon': return 'Onderhoudsbon';
      case 'factuur': return 'Factuur';
      default: return eventType.replace(/_/g, ' ');
    }
  };

  // Get status color and text
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          text: 'Pending',
          bgColor: 'bg-orange-100',
          textColor: 'text-orange-700',
          icon: '⏳'
        };
      case 'completed':
        return {
          text: 'Completed',
          bgColor: 'bg-green-100',
          textColor: 'text-green-700',
          icon: '✅'
        };
      case 'cancelled':
        return {
          text: 'Cancelled',
          bgColor: 'bg-red-100',
          textColor: 'text-red-700',
          icon: '❌'
        };
      default:
        return {
          text: 'Unknown',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-700',
          icon: '❓'
        };
    }
  };

  // Calendar options based on view
  const getCalendarOptions = () => {
    const baseOptions = {
      plugins: [dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin],
      headerToolbar: {
        left: 'prev,next Week',
        center: 'title',
        right: isMobileView ? 'listWeek' : 'dayGridMonth,timeGridWeek,listWeek'
      },
      initialView: isMobileView ? 'listWeek' : 'dayGridMonth',
      editable: true,
      selectable: true,
      selectMirror: true,
      dayMaxEvents: isMobileView ? 2 : 6, // Show more events but with better layout
      weekends: true,
      events: filteredEvents.map(event => ({
        id: event.id.toString(),
        title: event.description, // Simplified title to prevent overlapping
        start: event.scheduled_date,
        end: event.scheduled_date,
        backgroundColor: getEventColor(event.event_type || '', event.status),
        borderColor: getEventColor(event.event_type || '', event.status),
        textColor: '#ffffff',
        extendedProps: {
          event_type: event.event_type,
          status: event.status,
          customer_name: event.customer_name,
          customer_address: event.customer_address,
          description: event.description,
          user_ids: event.user_ids
        }
      })),
      // Enhanced event rendering for better readability
      eventContent: (arg: any) => {
        const event = arg.event;
        const extendedProps = event.extendedProps;
        const isMonthView = arg.view.type === 'dayGridMonth';
        const isWeekView = arg.view.type === 'timeGridWeek';
        const isListView = arg.view.type === 'listWeek';
        
        // For month view, show compact information
        if (isMonthView) {
          return (
            <div className="event-content-month p-1 text-xs">
              <div className="font-semibold text-white mb-1 leading-tight text-xs drop-shadow-sm truncate">
                {extendedProps.description.length > 30 
                  ? extendedProps.description.substring(0, 30) + '...' 
                  : extendedProps.description}
              </div>
              <div className="text-white mb-1 text-xs drop-shadow-sm truncate">
                👤 {extendedProps.customer_name || 'No Customer'}
              </div>
              <div className="text-white text-xs drop-shadow-sm truncate">
                📋 {extendedProps.event_type.replace(/_/g, ' ')}
              </div>
              {extendedProps.status === 'pending' && (
                <div className="text-xs px-1 py-0.5 rounded-full mt-1 inline-block font-medium bg-orange-600 text-white shadow-sm">
                  ⏳ Pending
                </div>
              )}
              {extendedProps.status === 'completed' && (
                <div className="text-xs px-1 py-0.5 rounded-full mt-1 inline-block font-medium bg-green-600 text-white shadow-sm">
                  ✅ Done
                </div>
              )}
            </div>
          );
        }
        
        // For week view, use same clean card style as month view
        if (isWeekView) {
          return (
            <div className="event-content-week p-2 text-xs bg-white/10 backdrop-blur-sm rounded-md border border-white/20 shadow-sm">
              <div className="font-semibold text-white mb-1 leading-tight text-sm drop-shadow-sm">
                {extendedProps.description.length > 40 
                  ? extendedProps.description.substring(0, 40) + '...' 
                  : extendedProps.description}
              </div>
              <div className="text-white mb-1 text-xs drop-shadow-sm truncate">
                👤 {extendedProps.customer_name || 'No Customer'}
              </div>
              <div className="text-white text-xs drop-shadow-sm truncate">
                📋 {extendedProps.event_type.replace(/_/g, ' ')}
              </div>
              {extendedProps.status === 'pending' && (
                <div className="text-xs px-1 py-0.5 rounded-full mt-1 inline-block font-medium bg-orange-600 text-white shadow-sm">
                  ⏳ Pending
                </div>
              )}
              {extendedProps.status === 'completed' && (
                <div className="text-xs px-1 py-0.5 rounded-full mt-1 inline-block font-medium bg-green-600 text-white shadow-sm">
                  ✅ Done
                </div>
              )}
            </div>
          );
        }
        
        // For list view, show clean information that matches iOS calendar design
        if (isListView) {
          return (
            <div className="event-content-list">
              <div className="flex flex-col">
                <div className="font-semibold text-gray-900 text-sm mb-1 leading-tight">
                  {extendedProps.description}
                </div>
                <div className="text-gray-600 text-xs mb-1">
                  {extendedProps.customer_name || 'No Customer'}
                </div>
                <div className="text-gray-500 text-xs capitalize">
                  {extendedProps.event_type.replace(/_/g, ' ')}
                </div>
                {extendedProps.status === 'pending' && (
                  <div className="text-xs px-2 py-0.5 rounded-full font-medium bg-orange-100 text-orange-700 mt-1 inline-block">
                    Pending
                  </div>
                )}
                {extendedProps.status === 'completed' && (
                  <div className="text-xs px-2 py-0.5 rounded-full font-medium bg-green-100 text-green-700 mt-1 inline-block">
                    Done
                  </div>
                )}
              </div>
            </div>
          );
        }
        
        return null;
      },
      select: handleDateSelect,
      eventClick: handleEventClick,
      eventDrop: handleEventDrop,
      eventResize: handleEventResize,
      height: isMobileView ? 'auto' : 700,
      aspectRatio: isMobileView ? 1.0 : 1.35,
      contentHeight: isMobileView ? 'auto' : undefined,
      // Enhanced view configurations
      views: {
        dayGridMonth: {
          dayMaxEvents: 6,
          dayMaxEventRows: 6,
          moreLinkClick: 'popover' as const,
          moreLinkContent: (args: any) => {
            return `+${args.num} more`;
          }
        },
        timeGridWeek: {
          dayHeaderFormat: { weekday: 'long' as const, month: 'short' as const, day: 'numeric' as const },
          slotMinTime: '07:00:00',
          slotMaxTime: '20:00:00',
          height: 'auto',
          slotDuration: '00:30:00',
          slotLabelFormat: {
            hour: '2-digit' as const,
            minute: '2-digit' as const,
            hour12: false
          }
        },
        listWeek: {
          listDayFormat: { weekday: 'long' as const, month: 'short' as const, day: 'numeric' as const },
          listDaySideFormat: false,
          noEventsMessage: 'No events scheduled for this week',
          height: 'auto',
          eventDisplay: 'block' as const,
          listDayContent: (args: any) => {
            return {
              html: `<div class="fc-list-day-cushion fc-cell-shaded">${args.text}</div>`
            };
          }
        }
      },
      // Better event display settings
      eventDisplay: 'block',
      eventTimeFormat: {
        hour: '2-digit' as const,
        minute: '2-digit' as const,
        hour12: false
      },
      // Improved day grid settings
      dayGrid: {
        dayMaxEvents: 6,
        dayMaxEventRows: 6
      }
    };

    return baseOptions;
  };

  const getEventColor = (eventType: string, status: string) => {
    if (status === 'completed') return '#10B981';
    if (status === 'cancelled') return '#EF4444';
    
    // If no event type (general event), use gray
    if (!eventType || eventType === '') return '#6B7280';
    
    switch (eventType) {
      case 'offerte': return '#3B82F6'; // Blue
      case 'werkbon': return '#F59E0B'; // Amber
      case 'onderhoudsbon': return '#8B5CF6'; // Purple
      case 'factuur': return '#EF4444'; // Red
      default: return '#6B7280'; // Gray
    }
  };

  // Custom List View Component
  const CustomListView = () => {
    const weekEvents = getCurrentWeekEvents(filteredEvents);
    const groupedEvents = groupEventsByDate(weekEvents);
    const sortedDates = Object.keys(groupedEvents).sort();
    
    if (sortedDates.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg">No events scheduled for this week</div>
          <div className="text-gray-300 text-sm mt-2">Try navigating to a different week or create new events</div>
        </div>
      );
    }

    return (
      <div className="space-y-0">
        {sortedDates.map((dateKey) => {
          const date = new Date(dateKey);
          const dayEvents = groupedEvents[dateKey];
          
          return (
            <div key={dateKey} className="mb-6">
              {/* Day Header */}
              <div className="bg-gray-100 px-4 py-3 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {formatDate(dateKey)}
                  </h3>
                  <span className="text-sm text-gray-500 font-medium">
                    {date.toLocaleDateString('en-US', { weekday: 'short' })}
                  </span>
                </div>
              </div>
              
              {/* Events for this day */}
              <div className="space-y-3 px-4 py-3">
                {dayEvents.map((event) => {
                  const statusDisplay = getStatusDisplay(event.status);
                  
                  return (
                    <div 
                      key={event.id} 
                      className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
                      onClick={() => {
                        setSelectedEvent(event);
                        setShowEventDetails(true);
                      }}
                    >
                      <div className="p-4">
                        {/* Time and Status Row */}
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                            <span className="text-lg font-semibold text-gray-900">
                              {formatTime(event.scheduled_date)}
                            </span>
                          </div>
                          <div className={`px-3 py-1 rounded-full text-xs font-medium ${statusDisplay.bgColor} ${statusDisplay.textColor}`}>
                            {statusDisplay.icon} {statusDisplay.text}
                          </div>
                        </div>
                        
                        {/* Event Description */}
                        <div className="mb-3">
                          <h4 className="text-base font-semibold text-gray-900 leading-tight">
                            {event.description}
                          </h4>
                        </div>
                        
                        {/* Customer and Document Type */}
                        <div className="space-y-2">
                          <div className="flex items-center text-sm text-gray-600">
                            <FaUser className="mr-2 text-gray-400" />
                            <span className="font-medium">
                              {event.customer_name || 'No Customer'}
                            </span>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <FaFileAlt className="mr-2 text-gray-400" />
                            <span className="font-medium">
                              {getEventTypeDisplay(event.event_type)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  if (loading) {
    return <LoadingSpinner message="Loading calendar..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        {/* Professional Header */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-4 sm:px-6 py-6 sm:py-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-12 w-12 sm:h-16 sm:w-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <FaCalendarAlt className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                    </div>
                  </div>
                  <div className="ml-4 sm:ml-6">
                    <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">
                      Calendar Management
                    </h1>
                    <div className="mt-2 flex flex-wrap items-center text-xs sm:text-sm text-gray-500">
                      <FaBuilding className="mr-2" />
                      <span className="font-medium">{events.length} events</span>
                      {metrics && (
                        <>
                          <span className="mx-2 hidden sm:inline">•</span>
                          <span className="text-green-600 font-medium ml-2 sm:ml-0">{metrics.events.completed} completed</span>
                          <span className="mx-2 hidden sm:inline">•</span>
                          <span className="text-blue-600 font-medium ml-2 sm:ml-0">{metrics.events.pending} pending</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row gap-2 sm:gap-3">
                {!isMobileView && (
                  <button
                    onClick={() => setShowAnalytics(!showAnalytics)}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                  >
                    <FaChartBar className="mr-2 h-4 w-4" />
                    {showAnalytics ? 'Hide Analytics' : 'Show Analytics'}
                  </button>
                )}
                <button
                  onClick={() => {
                    setNewEvent({
                      customer_id: null,
                      customer_name: undefined,
                      customer_address: undefined,
                      event_type: '',
                      description: '',
                      scheduled_date: getCurrentDateTimeForInput(),
                      user_ids: [],
                      user_id: null
                    });
                    setEditingEvent(null);
                    setShowModal(true);
                  }}
                  className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Create Event
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Section */}
        {showAnalytics && metrics && (
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-6">
            <div className="px-6 py-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Event Analytics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <FaCalendarDay className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-blue-600">Total Events</p>
                      <p className="text-2xl font-bold text-blue-900">{metrics.events.total}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <FaCheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-green-600">Completed</p>
                      <p className="text-2xl font-bold text-green-900">{metrics.events.completed}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 p-4 rounded-lg border border-yellow-200">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <FaClock className="h-8 w-8 text-yellow-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-yellow-600">Pending</p>
                      <p className="text-2xl font-bold text-yellow-900">{metrics.events.pending}</p>
                    </div>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-lg border border-purple-200">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <FaUserTie className="h-8 w-8 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-purple-600">Active Users</p>
                      <p className="text-2xl font-bold text-purple-900">{metrics.users.total}</p>
                    </div>
                  </div>
                </div>
              </div>
          </div>
        </div>
      )}

        {/* Filters and Search */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-6">
          <div className="px-4 sm:px-6 py-4">
            <div className="space-y-4">
              {/* Search */}
              <div className="w-full">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaSearch className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search events..."
                    className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm('')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <FaTimes className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    </button>
                  )}
                </div>
              </div>

              {/* Filters */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
                <select
                  value={eventTypeFilter}
                  onChange={(e) => setEventTypeFilter(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Event Types</option>
                  <option value="offerte">Offerte</option>
                  <option value="werkbon">Werkbon</option>
                  <option value="onderhoudsbon">Onderhoudsbon</option>
                  <option value="factuur">Factuur</option>
                </select>

                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>

                {user?.role === 'administrator' && (
                  <select
                    value={selectedUserId || ''}
                    onChange={(e) => setSelectedUserId(e.target.value ? parseInt(e.target.value) : null)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All Users</option>
                    {users.map(user => (
                      <option key={user.id} value={user.id}>{user.name}</option>
                    ))}
                  </select>
                )}

                <select
                  value={selectedCustomerId || ''}
                  onChange={(e) => setSelectedCustomerId(e.target.value ? parseInt(e.target.value) : null)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Customers</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>{customer.name}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Calendar View Toggle */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-6">
          <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <h2 className="text-base sm:text-lg font-semibold text-gray-900">
                Calendar View
              </h2>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCalendarView('dayGridMonth')}
                  className={`px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                    calendarView === 'dayGridMonth'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <FaTh className="inline mr-2" />
                  Month
                </button>
                <button
                  onClick={() => setCalendarView('timeGridWeek')}
                  className={`px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                    calendarView === 'timeGridWeek'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <FaCalendarWeek className="inline mr-2" />
                  Week
                </button>
                <button
                  onClick={() => setCalendarView('customList')}
                  className={`px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                    calendarView === 'customList'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <FaList className="inline mr-2" />
                  List
                </button>
              </div>
            </div>
          </div>

          {/* Week Navigation for List View */}
          {calendarView === 'customList' && (
            <div className="px-4 sm:px-6 py-3 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={goToPreviousWeek}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200"
                  >
                    <FaArrowLeft className="h-4 w-4" />
                  </button>
                  <button
                    onClick={goToCurrentWeek}
                    className="px-3 py-1 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors duration-200"
                  >
                    Today
                  </button>
                  <button
                    onClick={goToNextWeek}
                    className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200"
                  >
                    <FaArrowRight className="h-4 w-4" />
                  </button>
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {formatWeekRange()}
                </div>
              </div>
            </div>
          )}

          <div className="p-1 sm:p-3 lg:p-4">
            {calendarView === 'customList' ? (
              <CustomListView />
            ) : (
              <FullCalendar
                ref={calendarRef}
                {...getCalendarOptions()}
                initialView={calendarView}
                headerToolbar={{
                  left: 'prev,next today',
                  center: 'title',
                  right: ''
                }}
              />
            )}
          </div>
        </div>

        {/* Modals */}
      {showModal && (
        <EventModal
          event={newEvent}
          onClose={() => {
            setShowModal(false);
            setEditingEvent(null);
              setNewEvent({
                customer_id: null,
                customer_name: undefined,
                customer_address: undefined,
                event_type: '',
                description: '',
                scheduled_date: '',
                user_ids: [],
                user_id: null
              });
          }}
          onSubmit={editingEvent ? handleUpdateEvent : handleCreateEvent}
          setEvent={setNewEvent}
          isEditing={!!editingEvent}
          submitting={submitting}
            users={users}
          customers={customers}
        />
      )}

      {showEventDetails && selectedEvent && (
        <EventDetailsModal
          event={selectedEvent}
          onClose={() => {
            setShowEventDetails(false);
            setSelectedEvent(null);
          }}
            onEdit={() => {
              setEditingEvent(selectedEvent);
                          setNewEvent({
              customer_id: selectedEvent.customer_id,
              customer_name: selectedEvent.customer_name,
              customer_address: selectedEvent.customer_address,
              event_type: selectedEvent.event_type || '',
              description: selectedEvent.description,
              scheduled_date: selectedEvent.scheduled_date,
              user_ids: selectedEvent.user_ids || [],
              user_id: selectedEvent.user_id
            });
              setShowEventDetails(false);
              setShowModal(true);
            }}
            onComplete={handleCompleteEvent}
            onDelete={handleDeleteEvent}
            canDelete={user?.role === 'administrator'}
                  />
      )}


      </div>
    </div>
  );
};

export default Calendar;
