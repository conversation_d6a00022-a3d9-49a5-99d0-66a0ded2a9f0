/**
 * Date utility functions for handling timezone conversions
 */

/**
 * Converts a datetime-local input value to a format that preserves local time
 * when sent to the backend. This prevents the 2-hour timezone offset issue.
 * 
 * @param datetimeLocalValue - Value from datetime-local input (YYYY-MM-DDTHH:MM)
 * @returns ISO string that represents the local time without timezone conversion
 */
export const formatDateTimeForBackend = (datetimeLocalValue: string): string => {
  if (!datetimeLocalValue) return '';
  
  // Create a date object from the datetime-local value
  // This treats the input as local time
  const localDate = new Date(datetimeLocalValue);
  
  // Get the timezone offset in minutes
  const timezoneOffset = localDate.getTimezoneOffset();
  
  // Adjust for timezone offset to get the actual local time
  const adjustedDate = new Date(localDate.getTime() - (timezoneOffset * 60000));
  
  // Return ISO string which will be in UTC but represents the local time
  return adjustedDate.toISOString();
};

/**
 * Converts a backend datetime string to a format suitable for datetime-local input
 * 
 * @param backendDateTime - ISO datetime string from backend
 * @returns String in YYYY-MM-DDTHH:MM format for datetime-local input
 */
export const formatDateTimeForInput = (backendDateTime: string): string => {
  if (!backendDateTime) return '';
  
  const date = new Date(backendDateTime);
  
  // Format for datetime-local input (YYYY-MM-DDTHH:MM)
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

/**
 * Gets current date and time in format suitable for datetime-local input
 * 
 * @returns String in YYYY-MM-DDTHH:MM format
 */
export const getCurrentDateTimeForInput = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

export const getTwelveMonthExpiryDate = (): string => {
  const now = new Date();
  const expiryDate = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate(), now.getHours(), now.getMinutes());
  
  const year = expiryDate.getFullYear();
  const month = String(expiryDate.getMonth() + 1).padStart(2, '0');
  const day = String(expiryDate.getDate()).padStart(2, '0');
  const hours = String(expiryDate.getHours()).padStart(2, '0');
  const minutes = String(expiryDate.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};
