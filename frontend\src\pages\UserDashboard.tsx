import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { getMyEvents, completeEvent } from "../services/eventService";
import { createDocument } from "../services/documentService";
import TemplateService from "../services/templateService";
import { Event } from "../types/event";
import { Document } from "../types/document";
import LoadingSpinner from "../components/LoadingSpinner";
import { useAuth } from "../context/AuthContext";
import HandleEventModal from "../components/HandleEventModal";
import { 
  FaUser, 
  FaCalendarAlt, 
  FaCheckCircle, 
  FaClock, 
  FaExclamationTriangle, 
  FaBuilding, 
  FaFileAlt, 
  FaArrowRight,
  FaClipboardList,
  FaTasks,
  FaUserCheck,
  FaCalendarCheck,
  FaExclamation,
  FaCheckDouble
} from "react-icons/fa";

const UserDashboard: React.FC = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [newExpiryDate, setNewExpiryDate] = useState("");
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [expiryType, setExpiryType] = useState<"date" | "niet_van_toepassing">("date");
  const [documentNotApplicable, setDocumentNotApplicable] = useState(false);
  const [useVersionStatus, setUseVersionStatus] = useState<boolean>(true);
  const [versionStatus, setVersionStatus] = useState<"active" | "inactive">("active");

  // All users can handle all event types - no document type restrictions
  const canHandleEvent = (eventType: string): boolean => {
    // All authenticated users can handle events assigned to them
    return true;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const eventsResponse = await getMyEvents();
        setEvents(eventsResponse?.events || []);
        setLoading(false);
      } catch (err: any) {
        const errorMessage = err.response?.data?.error || err.message || "Failed to fetch data. Please try again.";
        setError(errorMessage);
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleCompleteWithFile = async (event: Event) => {
    // All users can handle events assigned to them

    // Validation checks
    if (!documentNotApplicable && !file) {
      setError("Please select a file or mark document as 'Niet van toepassing'.");
      return;
    }

    if (expiryType === "date" && !newExpiryDate && !documentNotApplicable) {
      setError("Please set a new expiry date or mark validity as 'Niet van toepassing'.");
      return;
    }

    // Removed customer_id check to allow completing events without a customer

    setSubmitting(true);

    try {
      // Step 1: Create document if needed and if customer_id exists
      if (!documentNotApplicable && event.customer_id !== null) {
        const formattedExpiryDate = expiryType === "date" ? newExpiryDate : undefined;

        await createDocument(
          event.customer_id,
          event.id,
          file,
          event.event_type || "vrije_documenten", // Use vrije_documenten as fallback for events without type
          expiryType,
          formattedExpiryDate,
          undefined,
          false,
          documentNotApplicable,
          useVersionStatus,
          versionStatus
        );
      }

      // Step 2: Complete the event
      await completeEvent(event.id);

      // Step 3: Reset state and refresh events
      setFile(null);
      setNewExpiryDate("");
      setExpiryType("date");
      setDocumentNotApplicable(false);
      setUseVersionStatus(true);
      setVersionStatus("active");
      setSelectedEvent(null);
      setError(null);

      // Refresh the events list
      try {
        const response = await getMyEvents();
        if (response && response.events) {
          console.log("Events refreshed after completion:", response.events.length);
          setEvents(response.events);
        } else {
          console.error("Invalid response from getMyEvents:", response);
        }
      } catch (refreshErr) {
        console.error("Error refreshing events:", refreshErr);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || "Failed to complete event with file. Please try again.";
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCompleteWithTemplate = async (event: Event, templateBlob: Blob, fileName: string) => {
    setSubmitting(true);

    try {
      // Step 1: Save the filled template as a document if customer exists
      if (event.customer_id !== null) {
        // Convert blob to file for the document service
        const file = new File([templateBlob], fileName, {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });

        await createDocument(
          event.customer_id,
          event.id,
          file,
          event.event_type || "vrije_documenten", // Use vrije_documenten as fallback for events without type
          'niet_van_toepassing', // Templates typically don't have expiry dates
          undefined,
          undefined,
          false,
          false, // Document is not "not applicable" since we're creating it
          true,
          'active'
        );
      }

      // Step 2: Complete the event
      await completeEvent(event.id);

      // Step 3: Reset state and refresh events
      setFile(null);
      setNewExpiryDate("");
      setExpiryType("date");
      setDocumentNotApplicable(false);
      setUseVersionStatus(true);
      setVersionStatus("active");
      setSelectedEvent(null);
      setError(null);

      // Refresh the events list
      try {
        const response = await getMyEvents();
        if (response && response.events) {
          console.log("Events refreshed after template completion:", response.events.length);
          setEvents(response.events);
        } else {
          console.error("Invalid response from getMyEvents:", response);
        }
      } catch (refreshErr) {
        console.error("Error refreshing events:", refreshErr);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || "Failed to complete event with template. Please try again.";
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // The backend already filters events by user, so we don't need to filter by user_id here
  // Just filter by status since getMyEvents() already returns only events assigned to the current user
  const pendingEvents = events.filter((event) => event.status === "pending");
  const completedEvents = events.filter((event) => event.status === "completed");

  // Log for debugging
  console.log("Total events from backend:", events.length);
  console.log("Pending events:", pendingEvents.length);
  console.log("Completed events:", completedEvents.length);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "red":
        return "text-red-500";
      case "orange":
        return "text-orange-500";
      case "green":
        return "text-green-500";
      default:
        return "text-gray-500";
    }
  };

  const getStatusBgColor = (status: string) => {
    switch (status) {
      case "red":
        return "bg-red-100 text-red-800";
      case "orange":
        return "bg-orange-100 text-orange-800";
      case "green":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const renderEventCards = (events: Event[], title: string, icon: React.ReactNode, color: string) => {
    // Log the events being rendered for debugging
    console.log(`Rendering ${title}:`, events.length, events.map(e => ({id: e.id, user_id: e.user_id})));

    if (events.length === 0) {
      return (
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              {icon}
              <span className="ml-2">{title}</span>
            </h2>
          </div>
          <div className="p-8 text-center">
            <div className="text-gray-400 mb-4">
              {icon}
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
            <p className="text-gray-500">There are no {title.toLowerCase()} at the moment.</p>
          </div>
        </div>
      );
    }

    return (
      <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            {icon}
            <span className="ml-2">{title}</span>
            <span className="ml-auto bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
              {events.length}
            </span>
          </h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {events.map((event) => (
              <div
                key={event.id}
                className={`bg-white border rounded-lg p-6 hover:shadow-md transition-shadow duration-200 ${
                  event.document?.expiry_status === "red"
                    ? "border-red-300 bg-red-50"
                    : event.document?.expiry_status === "orange"
                    ? "border-orange-300 bg-orange-50"
                    : "border-green-300 bg-green-50"
                }`}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {event.event_type}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3">
                      {event.description}
                    </p>
                  </div>
                  <div className="ml-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      event.status === "completed" ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"
                    }`}>
                      {event.status === "completed" ? "Completed" : "Pending"}
                    </span>
                  </div>
                </div>

                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <FaBuilding className="mr-2 text-gray-400" />
                    <span className="font-medium">Customer:</span>
                    <span className="ml-2">{event.customer_name || 'No customer'}</span>
                  </div>
                  
                  {event.customer_address && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FaBuilding className="mr-2 text-gray-400" />
                      <span className="font-medium">Address:</span>
                      <span className="ml-2">{event.customer_address}</span>
                    </div>
                  )}

                  <div className="flex items-center text-sm text-gray-600">
                    <FaCalendarAlt className="mr-2 text-gray-400" />
                    <span className="font-medium">Scheduled:</span>
                    <span className="ml-2">
                      {new Date(event.scheduled_date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>

                  {event.completed_at && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FaCheckCircle className="mr-2 text-gray-400" />
                      <span className="font-medium">Completed:</span>
                      <span className="ml-2">
                        {new Date(event.completed_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  )}

                  {event.status === "completed" && event.completed_by_name && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FaUserCheck className="mr-2 text-gray-400" />
                      <span className="font-medium">Completed by:</span>
                      <span className="ml-2">{event.completed_by_name}</span>
                    </div>
                  )}

                  {event.document && (
                    <div className="flex items-center text-sm">
                      <FaFileAlt className="mr-2 text-gray-400" />
                      <span className="font-medium text-gray-600">Document Status:</span>
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusBgColor(event.document.expiry_status)}`}>
                        {event.document.expiry_status === "red" ? "Expired" :
                         event.document.expiry_status === "orange" ? "Expiring Soon" : "Valid"}
                      </span>
                    </div>
                  )}
                </div>

                {event.status === "pending" && (
                  <div className="flex justify-end">
                    <button
                      onClick={() => setSelectedEvent(event)}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                      disabled={submitting}
                    >
                      <FaClipboardList className="mr-2 h-4 w-4" />
                      Handle Event
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return <LoadingSpinner message="Loading dashboard..." />;
  }

  // Group events by customer for the "Go to Customer Documents" button
  const getCustomersWithPendingEvents = () => {
    // Since getMyEvents() already filters by current user, we don't need to filter again
    // Just filter out events without customers
    const pendingEventsWithCustomers = pendingEvents.filter(event => event.customer_id !== null);
    const customerMap = new Map<number, Event[]>();

    pendingEventsWithCustomers.forEach(event => {
      if (event.customer_id) {
        if (!customerMap.has(event.customer_id)) {
          customerMap.set(event.customer_id, []);
        }
        customerMap.get(event.customer_id)?.push(event);
      }
    });

    return customerMap;
  };

  // Render the customer documents section if user has pending events with customers
  const renderCustomerDocumentsSection = () => {
    const customersWithPendingEvents = getCustomersWithPendingEvents();

    if (customersWithPendingEvents.size === 0) {
      return null;
    }

    return (
      <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <FaFileAlt className="mr-2 h-5 w-5 text-blue-600" />
            Customer Documents
          </h2>
        </div>
        <div className="p-6">
          <p className="text-gray-600 mb-6">You have pending events for the following customers. Click to view their documents:</p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from(customersWithPendingEvents.entries()).map(([customerId, customerEvents]) => (
              <Link
                key={customerId}
                to={`/customer/${customerId}/documents`}
                className="group relative bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:scale-105"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-blue-900 group-hover:text-blue-700 transition-colors">
                      {customerEvents[0].customer_name || `Customer ID: ${customerId}`}
                    </h3>
                    <p className="text-sm text-blue-600 mt-1">
                      {customerEvents.length} pending event{customerEvents.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <div className="ml-4">
                    <div className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                      {customerEvents.length}
                    </div>
                  </div>
                </div>
                <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <FaArrowRight className="text-blue-400" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Header */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-16 w-16 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                      <FaUser className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="ml-6">
                    <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                      User Dashboard
                    </h1>
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <FaUserCheck className="mr-2" />
                      <span className="font-medium">Welcome, {user?.name || user?.email}</span>
                      <span className="mx-2">•</span>
                      <span>Your Events Overview</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-6 lg:mt-0 lg:ml-6">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="text-2xl font-bold text-blue-900">{pendingEvents.length}</div>
                    <div className="text-xs text-blue-600">Pending</div>
                  </div>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="text-2xl font-bold text-green-900">{completedEvents.length}</div>
                    <div className="text-xs text-green-600">Completed</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Customer Documents Section */}
        {renderCustomerDocumentsSection()}

        {/* Event Cards */}
        {renderEventCards(pendingEvents, "Pending Events", <FaClock className="h-5 w-5 text-orange-500" />, "orange")}
        {renderEventCards(completedEvents, "Completed Events", <FaCheckDouble className="h-5 w-5 text-green-500" />, "green")}

        {/* Event Modal */}
        {selectedEvent && (
          <HandleEventModal
            event={selectedEvent}
            onClose={() => {
              setSelectedEvent(null);
              setFile(null);
              setNewExpiryDate("");
              setExpiryType("date");
              setDocumentNotApplicable(false);
              setUseVersionStatus(true);
              setVersionStatus("active");
            }}
            onCompleteWithFile={handleCompleteWithFile}
            onCompleteWithTemplate={handleCompleteWithTemplate}
            submitting={submitting}
            file={file}
            setFile={setFile}
            newExpiryDate={newExpiryDate}
            setNewExpiryDate={setNewExpiryDate}
            expiryType={expiryType}
            setExpiryType={setExpiryType}
            documentNotApplicable={documentNotApplicable}
            setDocumentNotApplicable={setDocumentNotApplicable}
            useVersionStatus={useVersionStatus}
            setUseVersionStatus={setUseVersionStatus}
            versionStatus={versionStatus}
            setVersionStatus={setVersionStatus}
          />
        )}
      </div>
    </div>
  );
};

export default UserDashboard;
