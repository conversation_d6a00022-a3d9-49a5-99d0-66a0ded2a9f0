from app import db
from datetime import datetime

VALID_EVENT_TYPES = [
    "offerte",
    "werkbon",
    "onderhoudsbon",
    "onderhoudscontract",
    "meldkamercontract",
    "beveiligingscertificaat",
    "intakedocument",
    "projectietekening",
    "beveiligingsplan",
    "kabeltekeningen",
    "checklist oplevering installatie",
    "vrije_documenten",
    "factuur"
]

# Association table for many-to-many relationship between events and users
event_users = db.Table('event_users',
    db.Column('event_id', db.Integer, db.<PERSON>ey('events.id'), primary_key=True),
    db.<PERSON>umn('user_id', db.Integer, db.<PERSON>ey('users.id'), primary_key=True),
    db.Column('created_at', db.DateTime, default=datetime.utcnow)
)

class Event(db.Model):
    __tablename__ = "events"
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.<PERSON>umn(db.Integer, db.<PERSON>ey("customers.id"), nullable=True)  # Changed to nullable=True
    # Removed user_id field - now using many-to-many relationship
    document_id = db.Column(db.Integer, db.ForeignKey("documents.id"), nullable=True)
    event_type = db.Column(db.String(50), nullable=True)
    description = db.Column(db.Text, nullable=False)
    scheduled_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(20), default="pending")
    completed_at = db.Column(db.DateTime, nullable=True)
    completed_by = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Allow empty event_type for general events
        if self.event_type and self.event_type not in VALID_EVENT_TYPES:
            raise ValueError(f"Invalid event type. Must be one of {VALID_EVENT_TYPES}")

    customer = db.relationship("Customer", backref=db.backref("events", lazy="dynamic"))
    # Many-to-many relationship with users
    users = db.relationship("User", secondary=event_users, backref=db.backref("events", lazy="dynamic"))
    # Relationship to the user who completed the event
    completed_by_user = db.relationship("User", foreign_keys=[completed_by])
    # Remove the backref from Document relationship to avoid circular dependency
    document = db.relationship("Document", foreign_keys=[document_id])

    def to_dict(self):
        return {
            "id": self.id,
            "customer_id": self.customer_id,
            "customer_name": self.customer.name if self.customer else None,
            "customer_address": self.customer.address if self.customer else None,
            "user_ids": [user.id for user in self.users],
            "user_emails": [user.email for user in self.users],
            "user_names": [user.name or user.email for user in self.users],
            # Keep legacy fields for backward compatibility (will be removed later)
            "user_id": self.users[0].id if self.users else None,
            "user_email": self.users[0].email if self.users else None,
            "document_id": self.document_id,
            "event_type": self.event_type,
            "description": self.description,
            "scheduled_date": self.scheduled_date.isoformat(),
            "status": self.status,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "completed_by": self.completed_by,
            "completed_by_name": self.completed_by_user.name if self.completed_by_user else None,
            "completed_by_email": self.completed_by_user.email if self.completed_by_user else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
