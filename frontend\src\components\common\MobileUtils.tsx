import React from 'react';

// Mobile-friendly container component
interface MobileContainerProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
}

export const MobileContainer: React.FC<MobileContainerProps> = ({ 
  children, 
  className = '', 
  padding = 'md' 
}) => {
  const paddingClasses = {
    sm: 'p-2 sm:p-3',
    md: 'p-3 sm:p-4 md:p-6',
    lg: 'p-4 sm:p-6 md:p-8'
  };

  return (
    <div className={`mobile-container ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  );
};

// Mobile-friendly card component
interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
}

export const MobileCard: React.FC<MobileCardProps> = ({ 
  children, 
  className = '', 
  onClick,
  hoverable = false 
}) => {
  const baseClasses = 'mobile-card-enhanced bg-white dark:bg-dark-secondary border border-amspm-light-gray dark:border-dark-border';
  const hoverClasses = hoverable ? 'hover:shadow-md hover:border-amspm-primary dark:hover:border-dark-accent transition-all duration-200 cursor-pointer' : '';
  const clickClasses = onClick ? 'mobile-touch-target' : '';

  return (
    <div 
      className={`${baseClasses} ${hoverClasses} ${clickClasses} ${className}`}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

// Mobile-friendly button group
interface MobileButtonGroupProps {
  children: React.ReactNode;
  direction?: 'horizontal' | 'vertical' | 'responsive';
  className?: string;
}

export const MobileButtonGroup: React.FC<MobileButtonGroupProps> = ({ 
  children, 
  direction = 'responsive',
  className = '' 
}) => {
  const directionClasses = {
    horizontal: 'flex flex-row gap-2 sm:gap-3',
    vertical: 'flex flex-col gap-2',
    responsive: 'btn-group-mobile'
  };

  return (
    <div className={`${directionClasses[direction]} ${className}`}>
      {children}
    </div>
  );
};

// Mobile-friendly form group
interface MobileFormGroupProps {
  label: string;
  children: React.ReactNode;
  error?: string;
  required?: boolean;
  className?: string;
  helpText?: string;
}

export const MobileFormGroup: React.FC<MobileFormGroupProps> = ({
  label,
  children,
  error,
  required = false,
  className = '',
  helpText
}) => {
  return (
    <div className={`mobile-form-group ${className}`}>
      <label className="mobile-form-group label block text-sm font-medium text-amspm-text dark:text-dark-text-light mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {children}
      {helpText && !error && (
        <p className="text-xs text-gray-500 dark:text-dark-text-light mt-1">{helpText}</p>
      )}
      {error && (
        <p className="text-red-600 dark:text-red-400 text-sm mt-1">{error}</p>
      )}
    </div>
  );
};

// Mobile-friendly input component
interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

export const MobileInput: React.FC<MobileInputProps> = ({ 
  className = '', 
  error = false, 
  ...props 
}) => {
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : '';
  
  return (
    <input
      className={`mobile-form-input mobile-touch-target mobile-focus-visible ${errorClasses} ${className}`}
      {...props}
    />
  );
};

// Mobile-friendly select component
interface MobileSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean;
  options: { value: string; label: string }[];
  placeholder?: string;
}

export const MobileSelect: React.FC<MobileSelectProps> = ({ 
  className = '', 
  error = false,
  options,
  placeholder,
  ...props 
}) => {
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : '';
  
  return (
    <select
      className={`mobile-form-input mobile-touch-target mobile-focus-visible ${errorClasses} ${className}`}
      {...props}
    >
      {placeholder && <option value="">{placeholder}</option>}
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

// Mobile-friendly textarea component
interface MobileTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
}

export const MobileTextarea: React.FC<MobileTextareaProps> = ({ 
  className = '', 
  error = false, 
  ...props 
}) => {
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : '';
  
  return (
    <textarea
      className={`mobile-form-input mobile-touch-target mobile-focus-visible resize-none ${errorClasses} ${className}`}
      rows={4}
      {...props}
    />
  );
};

// Mobile-friendly search component
interface MobileSearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onClear?: () => void;
  className?: string;
}

export const MobileSearch: React.FC<MobileSearchProps> = ({ 
  value, 
  onChange, 
  placeholder = "Search...",
  onClear,
  className = '' 
}) => {
  return (
    <div className={`mobile-search-container ${className}`}>
      <div className="mobile-search-icon">
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="mobile-search-input mobile-touch-target mobile-focus-visible"
      />
      {value && onClear && (
        <button
          onClick={onClear}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
};

// Mobile-friendly loading component
interface MobileLoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const MobileLoading: React.FC<MobileLoadingProps> = ({ 
  message = "Loading...",
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className="mobile-loading">
      <div className={`mobile-loading-spinner ${sizeClasses[size]}`}></div>
      <p className="text-sm text-amspm-text-light dark:text-dark-text-light">{message}</p>
    </div>
  );
};

// Mobile-friendly message component
interface MobileMessageProps {
  type: 'error' | 'success' | 'warning' | 'info';
  message: string;
  onClose?: () => void;
}

export const MobileMessage: React.FC<MobileMessageProps> = ({ 
  type, 
  message, 
  onClose 
}) => {
  const typeClasses = {
    error: 'mobile-message-error',
    success: 'mobile-message-success',
    warning: 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-200 dark:border-yellow-800/30',
    info: 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-200 dark:border-blue-800/30'
  };

  return (
    <div className={`mobile-message ${typeClasses[type]} flex items-center justify-between`}>
      <span>{message}</span>
      {onClose && (
        <button
          onClick={onClose}
          className="ml-2 text-current opacity-70 hover:opacity-100 mobile-touch-target"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
};

// Mobile-friendly page header
interface MobilePageHeaderProps {
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
  backButton?: boolean;
  onBack?: () => void;
}

export const MobilePageHeader: React.FC<MobilePageHeaderProps> = ({
  title,
  subtitle,
  actions,
  backButton = false,
  onBack
}) => {
  return (
    <div className="mobile-header">
      <div className="flex items-center gap-3">
        {backButton && (
          <button
            onClick={onBack}
            className="mobile-touch-target p-2 rounded-lg hover:bg-amspm-light-gray dark:hover:bg-dark-hover"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        <div>
          <h1 className="mobile-header-title">{title}</h1>
          {subtitle && (
            <p className="text-sm text-amspm-text-light dark:text-dark-text-light mt-1">{subtitle}</p>
          )}
        </div>
      </div>
      {actions && (
        <div className="mobile-header-actions">
          {actions}
        </div>
      )}
    </div>
  );
};

// Mobile-responsive modal wrapper
interface MobileModalProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  className?: string;
}

export const MobileModal: React.FC<MobileModalProps> = ({
  children,
  isOpen,
  onClose,
  title,
  className = ''
}) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 mobile-modal-overlay flex items-center justify-center md:items-center md:justify-center"
      onClick={onClose}
      style={{ pointerEvents: 'auto' }}
    >
      <div
        className={`mobile-modal-content md:modal-content md:max-w-lg md:mx-auto md:my-8 md:relative md:bottom-auto md:left-auto md:right-auto md:rounded-lg ${className}`}
        onClick={(e) => e.stopPropagation()}
        style={{ pointerEvents: 'auto' }}
      >
        {title && (
          <div className="flex justify-between items-center mb-4 pb-3 border-b border-gray-200 dark:border-dark-border pt-6 md:pt-0">
            <h2 className="text-lg sm:text-xl font-semibold text-amspm-primary dark:text-dark-accent uppercase">
              {title}
            </h2>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xl transition-colors duration-200 mobile-touch-target flex items-center justify-center rounded hover:bg-gray-100 dark:hover:bg-gray-700"
              aria-label="Close"
            >
              ×
            </button>
          </div>
        )}
        {children}
      </div>
    </div>
  );
};

// Mobile form layout components
interface MobileFormLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const MobileFormLayout: React.FC<MobileFormLayoutProps> = ({
  children,
  className = ''
}) => {
  return (
    <div className={`mobile-form-grid ${className}`}>
      {children}
    </div>
  );
};

export const MobileFormRow: React.FC<MobileFormLayoutProps> = ({
  children,
  className = ''
}) => {
  return (
    <div className={`mobile-form-row sm:flex-row sm:gap-4 ${className}`}>
      {children}
    </div>
  );
};

// Mobile form actions
interface MobileFormActionsProps {
  children: React.ReactNode;
  className?: string;
  sticky?: boolean;
}

export const MobileFormActions: React.FC<MobileFormActionsProps> = ({
  children,
  className = '',
  sticky = false
}) => {
  const stickyClasses = sticky ? 'mobile-modal-content .form-actions' : 'mobile-form-actions';

  return (
    <div className={`${stickyClasses} ${className}`}>
      {children}
    </div>
  );
};

// Enhanced components for Offerte system

// Progress indicator component
interface ProgressStepProps {
  steps: Array<{
    id: string;
    label: string;
    status: 'completed' | 'current' | 'upcoming';
  }>;
  className?: string;
}

export const ProgressSteps: React.FC<ProgressStepProps> = ({ steps, className = '' }) => {
  return (
    <div className={`flex items-center justify-center space-x-2 sm:space-x-4 mb-6 ${className}`}>
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div className="flex items-center">
            <div className={`
              w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300
              ${step.status === 'completed'
                ? 'bg-green-500 text-white shadow-lg'
                : step.status === 'current'
                ? 'bg-blue-600 text-white shadow-lg ring-4 ring-blue-200'
                : 'bg-gray-300 text-gray-500'
              }
            `}>
              {step.status === 'completed' ? '✓' : index + 1}
            </div>
            <span className={`ml-2 text-xs sm:text-sm font-medium transition-colors duration-300 ${
              step.status === 'completed'
                ? 'text-green-600'
                : step.status === 'current'
                ? 'text-blue-600'
                : 'text-gray-500'
            }`}>
              {step.label}
            </span>
          </div>
          {index < steps.length - 1 && (
            <div className={`w-6 sm:w-8 h-0.5 transition-colors duration-300 ${
              steps[index + 1].status !== 'upcoming' ? 'bg-green-500' : 'bg-gray-300'
            }`}></div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

// Enhanced card for product selection
interface ProductCardProps {
  title: string;
  description: string;
  price?: string;
  monthlyPrice?: string;
  features: string[];
  icon?: React.ReactNode;
  popular?: boolean;
  onClick?: () => void;
  className?: string;
  gradient?: string;
  borderColor?: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  title,
  description,
  price,
  monthlyPrice,
  features,
  icon,
  popular = false,
  onClick,
  className = '',
  gradient = 'from-blue-50 to-indigo-50',
  borderColor = 'border-blue-500 hover:border-blue-600'
}) => {
  return (
    <div
      onClick={onClick}
      className={`
        relative border-2 rounded-xl p-4 sm:p-6 cursor-pointer transition-all duration-300
        ${borderColor}
        hover:shadow-xl transform hover:-translate-y-1
        bg-gradient-to-br ${gradient}
        ${popular ? 'ring-2 ring-yellow-400 ring-opacity-50' : ''}
        mobile-touch-target
        ${className}
      `}
    >
      {popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold shadow-md">
            POPULAIR
          </span>
        </div>
      )}

      <div className="text-center mb-4 sm:mb-6">
        {icon && (
          <div className="mb-3 sm:mb-4 flex justify-center">
            {icon}
          </div>
        )}
        <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">
          {title}
        </h3>
        <p className="text-gray-600 text-sm mb-4">
          {description}
        </p>
      </div>

      {/* Features list */}
      <div className="mb-4 sm:mb-6">
        <h4 className="font-semibold text-gray-800 mb-3 text-sm sm:text-base">Inclusief:</h4>
        <ul className="text-xs sm:text-sm text-gray-600 space-y-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3 flex-shrink-0"></span>
              {feature}
            </li>
          ))}
        </ul>
      </div>

      {/* Pricing */}
      {(price || monthlyPrice) && (
        <div className="border-t border-gray-200 pt-4 mb-4">
          {price && (
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">Installatiekosten:</span>
              <span className="font-bold text-base sm:text-lg text-gray-900">{price}</span>
            </div>
          )}
          {monthlyPrice && (
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Maandelijkse lease:</span>
              <span className="font-bold text-base sm:text-lg text-green-600">{monthlyPrice}</span>
            </div>
          )}
        </div>
      )}

      {/* Call to action */}
      <div className="text-center">
        <div className="bg-white bg-opacity-80 rounded-lg py-2 px-4 shadow-sm">
          <span className="text-sm font-medium text-gray-800">
            Klik om te selecteren →
          </span>
        </div>
      </div>
    </div>
  );
};

// Quantity selector component
interface QuantitySelectorProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  disabled?: boolean;
  className?: string;
}

export const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  value,
  onChange,
  min = 0,
  max = 99,
  disabled = false,
  className = ''
}) => {
  const handleDecrease = () => {
    if (value > min) {
      onChange(value - 1);
    }
  };

  const handleIncrease = () => {
    if (value < max) {
      onChange(value + 1);
    }
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <button
        onClick={handleDecrease}
        disabled={disabled || value <= min}
        className="w-10 h-10 rounded-full bg-red-100 text-red-600 flex items-center justify-center hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mobile-touch-target"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
        </svg>
      </button>
      <span className="text-lg font-semibold text-gray-900 min-w-[2rem] text-center">
        {value}
      </span>
      <button
        onClick={handleIncrease}
        disabled={disabled || value >= max}
        className="w-10 h-10 rounded-full bg-green-100 text-green-600 flex items-center justify-center hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mobile-touch-target"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      </button>
    </div>
  );
};

// Enhanced product item card
interface ProductItemCardProps {
  name: string;
  description: string;
  price: number;
  image?: string;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  disabled?: boolean;
  className?: string;
}

export const ProductItemCard: React.FC<ProductItemCardProps> = ({
  name,
  description,
  price,
  image,
  quantity,
  onQuantityChange,
  disabled = false,
  className = ''
}) => {
  return (
    <div className={`border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 bg-white ${className}`}>
      <div className="flex items-start space-x-4">
        {/* Product image */}
        <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-100 rounded-xl flex items-center justify-center overflow-hidden flex-shrink-0">
          {image ? (
            <img
              src={image}
              alt={name}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0NEgyMFYyMFoiIHN0cm9rZT0iIzlDQTNBRiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxjaXJjbGUgY3g9IjI4IiBjeT0iMjgiIHI9IjMiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDM2TDI4IDI4TDM2IDM2TDQ0IDI4VjQ0SDIwVjM2WiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
              }}
            />
          ) : (
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-gray-900 mb-1 text-sm sm:text-base">{name}</h4>
          <p className="text-xs sm:text-sm text-gray-600 mb-2 line-clamp-2">{description}</p>
          <p className="text-base sm:text-lg font-bold text-green-600">€{price.toFixed(2)}</p>
        </div>
      </div>

      {/* Quantity controls */}
      <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
        <span className="text-sm font-medium text-gray-700">Aantal:</span>
        <QuantitySelector
          value={quantity}
          onChange={onQuantityChange}
          disabled={disabled}
        />
      </div>
    </div>
  );
};

// Price summary component
interface PriceSummaryProps {
  items: Array<{
    label: string;
    value: string | number;
    highlight?: boolean;
    subtext?: string;
  }>;
  total?: {
    label: string;
    value: string | number;
    subtext?: string;
  };
  className?: string;
}

export const PriceSummary: React.FC<PriceSummaryProps> = ({
  items,
  total,
  className = ''
}) => {
  return (
    <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4 sm:p-6 ${className}`}>
      <h3 className="font-semibold text-blue-900 mb-4 flex items-center text-base sm:text-lg">
        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        Prijsoverzicht
      </h3>

      <div className="space-y-3">
        {items.map((item, index) => (
          <div key={index} className="flex justify-between items-center">
            <div>
              <span className={`text-sm ${item.highlight ? 'font-semibold text-blue-900' : 'text-blue-800'}`}>
                {item.label}
              </span>
              {item.subtext && (
                <p className="text-xs text-blue-600">{item.subtext}</p>
              )}
            </div>
            <span className={`font-bold ${item.highlight ? 'text-lg text-blue-900' : 'text-blue-900'}`}>
              {typeof item.value === 'number' ? `€${item.value.toFixed(2)}` : item.value}
            </span>
          </div>
        ))}

        {total && (
          <>
            <div className="border-t border-blue-300 pt-3 mt-3">
              <div className="flex justify-between items-center">
                <div>
                  <span className="text-base font-bold text-blue-900">{total.label}</span>
                  {total.subtext && (
                    <p className="text-sm text-blue-700">{total.subtext}</p>
                  )}
                </div>
                <span className="text-xl font-bold text-blue-900">
                  {typeof total.value === 'number' ? `€${total.value.toFixed(2)}` : total.value}
                </span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

// Enhanced info card component
interface InfoCardProps {
  title: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  variant?: 'info' | 'success' | 'warning' | 'error';
  className?: string;
}

export const InfoCard: React.FC<InfoCardProps> = ({
  title,
  children,
  icon,
  variant = 'info',
  className = ''
}) => {
  const variantClasses = {
    info: 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900',
    success: 'from-green-50 to-emerald-50 border-green-200 text-green-900',
    warning: 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900',
    error: 'from-red-50 to-pink-50 border-red-200 text-red-900'
  };

  return (
    <div className={`bg-gradient-to-r ${variantClasses[variant]} border rounded-xl p-4 sm:p-6 ${className}`}>
      <h4 className="font-semibold mb-3 flex items-center text-sm sm:text-base">
        {icon && <span className="mr-2">{icon}</span>}
        {title}
      </h4>
      <div className="text-sm">
        {children}
      </div>
    </div>
  );
};

// Enhanced button component
interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

export const EnhancedButton: React.FC<EnhancedButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  fullWidth = false,
  className = '',
  disabled,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 mobile-touch-target focus:outline-none focus:ring-2 focus:ring-offset-2';

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-md hover:shadow-lg',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 shadow-md hover:shadow-lg',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-md hover:shadow-lg',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-md hover:shadow-lg',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500'
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  };

  const widthClass = fullWidth ? 'w-full' : '';

  return (
    <button
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${widthClass}
        ${(disabled || loading) ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      ) : icon ? (
        <span className="mr-2">{icon}</span>
      ) : null}
      {children}
    </button>
  );
};

// Customer summary card
interface CustomerSummaryProps {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  systemType?: string;
  className?: string;
}

export const CustomerSummary: React.FC<CustomerSummaryProps> = ({
  name,
  email,
  phone,
  address,
  city,
  systemType,
  className = ''
}) => {
  return (
    <div className={`bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div>
            <h3 className="font-semibold text-blue-900 text-sm sm:text-base">{name}</h3>
            <p className="text-xs sm:text-sm text-blue-700">
              {address && city ? `${address}, ${city}` : email || phone || 'Geen contactgegevens'}
            </p>
          </div>
        </div>
        {systemType && (
          <div className="text-right">
            <p className="text-sm font-medium text-blue-900">{systemType}</p>
            <p className="text-xs text-blue-700">Gekozen systeem</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Enhanced loading states
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'white';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const colorClasses = {
    primary: 'border-blue-600 border-t-transparent',
    secondary: 'border-gray-600 border-t-transparent',
    white: 'border-white border-t-transparent'
  };

  return (
    <div className={`${sizeClasses[size]} border-2 border-solid rounded-full animate-spin ${colorClasses[color]} ${className}`}></div>
  );
};

// Enhanced loading overlay
interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  children: React.ReactNode;
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  message = 'Laden...',
  children,
  className = ''
}) => {
  return (
    <div className={`relative ${className}`}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 dark:bg-dark-primary dark:bg-opacity-75 flex items-center justify-center z-50 rounded-lg">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-3 text-sm font-medium text-gray-700 dark:text-dark-text">{message}</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Enhanced error boundary component
interface ErrorMessageProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  variant?: 'error' | 'warning' | 'info';
  className?: string;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = 'Er is een fout opgetreden',
  message,
  onRetry,
  onDismiss,
  variant = 'error',
  className = ''
}) => {
  const variantClasses = {
    error: 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200',
    info: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200'
  };

  const iconClasses = {
    error: 'text-red-500 dark:text-red-400',
    warning: 'text-yellow-500 dark:text-yellow-400',
    info: 'text-blue-500 dark:text-blue-400'
  };

  return (
    <div className={`border rounded-lg p-4 ${variantClasses[variant]} ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className={`w-5 h-5 ${iconClasses[variant]}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            {variant === 'error' && (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            )}
            {variant === 'warning' && (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            )}
            {variant === 'info' && (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            )}
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium">{title}</h3>
          <p className="mt-1 text-sm">{message}</p>
          {(onRetry || onDismiss) && (
            <div className="mt-3 flex gap-2">
              {onRetry && (
                <EnhancedButton
                  variant="outline"
                  size="sm"
                  onClick={onRetry}
                >
                  Opnieuw proberen
                </EnhancedButton>
              )}
              {onDismiss && (
                <EnhancedButton
                  variant="outline"
                  size="sm"
                  onClick={onDismiss}
                >
                  Sluiten
                </EnhancedButton>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Enhanced skeleton loader
interface SkeletonProps {
  width?: string;
  height?: string;
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = '1rem',
  className = '',
  variant = 'rectangular'
}) => {
  const variantClasses = {
    text: 'rounded',
    rectangular: 'rounded-md',
    circular: 'rounded-full'
  };

  return (
    <div
      className={`bg-gray-200 dark:bg-gray-700 animate-pulse offerte-loading-skeleton ${variantClasses[variant]} ${className}`}
      style={{ width, height }}
    />
  );
};

// Enhanced toast notification
interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onClose?: () => void;
  className?: string;
}

export const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  duration = 5000,
  onClose,
  className = ''
}) => {
  React.useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        onClose?.();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);

  const typeClasses = {
    success: 'bg-green-500 text-white',
    error: 'bg-red-500 text-white',
    warning: 'bg-yellow-500 text-white',
    info: 'bg-blue-500 text-white'
  };

  const icons = {
    success: '✓',
    error: '✕',
    warning: '⚠',
    info: 'ℹ'
  };

  return (
    <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg flex items-center gap-3 mobile-slide-up ${typeClasses[type]} ${className}`}>
      <span className="text-lg">{icons[type]}</span>
      <span className="text-sm font-medium">{message}</span>
      {onClose && (
        <button
          onClick={onClose}
          className="ml-2 text-white hover:text-gray-200 transition-colors mobile-touch-target"
        >
          ✕
        </button>
      )}
    </div>
  );
};

// Enhanced form validation hook
export const useFormValidation = (initialValues: Record<string, any>, validationRules: Record<string, (value: any) => string | null>) => {
  const [values, setValues] = React.useState(initialValues);
  const [errors, setErrors] = React.useState<Record<string, string>>({});
  const [touched, setTouched] = React.useState<Record<string, boolean>>({});

  const validateField = (name: string, value: any) => {
    const rule = validationRules[name];
    if (rule) {
      const error = rule(value);
      setErrors(prev => ({
        ...prev,
        [name]: error || ''
      }));
      return !error;
    }
    return true;
  };

  const handleChange = (name: string, value: any) => {
    setValues(prev => ({
      ...prev,
      [name]: value
    }));

    if (touched[name]) {
      validateField(name, value);
    }
  };

  const handleBlur = (name: string) => {
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
    validateField(name, values[name]);
  };

  const validateAll = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    Object.keys(validationRules).forEach(name => {
      const error = validationRules[name](values[name]);
      if (error) {
        newErrors[name] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    setTouched(Object.keys(validationRules).reduce((acc, key) => ({ ...acc, [key]: true }), {}));

    return isValid;
  };

  const reset = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  };

  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    validateAll,
    reset,
    isValid: Object.keys(errors).length === 0 || Object.values(errors).every(error => !error)
  };
};
