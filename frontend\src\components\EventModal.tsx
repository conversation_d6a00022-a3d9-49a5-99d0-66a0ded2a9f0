import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Customer } from '../types/customer';
import { User } from '../types/user';
import { searchCustomers, getAllCustomersNoPage } from '../services/customerService';
import { eventSchema, validateData } from '../utils/validation';
import { useConfirmation } from '../context/ConfirmationContext';
import { formatDateTimeForInput } from '../utils/dateUtils';
import { 
  FaCalendarAlt, 
  FaUser, 
  FaUsers, 
  FaBuilding, 
  FaClock, 
  FaCheckCircle, 
  FaEdit, 
  FaTimes,
  FaMapMarkerAlt,
  FaFileAlt,
  FaExclamationTriangle,
  FaTrash,
  FaPhone,
  FaEnvelope,
  FaIdCard,
  FaCalendarDay,
  FaUserTie,
  FaInfoCircle,
  FaSearch,
  FaChevronDown
} from 'react-icons/fa';

interface EventModalProps {
  event: {
    customer_id: number | null;
    customer_name?: string | null;
    customer_address?: string | null;
    event_type: string | null;
    description: string;
    scheduled_date: string;
    user_ids: number[];
    // Keep legacy field for backward compatibility
    user_id: number | null;
  };
  onClose: () => void;
  onSubmit: (e: React.FormEvent) => void;
  setEvent: (event: {
    customer_id: number | null;
    customer_name?: string | null;
    customer_address?: string | null;
    event_type: string | null;
    description: string;
    scheduled_date: string;
    user_ids: number[];
    user_id: number | null;
  }) => void;
  isEditing: boolean;
  submitting: boolean;
  customers: Customer[];
  users: User[];
}

const EventModal: React.FC<EventModalProps> = ({
  event,
  onClose,
  onSubmit,
  setEvent,
  isEditing,
  submitting,
  customers,
  users,
}) => {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const EVENT_TYPES = [
    "offerte",
    "werkbon",
    "onderhoudsbon",
    "onderhoudscontract",
    "meldkamercontract",
    "beveiligingscertificaat",
    "intakedocument",
    "projectietekening",
    "beveiligingsplan",
    "kabeltekeningen",
    "checklist oplevering installatie",
    "vrije_documenten",
    "factuur"
  ];

  const [customerSearch, setCustomerSearch] = useState('');
  const [searchResults, setSearchResults] = useState<Customer[]>([]);
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [formModified, setFormModified] = useState(false);
  const [noDocumentType, setNoDocumentType] = useState(false);
  const initialEventRef = useRef({...event});
  const { showConfirmation } = useConfirmation();

  // Update initial event ref when event prop changes (for editing different events)
  useEffect(() => {
    initialEventRef.current = {...event};
    setFormModified(false); // Reset form modified state when switching events

    // Set noDocumentType state based on event_type
    setNoDocumentType(!event.event_type || event.event_type === '');

    // Reset and set customer search when event changes
    if (isEditing && event.customer_id) {
      // First try to use customer_name from event data
      if (event.customer_name) {
        setCustomerSearch(event.customer_name);
      } else {
        // Fallback to searching in customers array
        const selectedCustomer = customers.find(c => c.id === event.customer_id);
        if (selectedCustomer) {
          setCustomerSearch(selectedCustomer.name);
        } else {
          setCustomerSearch('');
        }
      }
    } else {
      setCustomerSearch('');
    }
  }, [event.customer_id, event.event_type, event.description, event.scheduled_date, JSON.stringify(event.user_ids), isEditing, customers]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.customer-search-container') && !target.closest('.browse-customers-btn')) {
        setShowCustomerDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Load search results when the search term changes
  useEffect(() => {
    // Don't search if the term is too short
    if (customerSearch.trim().length < 2) {
      // Keep existing results if dropdown is showing
      if (!showCustomerDropdown) {
        setSearchResults([]);
      }
      return;
    }

    const fetchSearchResults = async () => {
      setIsSearching(true);
      try {
        const response = await searchCustomers(customerSearch);
        setSearchResults(response.customers);
      } catch (error) {
        console.error('Error searching customers:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    // Debounce the search to avoid too many API calls
    const timeoutId = setTimeout(() => {
      fetchSearchResults();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [customerSearch, showCustomerDropdown, customers]);

  // Track form modifications
  useEffect(() => {
    // Compare current event with initial state
    const hasChanges = JSON.stringify(event) !== JSON.stringify(initialEventRef.current);
    setFormModified(hasChanges);
  }, [event]);

  // Function to handle modal close with confirmation if needed
  const handleClose = () => {
    if (formModified) {
      showConfirmation({
        title: "Discard Changes",
        message: "You have unsaved changes. Are you sure you want to close this window?",
        confirmText: "Discard",
        cancelText: "Cancel",
        confirmButtonClass: "bg-red-600 hover:bg-red-700",
        onConfirm: () => onClose()
      });
    } else {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 p-4 flex items-center justify-center z-50"
      onClick={handleClose}
      style={{ pointerEvents: 'auto' }}
    >
      <div
        className="bg-white shadow-xl rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
        style={{ pointerEvents: 'auto' }}
      >
        {/* Header */}
        <div className="flex justify-between items-start p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <FaCalendarAlt className="text-white" size={24} />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  {isEditing ? "Edit Event" : "Create New Event"}
                </h2>
                <p className="text-gray-600 mt-1">
                  {isEditing ? "Update event details and assignments" : "Schedule a new event with customer and user assignments"}
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-full"
            disabled={submitting}
          >
            <FaTimes size={24} />
          </button>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-6 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                  {validationErrors.map((err, index) => (
                    <li key={index}>{err}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={async (e) => {
          e.preventDefault();
          setValidationErrors([]);

          // Validate input data
          const { isValid, errors } = await validateData(eventSchema, event);

          if (!isValid) {
            setValidationErrors(errors);
            return;
          }

          // Submit the form
          onSubmit(e);
        }} className="p-6 space-y-6">
          
          {/* Customer Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaBuilding className="mr-2 text-purple-600" />
              Customer Selection
            </h3>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="relative customer-search-container">
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                  <div className="relative flex-grow">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <FaSearch className="w-5 h-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search customer by name..."
                      value={customerSearch}
                      onChange={(e) => {
                        setCustomerSearch(e.target.value);
                        if (e.target.value.trim() !== '') {
                          setShowCustomerDropdown(true);
                        }
                      }}
                      onFocus={() => {
                        if (customerSearch.trim() !== '') {
                          setShowCustomerDropdown(true);
                        }
                      }}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={submitting}
                    />
                  </div>
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                    onClick={async () => {
                      if (!showCustomerDropdown) {
                        setIsSearching(true);
                        try {
                          const response = await getAllCustomersNoPage();
                          setSearchResults(response.customers);
                        } catch (error) {
                          console.error('Error fetching all customers:', error);
                        } finally {
                          setIsSearching(false);
                        }
                      }
                      setShowCustomerDropdown(!showCustomerDropdown);
                    }}
                    disabled={submitting}
                  >
                    <FaChevronDown className="w-4 h-4 mr-2" />
                    Browse All
                  </button>
                </div>
                
                {/* Customer Dropdown */}
                {showCustomerDropdown && searchResults.length > 0 && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                    {isSearching ? (
                      <div className="p-4 text-center text-gray-500 flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
                        Searching...
                      </div>
                    ) : (
                      <>
                        {searchResults.map((customer) => (
                          <div
                            key={customer.id}
                            className={`px-4 py-3 cursor-pointer hover:bg-gray-100 transition-colors duration-150 ${event.customer_id === customer.id ? 'bg-blue-50' : ''}`}
                            onClick={() => {
                              setEvent({
                                ...event,
                                customer_id: customer.id,
                                customer_name: customer.name,
                                customer_address: customer.address
                              });
                              setCustomerSearch(customer.name);
                              setShowCustomerDropdown(false);
                            }}
                          >
                            <div className="font-medium text-gray-900">{customer.name}</div>
                            {customer.address && (
                              <div className="text-sm text-gray-600 mt-1 flex items-center">
                                <FaMapMarkerAlt className="mr-1" size={12} />
                                {customer.address}
                              </div>
                            )}
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                )}
              </div>
              
              {/* Selected Customer Display */}
              {event.customer_id && (
                <div className="mt-4 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-purple-700 text-sm uppercase">Selected Customer:</p>
                      <p className="text-gray-900 font-bold text-lg mt-1">
                        {(() => {
                          if (event.customer_id && event.customer_name) {
                            return event.customer_name;
                          }
                          const customer = customers.find(c => c.id === event.customer_id);
                          return customer?.name || 'Unknown';
                        })()}
                      </p>
                      {(() => {
                        if (event.customer_id && event.customer_address) {
                          return <p className="text-gray-700 mt-1 flex items-center"><FaMapMarkerAlt className="mr-1" size={12} />{event.customer_address}</p>;
                        }
                        const customer = customers.find(c => c.id === event.customer_id);
                        return customer?.address ? <p className="text-gray-700 mt-1 flex items-center"><FaMapMarkerAlt className="mr-1" size={12} />{customer.address}</p> : null;
                      })()}
                    </div>
                    <button
                      type="button"
                      className="text-sm text-red-600 hover:text-red-800 transition-colors duration-150 flex items-center p-2 hover:bg-red-50 rounded"
                      onClick={() => {
                        setEvent({
                          ...event,
                          customer_id: null,
                          customer_name: null,
                          customer_address: null
                        });
                        setCustomerSearch('');
                      }}
                    >
                      <FaTimes className="w-4 h-4 mr-1" />
                      Clear
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Event Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaInfoCircle className="mr-2 text-blue-600" />
                Event Information
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Type {!noDocumentType && <span className="text-red-500">*</span>}
                  </label>
                  <div className="relative">
                    <select
                      value={noDocumentType ? '' : (event.event_type || '')}
                      onChange={(e) => {
                        setEvent({ ...event, event_type: e.target.value || null });
                      }}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      required={!noDocumentType}
                      disabled={submitting || noDocumentType}
                    >
                      <option value="">Select Event Type</option>
                      {EVENT_TYPES.map((type) => (
                        <option key={type} value={type}>
                          {type.charAt(0).toUpperCase() + type.slice(1).replace(/_/g, ' ')}
                        </option>
                      ))}
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <FaChevronDown className="h-4 w-4" />
                    </div>
                  </div>
                  
                  {/* Niet van toepassing checkbox */}
                  <div className="mt-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={noDocumentType}
                        onChange={(e) => {
                          setNoDocumentType(e.target.checked);
                          if (e.target.checked) {
                            // Set event_type to null for "niet van toepassing"
                            setEvent({ ...event, event_type: null });
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        disabled={submitting}
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        Niet van toepassing (geen document type)
                      </span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={event.description}
                    onChange={(e) => setEvent({ ...event, description: e.target.value })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm min-h-[100px] resize-y"
                    placeholder="Enter a detailed description of the event..."
                    required
                    disabled={submitting}
                  />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FaClock className="mr-2 text-green-600" />
                Schedule & Assignment
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Scheduled Date <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <FaCalendarAlt className="w-5 h-5 text-gray-400" />
                    </div>
                    <input
                      type="datetime-local"
                      value={formatDateTimeForInput(event.scheduled_date)}
                      onChange={(e) => setEvent({ ...event, scheduled_date: e.target.value })}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      required
                      disabled={submitting}
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assign Users <span className="text-red-500">*</span>
                  </label>
                  <div className="bg-gray-50 rounded-lg p-3 max-h-40 overflow-y-auto">
                    <div className="space-y-2">
                      {users.map((user) => {
                        const isSelected = event.user_ids.includes(user.id);
                        return (
                          <div key={user.id} className="flex items-center space-x-3">
                            <input
                              type="checkbox"
                              id={`user-${user.id}`}
                              checked={isSelected}
                              onChange={(e) => {
                                const newUserIds = e.target.checked
                                  ? [...event.user_ids, user.id]
                                  : event.user_ids.filter(id => id !== user.id);

                                setEvent({
                                  ...event,
                                  user_ids: newUserIds,
                                  user_id: newUserIds.length > 0 ? newUserIds[0] : null
                                });
                              }}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              disabled={submitting}
                            />
                            <label
                              htmlFor={`user-${user.id}`}
                              className={`flex-1 text-sm text-gray-700 ${submitting ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                            >
                              <div className="font-medium">{user.name || user.email}</div>
                              <div className="text-xs text-gray-500 capitalize">{user.role}</div>
                            </label>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="bg-gray-50 border-t border-gray-200 p-6 -mx-6 -mb-6">
            <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                disabled={submitting}
              >
                <FaTimes className="mr-2 h-4 w-4" />
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <FaCheckCircle className="mr-2 h-4 w-4" />
                    {isEditing ? "Update Event" : "Create Event"}
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EventModal;


