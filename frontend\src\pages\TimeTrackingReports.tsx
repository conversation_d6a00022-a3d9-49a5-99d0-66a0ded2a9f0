
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { getAllUsers } from '../services/userService';
import { getTimeEntriesByMonthYear, getMileageEntriesByMonthYear } from '../services/timeTrackingService';
import { User } from '../types/user';
import LoadingSpinner from '../components/LoadingSpinner';
import { 
  FaUserClock, 
  FaArrowLeft, 
  FaCalendarAlt, 
  FaChevronLeft, 
  FaChevronRight,
  FaUser,
  FaDownload,
  FaClock,
  FaCar,
  FaFileExcel
} from 'react-icons/fa';

const TimeTrackingReports: React.FC = () => {
  const { user: currentUser } = useAuth();
  const navigate = useNavigate();
  
  // Data
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [monthlyData, setMonthlyData] = useState<any[]>([]);
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  
  // Get current period values
  const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
  const currentYear = currentDate.getFullYear();
  
  // Load users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const usersResponse = await getAllUsers();
        setUsers(usersResponse.users);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError('Er is een fout opgetreden bij het ophalen van de gebruikers.');
      }
    };
    
    fetchUsers();
  }, []);
  
  // Load monthly data
  useEffect(() => {
    const fetchMonthlyData = async () => {
      if (!selectedUser) {
        setMonthlyData([]);
        setLoading(false);
        return;
      }
      
      try {
        setLoading(true);
        
        // Fetch time entries for the selected month and year
        const timeResponse = await getTimeEntriesByMonthYear(
          currentMonth,
          currentYear,
          selectedUser
        );
        
        // Fetch mileage entries for the selected month and year
        const mileageResponse = await getMileageEntriesByMonthYear(
          currentMonth,
          currentYear,
          selectedUser
        );
        
        // Group time entries by date
        const entriesByDate: { [key: string]: any } = {};
        
        // Process time entries
        timeResponse.entries.forEach((entry: any) => {
          const date = entry.date;
          if (!entriesByDate[date]) {
            entriesByDate[date] = {
              date,
              hours: 0,
              kilometers: 0,
              timeEntries: [],
              mileageEntries: []
            };
          }
          
          // Calculate hours
          const start = new Date(`2000-01-01T${entry.start_time}`);
          const end = new Date(`2000-01-01T${entry.end_time}`);
          const diffMs = end.getTime() - start.getTime();
          const diffHours = diffMs / (1000 * 60 * 60);
          
          entriesByDate[date].hours += diffHours;
          entriesByDate[date].timeEntries.push(entry);
        });
        
        // Process mileage entries
        mileageResponse.entries.forEach((entry: any) => {
          const date = entry.date;
          if (!entriesByDate[date]) {
            entriesByDate[date] = {
              date,
              hours: 0,
              kilometers: 0,
              timeEntries: [],
              mileageEntries: []
            };
          }
          
          entriesByDate[date].kilometers += entry.kilometers;
          entriesByDate[date].mileageEntries.push(entry);
        });
        
        // Convert to array and sort by date
        const sortedData = Object.values(entriesByDate).sort((a: any, b: any) => {
          return new Date(a.date).getTime() - new Date(b.date).getTime();
        });
        
        setMonthlyData(sortedData);
      } catch (err) {
        console.error('Error fetching monthly data:', err);
        setError('Er is een fout opgetreden bij het ophalen van de gegevens.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMonthlyData();
  }, [selectedUser, currentMonth, currentYear]);
  
  // Navigation functions
  const goToPreviousMonth = () => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setCurrentDate(newDate);
  };
  
  const goToNextMonth = () => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setCurrentDate(newDate);
  };
  
  // Helper functions
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('nl-NL', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };
  
  const getMonthName = (month: number) => {
    const monthNames = [
      'Januari', 'Februari', 'Maart', 'April', 'Mei', 'Juni',
      'Juli', 'Augustus', 'September', 'Oktober', 'November', 'December'
    ];
    return monthNames[month - 1];
  };
  
  // Calculate totals
  const totalHours = monthlyData.reduce((sum, day) => sum + day.hours, 0);
  const totalKilometers = monthlyData.reduce((sum, day) => sum + day.kilometers, 0);
  
  // Export functions
  const handleExportToExcel = () => {
    // In a real implementation, this would generate and download an Excel file
    alert('Deze functie is nog niet geïmplementeerd. In een echte implementatie zou dit een Excel-bestand genereren en downloaden.');
  };
  
  // Check if the user is authorized
  if (!currentUser || currentUser.role !== 'administrator') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Je hebt geen toegang tot deze pagina. Alleen administrators kunnen deze pagina bekijken.
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header with back button */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <button
            onClick={() => navigate('/time-tracking-admin')}
            className="mr-4 text-amspm-text dark:text-dark-text hover:text-amspm-primary dark:hover:text-amspm-primary"
            aria-label="Terug naar overzicht"
          >
            <FaArrowLeft size={20} />
          </button>
          <h1 className="text-2xl font-bold text-amspm-text dark:text-dark-text">
            <FaUserClock className="inline mr-2" /> Rapportages
          </h1>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}
      
      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4">
          Selecteer gebruiker en periode
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* User selector */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Gebruiker
            </label>
            <div className="relative">
              <select
                value={selectedUser || ''}
                onChange={(e) => setSelectedUser(e.target.value ? parseInt(e.target.value) : null)}
                className="input pr-8 w-full"
              >
                <option value="">Selecteer een gebruiker</option>
                {users.map(user => (
                  <option key={user.id} value={user.id}>
                    {user.name || user.email}
                  </option>
                ))}
              </select>
              <FaUser className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          </div>
          
          {/* Month selector */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Periode
            </label>
            <div className="flex items-center">
              <button 
                onClick={goToPreviousMonth}
                className="btn btn-icon btn-secondary"
                aria-label="Vorige maand"
              >
                <FaChevronLeft />
              </button>
              <div className="flex-1 text-center">
                <span className="font-medium">
                  {getMonthName(currentMonth)} {currentYear}
                </span>
              </div>
              <button 
                onClick={goToNextMonth}
                className="btn btn-icon btn-secondary"
                aria-label="Volgende maand"
              >
                <FaChevronRight />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {loading ? (
        <LoadingSpinner />
      ) : selectedUser ? (
        <>
          {/* Summary cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 dark:bg-blue-800 p-3 rounded-full mr-4">
                  <FaClock className="text-blue-600 dark:text-blue-300" size={24} />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Totaal uren</h3>
                  <p className="text-2xl font-bold text-amspm-text dark:text-dark-text">{totalHours.toFixed(2)}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
              <div className="flex items-center">
                <div className="bg-green-100 dark:bg-green-800 p-3 rounded-full mr-4">
                  <FaCar className="text-green-600 dark:text-green-300" size={24} />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Totaal kilometers</h3>
                  <p className="text-2xl font-bold text-amspm-text dark:text-dark-text">{totalKilometers.toFixed(2)}</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Export button */}
          <div className="flex justify-end mb-6">
            <button
              onClick={handleExportToExcel}
              className="btn btn-primary flex items-center"
            >
              <FaFileExcel className="mr-2" /> Exporteer naar Excel
            </button>
          </div>
          
          {/* Monthly data table */}
          <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4 flex items-center">
              <FaCalendarAlt className="mr-2" /> 
              Overzicht {getMonthName(currentMonth)} {currentYear}
            </h2>
            
            {monthlyData.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Datum
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Uren
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Kilometers
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                    {monthlyData.map((day, index) => (
                      <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {formatDate(day.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {day.hours.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {day.kilometers.toFixed(2)}
                        </td>
                      </tr>
                    ))}
                    {/* Total row */}
                    <tr className="bg-gray-50 dark:bg-gray-800 font-medium">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                        Totaal
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                        {totalHours.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                        {totalKilometers.toFixed(2)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                Geen gegevens gevonden voor deze periode.
              </p>
            )}
          </div>
        </>
      ) : (
        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            Selecteer een gebruiker om rapportages te bekijken.
          </p>
        </div>
      )}
    </div>
  );
};

export default TimeTrackingReports;
