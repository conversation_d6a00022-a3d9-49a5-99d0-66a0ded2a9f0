import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getCustomerById, updateCustomer } from '../services/customerService';
import { Customer } from '../types/customer';
import { useAuth } from '../context/AuthContext';
import { useConfirmation } from '../context/ConfirmationContext';
import LoadingSpinner from '../components/LoadingSpinner';
import Breadcrumbs from '../components/Breadcrumbs';
import CustomerModal from '../components/CustomerModal';

const CustomerEditPage: React.FC = () => {
  const { customerId } = useParams<{ customerId: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { showConfirmation } = useConfirmation();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [editingCustomer, setEditingCustomer] = useState<Partial<Customer>>({});

  useEffect(() => {
    const fetchCustomer = async () => {
      if (!customerId) return;

      try {
        setLoading(true);
        const data = await getCustomerById(parseInt(customerId));
        setCustomer(data);
        
        // Set the editing customer data
        setEditingCustomer({
          date: data.date,
          code: data.code,
          name: data.name,
          kvk_number: data.kvk_number,
          contact_person: data.contact_person,
          gender: data.gender,
          title: data.title,
          address: data.address,
          postal_code: data.postal_code,
          city: data.city,
          country: data.country,
          address2: data.address2,
          postal_code2: data.postal_code2,
          city2: data.city2,
          country2: data.country2,
          phone: data.phone,
          mobile: data.mobile,
          fax: data.fax,
          email: data.email,
          invoice_email: data.invoice_email,
          reminder_email: data.reminder_email,
          website: data.website,
          bank_account: data.bank_account,
          giro_account: data.giro_account,
          vat_number: data.vat_number,
          iban: data.iban,
          bic: data.bic,
          sepa_auth_type: data.sepa_auth_type,
          mandate_reference: data.mandate_reference,
          mandate_date: data.mandate_date,
          customer_type: data.customer_type,
          no_email: data.no_email,
          payment_term: data.payment_term,
          newsletter_groups: data.newsletter_groups,
          subscriptions: data.subscriptions,
          notes: data.notes,
        });
        
        setError(null);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || 'Failed to fetch customer data');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [customerId]);

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!customerId) return;
    
    setSubmitting(true);
    try {
      const updatedCustomer = await updateCustomer(parseInt(customerId), editingCustomer);
      setSubmitting(false);
      
      // Show success message
      showConfirmation({
        title: 'Success',
        message: 'Customer updated successfully',
        confirmText: 'OK',
        showCancel: false,
        onConfirm: () => {
          navigate(`/customers/${customerId}`);
        }
      });
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to update customer");
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner message="Loading customer data..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
        <button
          className="btn btn-primary"
          onClick={() => navigate(`/customers/${customerId}`)}
        >
          Back to Customer
        </button>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          Customer not found
        </div>
        <button
          className="btn btn-primary"
          onClick={() => navigate('/customers')}
        >
          Back to Customers
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      {/* Breadcrumbs */}
      <Breadcrumbs
        customerName={customer.name}
        customerId={customer.id}
        customTitle="Edit Customer"
      />

      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <h1 className="text-3xl font-bold text-amspm-text">
            Edit Customer: {customer.name}
          </h1>
          <button
            className="btn btn-outline mt-4 md:mt-0"
            onClick={() => navigate(`/customers/${customerId}`)}
          >
            Cancel
          </button>
        </div>

        <CustomerModal
          customer={editingCustomer}
          onClose={() => navigate(`/customers/${customerId}`)}
          onSubmit={handleUpdate}
          setCustomer={setEditingCustomer}
          isEditing={true}
          submitting={submitting}
          embedded={true}
        />
      </div>
    </div>
  );
};

export default CustomerEditPage;
