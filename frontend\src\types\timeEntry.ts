export interface TimeEntry {
  id: number;
  user_id: number;
  user_name: string;
  date: string;
  start_time: string;
  end_time: string;
  break_time: number; // Break time in minutes
  description: string | null;
  status: 'pending' | 'approved' | 'rejected';
  approved_by: number | null;
  approver_name: string | null;
  approved_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface TimeEntryFormData {
  id?: number;
  user_id: number;
  date: string;
  start_time: string;
  end_time: string;
  break_time: number; // Break time in minutes
  description?: string;
}

export interface MonthlyTimeEntrySummary {
  month: number;
  month_name: string;
  year: number;
  total_hours: number;
  approved_hours: number;
  pending_hours: number;
  total_kilometers: number;
  approved_kilometers: number;
  pending_kilometers: number;
}

export interface TimeEntriesResponse {
  entries: TimeEntry[];
  total: number;
  page: number;
  per_page: number;
  month?: number;
  year?: number;
  monthly_total_hours?: number;
  monthly_approved_hours?: number;
  monthly_pending_hours?: number;
}