"""
Simple Quote PDF generation service.
This module handles PDF generation for simple quotes.
"""
import logging
from typing import Dict, Optional
from datetime import datetime, timedelta
import os
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
import base64
import io
from PIL import Image as PILImage

from app.models.simple_quote import SimpleQuote
from app.utils.simple_quote_products import BASE_ALARM_PACKAGE, EXTRA_PRODUCTS, get_product_by_key, PRICING_CONFIG

# Configure logging
logger = logging.getLogger(__name__)

class SimpleQuotePDFService:
    """Service for generating PDF documents from simple quotes."""

    def __init__(self):
        """Initialize the PDF service."""
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()

    def _eur(self, amount: float) -> str:
        """Format a number as Euro in Dutch format (e.g., €1.151,68)."""
        try:
            s = f"{float(amount):,.2f}"  # e.g., 1,151.68
            s = s.replace(",", "_")      # 1_151.68
            s = s.replace(".", ",")      # 1_151,68
            s = s.replace("_", ".")      # 1.151,68
            return f"€{s}"
        except Exception:
            return f"€{amount:.2f}"  # Fallback

    def _setup_custom_styles(self):
        """Setup custom paragraph styles."""
        # Blue header bar style
        self.styles.add(ParagraphStyle(
            name='BlueHeader',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=colors.white,
            alignment=TA_CENTER,
            spaceBefore=0,
            spaceAfter=0
        ))

        # Company header style
        self.styles.add(ParagraphStyle(
            name='CompanyHeader',
            parent=self.styles['Heading1'],
            fontSize=22,
            textColor=colors.HexColor('#2c3e50'),
            alignment=TA_CENTER,
            spaceAfter=8,
            spaceBefore=10
        ))

        # Company info style
        self.styles.add(ParagraphStyle(
            name='CompanyInfo',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#34495e'),
            alignment=TA_CENTER,
            spaceAfter=15
        ))

        # Quote title style
        self.styles.add(ParagraphStyle(
            name='QuoteTitle',
            parent=self.styles['Heading2'],
            fontSize=18,
            textColor=colors.HexColor('#2980b9'),
            alignment=TA_CENTER,
            spaceAfter=15,
            spaceBefore=10
        ))

        # Section header style - centered as requested
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading3'],
            fontSize=13,
            textColor=colors.HexColor('#2c3e50'),
            spaceBefore=15,
            spaceAfter=8,
            fontName='Helvetica-Bold',
            alignment=TA_CENTER
        ))

        # Table header style
        self.styles.add(ParagraphStyle(
            name='TableHeader',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#2c3e50'),
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))

        # Table cell style
        self.styles.add(ParagraphStyle(
            name='TableCell',
            parent=self.styles['Normal'],
            fontSize=9,
            textColor=colors.HexColor('#34495e'),
            alignment=TA_LEFT
        ))

        # Footer style
        self.styles.add(ParagraphStyle(
            name='Footer',
            parent=self.styles['Normal'],
            fontSize=8,
            textColor=colors.HexColor('#7f8c8d'),
            alignment=TA_CENTER
        ))

    def generate_quote_pdf(self, quote: SimpleQuote) -> bytes:
        """
        Generate PDF for a simple quote.

        Args:
            quote: SimpleQuote instance

        Returns:
            PDF content as bytes
        """
        try:
            # Create PDF buffer
            buffer = io.BytesIO()
            
            # Create document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )

            # Build content
            story = []
            
            # Add company header
            story.extend(self._add_company_header())

            # Add quote header
            story.extend(self._add_quote_header(quote))
            
            # Add customer information
            story.extend(self._add_customer_info(quote))
            
            # Add quote details
            story.extend(self._add_quote_details(quote))
            
            # Add product list
            story.extend(self._add_product_list(quote))
            
            # Add pricing summary
            story.extend(self._add_pricing_summary(quote))
            
            # Add signature section
            story.extend(self._add_signature_section(quote))
            
            # Add terms and conditions
            story.extend(self._add_terms_and_conditions())
            
            # Add footer
            story.extend(self._add_footer())

            # Build PDF
            doc.build(story)
            
            # Get PDF content
            pdf_content = buffer.getvalue()
            buffer.close()
            
            logger.info(f"Generated PDF for quote {quote.quote_number}")
            return pdf_content

        except Exception as e:
            logger.error(f"Failed to generate PDF for quote {quote.id}: {str(e)}")
            raise Exception(f"Failed to generate PDF: {str(e)}")

    def _add_company_header(self) -> list:
        """Add professional company header like in the example."""
        content = []

        # Remove the blue header bar as requested

        # Logo (centered)
        try:
            logo_path = os.path.join('app', 'static', 'images', 'store_information', 'winkellogo.png')
            if os.path.exists(logo_path):
                logo = Image(logo_path, width=4*cm, height=2*cm)
                logo.hAlign = 'CENTER'
                content.append(logo)
                content.append(Spacer(1, 10))
        except Exception as e:
            logger.warning(f"Failed to load company logo: {str(e)}")

        # Company name
        content.append(Paragraph("De SecurityWinkel", self.styles['CompanyHeader']))

        # Company details
        company_info = """Professionele beveiligingsoplossingen<br/>
Tel: ************<br/>
Technische vragen: <EMAIL><br/>
Vragen over uw bestelling: <EMAIL>"""

        content.append(Paragraph(company_info, self.styles['CompanyInfo']))

        # Blue line separator
        content.append(HRFlowable(width="100%", thickness=2, color=colors.HexColor('#2980b9')))
        content.append(Spacer(1, 20))

        return content

    def _add_quote_header(self, quote: SimpleQuote) -> list:
        """Add quote header information."""
        content = []
        
        content.append(Paragraph("OFFERTE", self.styles['QuoteTitle']))
        
        # Quote details table
        quote_data = [
            ['Offertenummer:', quote.quote_number or 'Concept'],
            ['Datum:', quote.created_at.strftime('%d-%m-%Y') if quote.created_at else datetime.now().strftime('%d-%m-%Y')],
            ['Geldig tot:', (quote.created_at + timedelta(days=30)).strftime('%d-%m-%Y') if quote.created_at else (datetime.now() + timedelta(days=30)).strftime('%d-%m-%Y')],
            ['Status:', 'Ondertekend' if quote.status == 'SIGNED' else 'Concept']
        ]
        
        quote_table = Table(quote_data, colWidths=[4*cm, 6*cm])
        quote_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#E5E7EB')),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#F9FAFB')),
        ]))
        
        content.append(quote_table)
        content.append(Spacer(1, 20))
        
        return content

    def _add_customer_info(self, quote: SimpleQuote) -> list:
        """Add customer information in a professional table format."""
        content = []

        content.append(Paragraph("Klantgegevens", self.styles['SectionHeader']))
        content.append(Spacer(1, 8))

        # Create customer info table - always show all fields like in example
        customer_data = [
            ['Naam:', quote.customer_name or 'Camiel Adriaens'],
            ['Email:', quote.customer_email or '<EMAIL>'],
            ['Telefoon:', quote.customer_phone or '0681610919'],
            ['Adres:', quote.customer_address or 'Willem Bayerstraat 24'],
            ['Plaats:', quote.customer_city or 'Roermond'],
            ['Postcode:', quote.customer_postal_code or '6042 BK']
        ]

        # Create table with professional styling
        customer_table = Table(customer_data, colWidths=[3*cm, 8*cm])
        customer_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.HexColor('#2c3e50')),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#bdc3c7')),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#ecf0f1')),
        ]))

        content.append(customer_table)
        content.append(Spacer(1, 20))

        return content

    def _add_quote_details(self, quote: SimpleQuote) -> list:
        """Add quote details and category in professional table format."""
        content = []

        content.append(Paragraph("Offerte Details", self.styles['SectionHeader']))
        content.append(Spacer(1, 8))

        category_name = {
            'ALARM': 'ALARM',
            'CAMERAS': 'CAMERAS',
            'ALARM_CAMERAS': 'Alarm + Camera systeem'
        }.get(quote.category, quote.category)

        payment_terms = {
            'ONE_TERM': '1 termijn',
            'TWO_TERMS': '2 termijnen',
            'THREE_TERMS': '3 termijnen'
        }.get(quote.payment_terms, quote.payment_terms)

        # Create details table like in example
        details_data = [
            ['Systeem type:', category_name],
            ['Betalingsvoorwaarden:', payment_terms],
            ['Korting:', f'{quote.discount_percentage:.0f}%']
        ]

        details_table = Table(details_data, colWidths=[4*cm, 7*cm])
        details_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.HexColor('#2c3e50')),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#bdc3c7')),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#ecf0f1')),
        ]))

        content.append(details_table)
        content.append(Spacer(1, 20))

        return content

    def _add_product_list(self, quote: SimpleQuote) -> list:
        """Add product list to PDF with images in table format."""
        content = []

        content.append(Paragraph("Producten & Diensten", self.styles['SectionHeader']))

        # Create product table header with professional styling
        header_row = [
            Paragraph('ARTIKELNAAM', self.styles['TableHeader']),
            Paragraph('HOEVEELHEID', self.styles['TableHeader']),
            Paragraph('INSTALLATIEKOSTEN', self.styles['TableHeader']),
            Paragraph('AFBEELDING', self.styles['TableHeader'])
        ]
        table_data = [header_row]

        # Add base package if alarm category (VERPLICHT PAKKET)
        if quote.category in ['ALARM', 'ALARM_CAMERAS']:
            # Add base package products met juiste namen
            for product_key, product in BASE_ALARM_PACKAGE.items():
                # Get product image
                image_cell = self._get_product_image_cell(product['image'])

                # Voor lease systeem: toon "Inbegrepen in basispakket" voor basis producten
                table_data.append([
                    Paragraph(f"<b>{product['name']}</b><br/>{product['description']}", self.styles['TableCell']),
                    Paragraph(str(product['quantity']), self.styles['TableCell']),
                    Paragraph("Inbegrepen", self.styles['TableCell']),
                    image_cell
                ])

        # Add extra products
        extra_products = {
            'extra_magneetcontact': quote.extra_magneetcontact,
            'extra_shock_sensor': quote.extra_shock_sensor,
            'extra_pir_normaal': quote.extra_pir_normaal,
            'extra_rookmelder': quote.extra_rookmelder,
            'extra_pircam': quote.extra_pircam,
            'extra_bediendeel': quote.extra_bediendeel,
            'extra_sirene': quote.extra_sirene
        }

        for product_key, quantity in extra_products.items():
            if quantity > 0:
                product_info = get_product_by_key(product_key.replace('extra_', ''))
                if product_info:
                    image_cell = self._get_product_image_cell(product_info['image'])

                    # Bereken installatiekosten per product: incl BTW + marge + arbeid
                    price_incl_vat = product_info['price_incl_vat']
                    price_with_margin = price_incl_vat * 1.1155  # 11.55% marge
                    installation_cost_per_item = price_with_margin + 10.0  # €10 arbeid
                    total_installation_cost = installation_cost_per_item * quantity

                    table_data.append([
                        Paragraph(f"<b>{product_info['name']}</b><br/>{product_info['description'][:30]}...", self.styles['TableCell']),
                        Paragraph(str(quantity), self.styles['TableCell']),
                        Paragraph(self._eur(total_installation_cost), self.styles['TableCell']),
                        image_cell
                    ])

        # Add videodoorbell
        if quote.videodoorbell_free:
            videodoorbell_info = get_product_by_key('videodoorbell')
            if videodoorbell_info:
                image_cell = self._get_product_image_cell(videodoorbell_info['image'])
                table_data.append([
                    Paragraph('<b>Video Deurbel (GRATIS)</b><br/>Slimme video deurbel', self.styles['TableCell']),
                    Paragraph('1', self.styles['TableCell']),
                    Paragraph(self._eur(10.0), self.styles['TableCell']),  # Alleen arbeid kosten
                    image_cell
                ])
        elif quote.videodoorbell_paid:
            videodoorbell_info = get_product_by_key('videodoorbell')
            if videodoorbell_info:
                image_cell = self._get_product_image_cell(videodoorbell_info['image'])
                # Videodeurbel: €249,99 + €10 arbeid (geen extra marge)
                installation_cost = 249.99 + 10.0
                table_data.append([
                    Paragraph('<b>Video Deurbel</b><br/>Slimme video deurbel', self.styles['TableCell']),
                    Paragraph('1', self.styles['TableCell']),
                    Paragraph(self._eur(installation_cost), self.styles['TableCell']),
                    image_cell
                ])

        # Create table with professional styling like in example
        product_table = Table(table_data, colWidths=[6*cm, 2*cm, 3*cm, 3.5*cm])
        product_table.setStyle(TableStyle([
            # Header styling - better readability with lighter background
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#5a6c7d')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

            # Data rows styling
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.HexColor('#2c3e50')),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),      # Product names left
            ('ALIGN', (1, 1), (1, -1), 'CENTER'),    # Quantity center
            ('ALIGN', (2, 1), (2, -1), 'CENTER'),    # Price center
            ('ALIGN', (3, 1), (3, -1), 'CENTER'),    # Images center
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

            # Professional borders and grid
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#bdc3c7')),
            ('LINEBELOW', (0, 0), (-1, 0), 2, colors.HexColor('#34495e')),

            # Alternating row colors for better readability
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        content.append(product_table)
        content.append(Spacer(1, 20))

        return content

    def _get_product_image_cell(self, image_path: str):
        """Get product image for table cell."""
        try:
            # Construct full path to image
            full_path = os.path.join('app', 'static', 'product_images', image_path)

            if os.path.exists(full_path):
                # Create image with fixed size for table
                img = Image(full_path, width=2.5*cm, height=2*cm)
                return img
            else:
                # Return placeholder text if image not found
                return "PLAATJE"

        except Exception as e:
            logger.warning(f"Failed to load product image {image_path}: {str(e)}")
            return "PLAATJE"

    def _add_pricing_summary(self, quote: SimpleQuote) -> list:
        """Add professional pricing summary like in example."""
        content = []

        # Services section with checkmarks - professional styling
        services_data = [
            [Paragraph('MELDKAMER', self.styles['TableHeader']), Paragraph('✓', self.styles['TableHeader'])],
            [Paragraph('SERVICE', self.styles['TableHeader']), Paragraph('✓', self.styles['TableHeader'])],
            [Paragraph('ONDERHOUD', self.styles['TableHeader']), Paragraph('✓', self.styles['TableHeader'])]
        ]

        services_table = Table(services_data, colWidths=[12*cm, 2*cm])
        services_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.white),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.HexColor('#2c3e50')),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#2c3e50')),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        content.append(services_table)
        content.append(Spacer(1, 15))

        # Calculate final prices
        final_installation_cost = quote.get_final_installation_cost()
        total_monthly = quote.get_total_monthly_cost()

        # Installation costs section - professional styling like example
        discount_text = f"Korting: {quote.discount_percentage:.0f}%" if quote.discount_percentage > 0 else "Korting: 0%"
        final_cost_text = self._eur(final_installation_cost)

        installation_data = [
            [
                Paragraph('EENMALIGE INSTALLATIEKOSTEN', self.styles['TableHeader']),
                Paragraph(final_cost_text, self.styles['TableHeader']),
                Paragraph('KORTING', self.styles['TableHeader'])
            ],
            [
                Paragraph(f'Basis: {self._eur(quote.total_installation_cost)}', self.styles['TableCell']),
                Paragraph('', self.styles['TableCell']),
                Paragraph(discount_text, self.styles['TableCell'])
            ]
        ]

        installation_table = Table(installation_data, colWidths=[8*cm, 3*cm, 3*cm])
        installation_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#5a6c7d')),
            ('BACKGROUND', (0, 1), (-1, 1), colors.HexColor('#ecf0f1')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, 1), colors.HexColor('#2c3e50')),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, 1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTSIZE', (0, 1), (-1, 1), 10),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('ALIGN', (2, 0), (2, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#2c3e50')),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        content.append(installation_table)
        content.append(Spacer(1, 15))

        # Monthly costs section - professional styling
        monthly_breakdown = [
            f"Apparatuur: {self._eur(quote.monthly_equipment_cost)}",
            f"Meldkamer: {self._eur(quote.monthly_monitoring_cost)}",
            f"Onderhoud: {self._eur(quote.monthly_maintenance_cost)}"
        ]

        monthly_data = [
            [
                Paragraph('MAANDELIJKSE KOSTEN', self.styles['TableHeader']),
                Paragraph(self._eur(total_monthly), self.styles['TableHeader'])
            ],
            [
                Paragraph(' + '.join(monthly_breakdown), self.styles['TableCell']),
                Paragraph('€49,99', self.styles['TableCell'])
            ]
        ]

        monthly_table = Table(monthly_data, colWidths=[10*cm, 4*cm])
        monthly_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#5a6c7d')),
            ('BACKGROUND', (0, 1), (-1, 1), colors.HexColor('#ecf0f1')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, 1), colors.HexColor('#2c3e50')),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, 1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTSIZE', (0, 1), (-1, 1), 10),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#2c3e50')),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        content.append(monthly_table)
        content.append(Spacer(1, 20))

        return content

    def _add_signature_section(self, quote: SimpleQuote) -> list:
        """Add signature section with customer details."""
        content = []

        # Customer details and signature section - AMSPM Layout
        signature_data = [
            ['VOORNAAM', quote.customer_name.split()[0] if quote.customer_name else '', 'HANDTEKENING'],
            ['ACHTERNAAM', ' '.join(quote.customer_name.split()[1:]) if len(quote.customer_name.split()) > 1 else '', ''],
            ['DATUM', quote.signed_at.strftime('%d-%m-%Y') if quote.signed_at else '', ''],
            ['PLAATS', quote.customer_city or '', '']
        ]

        # If quote is signed, add the signature image
        signature_cell = ''
        if quote.signature_data and quote.status == 'SIGNED':
            try:
                # Decode base64 signature
                signature_data_clean = quote.signature_data.split(',')[1] if ',' in quote.signature_data else quote.signature_data
                signature_bytes = base64.b64decode(signature_data_clean)

                # Create image from signature
                signature_image = PILImage.open(io.BytesIO(signature_bytes))

                # Save to temporary buffer
                img_buffer = io.BytesIO()
                signature_image.save(img_buffer, format='PNG')
                img_buffer.seek(0)

                # Add signature image
                signature_cell = Image(img_buffer, width=4*cm, height=2*cm)

            except Exception as e:
                logger.warning(f"Failed to add signature image: {str(e)}")
                signature_cell = 'Digitaal ondertekend'

        # Update signature data with actual signature
        signature_data[0][2] = signature_cell if signature_cell else ''

        # Fill in customer data if available
        if quote.customer_name:
            name_parts = quote.customer_name.split(' ', 1)
            signature_data[0][1] = name_parts[0] if name_parts else ''
            signature_data[1][1] = name_parts[1] if len(name_parts) > 1 else ''

        if quote.signed_at:
            signature_data[2][1] = quote.signed_at.strftime('%d-%m-%Y')

        if quote.customer_city:
            signature_data[3][1] = quote.customer_city

        signature_table = Table(signature_data, colWidths=[3*cm, 6*cm, 5*cm])
        signature_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.white),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.HexColor('#2c3e50')),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('ALIGN', (2, 0), (2, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#bdc3c7')),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            # Make signature cell span multiple rows visually
            ('SPAN', (2, 0), (2, -1)),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#ecf0f1')),
        ]))

        content.append(signature_table)
        content.append(Spacer(1, 20))

        return content

    def _add_terms_and_conditions(self) -> list:
        """Add terms and conditions."""
        content = []

        content.append(Paragraph("Algemene Voorwaarden", self.styles['SectionHeader']))

        terms = """
        <para fontSize="9">
        1. Deze offerte is 30 dagen geldig vanaf de datum van uitgifte.<br/>
        2. Alle prijzen zijn inclusief BTW en exclusief onvoorziene werkzaamheden.<br/>
        3. Betaling dient te geschieden volgens de overeengekomen betalingsvoorwaarden.<br/>
        4. Installatie wordt uitgevoerd door gecertificeerde technici.<br/>
        5. Op alle apparatuur geldt fabrieksgarantie, installatie heeft 2 jaar garantie.<br/>
        6. Wijzigingen in de offerte zijn mogelijk tot de installatiedatum.<br/>
        7. Bij annulering binnen 14 dagen na ondertekening worden geen kosten in rekening gebracht.<br/>
        8. Voor deze overeenkomst gelden de algemene voorwaarden van De SecurityWinkel.<br/>
        </para>
        """

        content.append(Paragraph(terms, self.styles['Normal']))
        content.append(Spacer(1, 20))

        return content

    def _add_footer(self) -> list:
        """Add professional footer information."""
        content = []

        footer_text = """© 2025, De SecurityWinkel<br/>
Produktieweg 1, 6045 JC Roermond<br/>
Tel: ************<br/>
Technische vragen: <EMAIL> | Vragen over uw bestelling: <EMAIL>"""

        content.append(HRFlowable(width="100%", thickness=1, color=colors.HexColor('#bdc3c7')))
        content.append(Spacer(1, 10))
        content.append(Paragraph(footer_text, self.styles['Footer']))

        return content
