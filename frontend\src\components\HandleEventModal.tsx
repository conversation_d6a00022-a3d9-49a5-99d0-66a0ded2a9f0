import React, { useState, useEffect, useRef } from 'react';
import { Event } from '../types/event';
import { handleEventSchema, validateData } from '../utils/validation';
import { useConfirmation } from '../context/ConfirmationContext';
import { DocumentTemplate } from '../types/document_template';
import { Customer } from '../types/customer';
import { getAllTemplates } from '../services/documentTemplateService';
import { getCustomerById } from '../services/customerService';
import TemplateFormEditor from './TemplateFormEditor';
import InspectionTemplateForm from './InspectionTemplateForm';
import TemplateService from '../services/templateService';
import { 
  FaFileUpload, 
  FaFileAlt, 
  FaTimes, 
  FaCheckCircle, 
  FaExclamationTriangle,
  FaCalendarAlt,
  FaClock,
  FaInfoCircle,
  FaUpload,
  FaDownload,
  FaEdit,
  FaTrash,
  FaP<PERSON>,
  FaEnvelope,
  FaIdCard,
  FaCalendarDay,
  FaUserTie,
  FaBuilding,
  FaMapMarkerAlt,
  FaChevronDown
} from 'react-icons/fa';
import { getTwelveMonthExpiryDate } from '../utils/dateUtils';

interface HandleEventModalProps {
  event: Event;
  onClose: () => void;
  onCompleteWithFile: (event: Event & { document_config?: any }) => void;
  onCompleteWithTemplate?: (event: Event & { document_config?: any }, templateBlob: Blob, fileName: string) => void;
  submitting: boolean;
  file: File | null;
  setFile: (file: File | null) => void;
  newExpiryDate: string;
  setNewExpiryDate: (date: string) => void;
  expiryType: "date" | "niet_van_toepassing";
  setExpiryType: (type: "date" | "niet_van_toepassing") => void;
  documentNotApplicable: boolean;
  setDocumentNotApplicable: (value: boolean) => void;
  useVersionStatus: boolean;
  setUseVersionStatus: (use: boolean) => void;
  versionStatus: "active" | "inactive";
  setVersionStatus: (status: "active" | "inactive") => void;
}

const HandleEventModal: React.FC<HandleEventModalProps> = ({
  event,
  onClose,
  onCompleteWithFile,
  onCompleteWithTemplate,
  submitting,
  file,
  setFile,
  newExpiryDate,
  setNewExpiryDate,
  expiryType,
  setExpiryType,
  documentNotApplicable,
  setDocumentNotApplicable,
  useVersionStatus,
  setUseVersionStatus,
  versionStatus,
  setVersionStatus
}) => {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [formModified, setFormModified] = useState(false);
  const { showConfirmation } = useConfirmation();

  // Template-related state
  const [completionMode, setCompletionMode] = useState<'file' | 'template'>('file');
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [templateContent, setTemplateContent] = useState<ArrayBuffer | null>(null);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  const [templateError, setTemplateError] = useState<string | null>(null);
  const [customerInfo, setCustomerInfo] = useState<Customer | null>(null);
  const initialStateRef = useRef({
    file,
    newExpiryDate,
    expiryType,
    documentNotApplicable,
    useVersionStatus,
    versionStatus,
    completionMode,
    selectedTemplate
  });

  // Load templates and customer info on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingTemplates(true);
        setTemplateError(null);
        
        // Load templates
        const allTemplates = await getAllTemplates();
        setTemplates(allTemplates);
        
        // Load customer information if event has customer_id
        if (event.customer_id) {
          try {
            const customerResponse = await getCustomerById(event.customer_id);
            setCustomerInfo(customerResponse);
          } catch (customerError: any) {
            console.error('Error loading customer info:', customerError);
            // Don't fail the entire modal if customer info fails to load
          }
        }
      } catch (error: any) {
        console.error('Error loading data:', error);
        setTemplateError('Failed to load templates');
      } finally {
        setLoadingTemplates(false);
      }
    };

    loadData();
  }, [event.customer_id]);

  // Load template content when a template is selected
  useEffect(() => {
    const loadTemplateContent = async () => {
      if (!selectedTemplate) {
        setTemplateContent(null);
        return;
      }

      try {
        setTemplateError(null);
        const content = await TemplateService.loadTemplate(selectedTemplate.id);
        setTemplateContent(content);
      } catch (error: any) {
        console.error('Error loading template content:', error);
        setTemplateError('Failed to load template content');
        setTemplateContent(null);
      }
    };

    loadTemplateContent();
  }, [selectedTemplate]);

  // Auto-set 12-month expiration for onderhoudsbon events on component mount
  useEffect(() => {
    if (event.event_type === 'onderhoudsbon') {
      setExpiryType('date');
      setNewExpiryDate(getTwelveMonthExpiryDate());
    }
  }, [event.event_type]);

  // Auto-set 12-month expiration for onderhoudsbon when template is selected
  useEffect(() => {
    if (selectedTemplate && event.event_type === 'onderhoudsbon') {
      setExpiryType('date');
      setNewExpiryDate(getTwelveMonthExpiryDate());
    }
  }, [selectedTemplate, event.event_type]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFile(e.target.files[0]);
      setFormModified(true);
    }
  };

  const handleTemplateCompletion = async (templateBlob: Blob, fileName: string) => {
    if (onCompleteWithTemplate) {
      // Create an enhanced event object with document configuration
      const enhancedEvent = {
        ...event,
        // Add document configuration that will be used when creating the document
        document_config: {
          expiry_type: expiryType,
          expiry_date: expiryType === "date" ? newExpiryDate : null,
          use_version_status: useVersionStatus,
          version_status: useVersionStatus ? versionStatus : null,
          event_type: event.event_type || "vrije_documenten" // Use vrije_documenten as fallback
        }
      };
      
      await onCompleteWithTemplate(enhancedEvent, templateBlob, fileName);
    }
  };

  const handleModeChange = (mode: 'file' | 'template') => {
    setCompletionMode(mode);
    setFormModified(true);
    if (mode === 'file') {
      setSelectedTemplate(null);
      setTemplateContent(null);
    } else {
      setFile(null);
    }
  };

  const handleEventTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedType = e.target.value;
    
    // Auto-set 12-month expiration for onderhoudsbon events
    if (selectedType === 'onderhoudsbon') {
      setExpiryType('date');
      setNewExpiryDate(getTwelveMonthExpiryDate());
    }
  };

  // Track form modifications
  useEffect(() => {
    const currentState = {
      file,
      newExpiryDate,
      expiryType,
      documentNotApplicable,
      useVersionStatus,
      versionStatus
    };

    // Compare current state with initial state
    const hasChanges = JSON.stringify(currentState) !== JSON.stringify(initialStateRef.current);
    setFormModified(hasChanges);
  }, [file, newExpiryDate, expiryType, documentNotApplicable, useVersionStatus, versionStatus]);

  // Handle button clicks
  const handleButtonClick = async (action: string) => {
    if (action === 'complete-with-file') {
      setValidationErrors([]);

      // Validate form data based on completion mode
      if (completionMode === 'file') {
        const { isValid, errors } = await validateData(handleEventSchema, {
          file,
          expiryType,
          newExpiryDate,
          documentNotApplicable,
          useVersionStatus,
          versionStatus
        });

        if (!isValid) {
          setValidationErrors(errors);
          return;
        }

        // Create an enhanced event object with document configuration
        const enhancedEvent = {
          ...event,
          // Add document configuration that will be used when creating the document
          document_config: {
            expiry_type: expiryType,
            expiry_date: expiryType === "date" ? newExpiryDate : null,
            use_version_status: useVersionStatus,
            version_status: useVersionStatus ? versionStatus : null,
            event_type: event.event_type || "vrije_documenten" // Use vrije_documenten as fallback
          }
        };
        
        onCompleteWithFile(enhancedEvent);
      } else if (completionMode === 'template') {
        // Template mode validation
        if (!selectedTemplate) {
          setValidationErrors(['Please select a template']);
          return;
        }

        // For template mode, we need to validate the document configuration
        // The template form will handle the actual template completion
        // but we need to ensure expiry date is set if required
        if (expiryType === "date" && !newExpiryDate) {
          setValidationErrors(['Please set an expiry date for the document']);
          return;
        }

        // If validation passes, the template form will handle completion
        // The form will call handleTemplateCompletion when ready
      }
    } else if (action === 'close') {
      if (formModified) {
        showConfirmation({
          title: "Discard Changes",
          message: "You have unsaved changes. Are you sure you want to close this window?",
          confirmText: "Discard",
          cancelText: "Cancel",
          confirmButtonClass: "bg-red-600 hover:bg-red-700",
          onConfirm: () => onClose()
        });
      } else {
        onClose();
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 p-4 flex items-center justify-center z-50">
      <div
        className={`bg-white shadow-xl rounded-lg w-full max-h-[90vh] overflow-y-auto ${
          completionMode === 'template' && selectedTemplate
            ? 'max-w-4xl'
            : 'max-w-2xl'
        }`}
        onClick={(e) => e.stopPropagation()}
        style={{ pointerEvents: 'auto' }}
      >
        {/* Header */}
        <div className="flex justify-between items-start p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <FaCheckCircle className="text-white" size={24} />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Complete Event #{event.id}
                </h2>
                <p className="text-gray-600 mt-1">
                  {event.event_type ? (event.event_type.charAt(0).toUpperCase() + event.event_type.slice(1).replace(/_/g, ' ')) : 'Algemeen'} • {event.description}
                </p>
              </div>
            </div>
          </div>
          <button
            onClick={() => handleButtonClick('close')}
            className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-full"
            disabled={submitting}
          >
            <FaTimes size={24} />
          </button>
        </div>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-6 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                  {validationErrors.map((err, index) => (
                    <li key={index}>{err}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {templateError && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-6 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{templateError}</p>
              </div>
            </div>
          </div>
        )}

        {/* Mode Selection */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <FaInfoCircle className="mr-2 text-blue-600" />
            Completion Method
          </h3>
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => handleModeChange('file')}
              className={`flex-1 flex items-center justify-center px-4 py-3 rounded-md text-sm font-medium transition-all duration-200 ${
                completionMode === 'file'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
              disabled={submitting}
            >
              <FaFileUpload className="mr-2 h-4 w-4" />
              Upload File
            </button>
            <button
              onClick={() => handleModeChange('template')}
              className={`flex-1 flex items-center justify-center px-4 py-3 rounded-md text-sm font-medium transition-all duration-200 ${
                completionMode === 'template'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
              disabled={submitting}
            >
              <FaFileAlt className="mr-2 h-4 w-4" />
              Use Template
            </button>
          </div>
        </div>

        {/* Content based on mode */}
        <div className="p-6">
          {completionMode === 'file' ? (
            <div className="space-y-6">
              {/* File Upload Section */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FaUpload className="mr-2 text-purple-600" />
                  Document Upload
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Upload Document <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleFileChange}
                        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        disabled={submitting || documentNotApplicable}
                      />
                    </div>
                    
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="documentNotApplicable"
                        checked={documentNotApplicable}
                        onChange={(e) => {
                          setDocumentNotApplicable(e.target.checked);
                          if (e.target.checked) setFile(null);
                          setFormModified(true);
                        }}
                        disabled={submitting}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="documentNotApplicable" className="ml-2 block text-sm text-gray-700">
                        Document not applicable
                      </label>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          ) : (
            // Template mode
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <FaFileAlt className="mr-2 text-purple-600" />
                  Template Selection
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  {loadingTemplates ? (
                    <div className="text-center py-8">
                      <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                      <p className="mt-2 text-gray-600">Loading templates...</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Select Template <span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                          <select
                            value={selectedTemplate?.id || ''}
                                                         onChange={(e) => {
                               const templateId = parseInt(e.target.value);
                               const template = templates.find(t => t.id === templateId) || null;
                               setSelectedTemplate(template);
                               setFormModified(true);
                               
                               // Auto-set 12-month expiration for onderhoudsbon when template is selected
                               if (template && event.event_type === 'onderhoudsbon') {
                                 setExpiryType('date');
                                 setNewExpiryDate(getTwelveMonthExpiryDate());
                               }
                             }}
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            disabled={submitting}
                          >
                            <option value="">Choose a template...</option>
                            {templates.map(template => (
                              <option key={template.id} value={template.id}>
                                {template.name} ({template.document_type})
                              </option>
                            ))}
                          </select>
                          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <FaChevronDown className="h-4 w-4" />
                          </div>
                        </div>
                      </div>

                                             {selectedTemplate && templateContent && (
                         <>
                           {/* Show loading state while customer info is being fetched */}
                           {event.customer_id && !customerInfo && (
                             <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                               <div className="flex items-center text-blue-700">
                                 <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                 <span className="text-sm">Loading customer information...</span>
                               </div>
                             </div>
                           )}
                           
                           {/* Show customer info if available */}
                           {customerInfo && (
                             <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                               <div className="flex items-center text-green-700">
                                 <FaBuilding className="mr-2" size={14} />
                                 <span className="text-sm font-medium">Customer: {customerInfo.name}</span>
                                 {customerInfo.address && (
                                   <span className="text-sm text-green-600 ml-2">
                                     • {customerInfo.address}
                                   </span>
                                 )}
                               </div>
                             </div>
                           )}
                           
                           {/* Show fallback if customer info failed to load */}
                           {event.customer_id && !customerInfo && !loadingTemplates && (
                             <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                               <div className="flex items-center text-yellow-700">
                                 <FaExclamationTriangle className="mr-2" size={14} />
                                 <span className="text-sm">Customer information unavailable. Using basic customer data.</span>
                               </div>
                             </div>
                           )}
                           
                           {/* Template forms */}
                         <div className="border-t pt-4">
                           {selectedTemplate.document_type === 'inspectie' ? (
                             <InspectionTemplateForm
                               template={selectedTemplate}
                               customer={customerInfo || undefined}
                               onSave={handleTemplateCompletion}
                               onCancel={() => setSelectedTemplate(null)}
                             />
                           ) : (
                             <TemplateFormEditor
                               template={selectedTemplate}
                               customer={customerInfo || undefined}
                               onSave={handleTemplateCompletion}
                               onCancel={() => setSelectedTemplate(null)}
                             />
                           )}
                         </div>
                       </>
                     )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Document Configuration Section - Always visible regardless of mode */}
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <FaEdit className="mr-2 text-green-600" />
              Document Configuration
            </h3>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Type
                  </label>
                  <div className="relative">
                    <select
                      value={event.event_type || ''}
                      onChange={handleEventTypeChange}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={submitting}
                    >
                      <option value="">Select Event Type</option>
                      <option value="offerte">Offerte</option>
                      <option value="werkbon">Werkbon</option>
                      <option value="onderhoudsbon">Onderhoudsbon</option>
                      <option value="onderhoudscontract">Onderhoudscontract</option>
                      <option value="meldkamercontract">Meldkamercontract</option>
                      <option value="beveiligingscertificaat">Beveiligingscertificaat</option>
                      <option value="intakedocument">Intakedocument</option>
                      <option value="projectietekening">Projectietekening</option>
                      <option value="beveiligingsplan">Beveiligingsplan</option>
                      <option value="kabeltekeningen">Kabeltekeningen</option>
                      <option value="checklist oplevering installatie">Checklist Oplevering</option>
                      <option value="vrije_documenten">Vrije Documenten</option>
                      <option value="factuur">Factuur</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <FaChevronDown className="h-4 w-4" />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expiry Type
                  </label>
                  <div className="relative">
                    <select
                      value={expiryType}
                      onChange={(e) => {
                        setExpiryType(e.target.value as "date" | "niet_van_toepassing");
                        if (e.target.value === "niet_van_toepassing") {
                          setNewExpiryDate("");
                        } else if (e.target.value === "date" && event.event_type === 'onderhoudsbon') {
                          setNewExpiryDate(getTwelveMonthExpiryDate());
                        }
                      }}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={submitting}
                    >
                      <option value="date">Date</option>
                      <option value="niet_van_toepassing">Not Applicable</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <FaChevronDown className="h-4 w-4" />
                    </div>
                  </div>
                </div>
              </div>

              {expiryType === "date" && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Expiry Date <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <FaCalendarAlt className="w-5 h-5 text-gray-400" />
                    </div>
                    <input
                      type="datetime-local"
                      value={newExpiryDate}
                      onChange={(e) => setNewExpiryDate(e.target.value)}
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={submitting}
                    />
                  </div>
                  {event.event_type === 'onderhoudsbon' && (
                    <p className="text-xs text-blue-600 mt-1 flex items-center">
                      <FaInfoCircle className="mr-1" size={12} />
                      Automatically set to 12 months from today for onderhoudsbon documents
                    </p>
                  )}
                </div>
              )}

              <div className="mt-4">
                <div className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    id="useVersionStatus"
                    checked={useVersionStatus}
                    onChange={(e) => {
                      setUseVersionStatus(e.target.checked);
                      setFormModified(true);
                    }}
                    disabled={submitting}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="useVersionStatus" className="ml-2 block text-sm font-medium text-gray-700">
                    Use Version Status
                  </label>
                </div>
                {useVersionStatus && (
                  <div className="relative">
                    <select
                      value={versionStatus}
                      onChange={(e) => {
                        setVersionStatus(e.target.value as "active" | "inactive");
                        setFormModified(true);
                      }}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      disabled={submitting}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <FaChevronDown className="h-4 w-4" />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons - Show for both modes */}
        <div className="bg-gray-50 border-t border-gray-200 p-6 -mx-6 -mb-6">
          <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
            <button
              type="button"
              onClick={() => handleButtonClick('close')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              disabled={submitting}
            >
              <FaTimes className="mr-2 h-4 w-4" />
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleButtonClick('complete-with-file')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
              disabled={submitting || (completionMode === 'template' && !selectedTemplate)}
            >
              {submitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <FaCheckCircle className="mr-2 h-4 w-4" />
                  {completionMode === 'template'
                    ? "Complete with Template"
                    : "Complete Event"}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HandleEventModal;
