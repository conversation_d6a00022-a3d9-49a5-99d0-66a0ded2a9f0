// frontend/src/pages/AdminDashboard.tsx
import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import { useConfirmation } from "../context/ConfirmationContext";
import { useNavigate } from "react-router-dom";
import { getUpcomingExpirations, getExpiredDocuments } from "../services/documentService";
import { getDashboardMetrics, DashboardMetrics, createEvent } from "../services/eventService";
import { getRecentActivities, Activity } from "../services/activityService";
import { getAllCustomersNoPage } from "../services/customerService";
import { getAllUsers } from "../services/userService";
import { Document } from "../types/document";
import { Customer } from "../types/customer";
import { User } from "../types/user";
import EventModal from "../components/EventModal";
import LoadingSpinner from '../components/LoadingSpinner';
import { toast } from 'react-toastify';
import MetricsCard from "../components/dashboard/MetricsCard";
import ChartWidget from "../components/dashboard/ChartWidget";
import ActivityWidget from "../components/dashboard/ActivityWidget";
import QuickActionsWidget from "../components/dashboard/QuickActionsWidget";
import ExpiryTimeline from "../components/dashboard/ExpiryTimeline";
import { 
  FaUserCog, 
  FaUsers, 
  FaBuilding, 
  FaCalendarAlt, 
  FaExclamationTriangle, 
  FaPlus, 
  FaChartBar,
  FaClipboardCheck, 
  FaClock, 
  FaFileAlt, 
  FaFile, 
  FaExclamation, 
  FaListAlt, 
  FaUserClock,
  FaSignOutAlt,
  FaTachometerAlt,
  FaShieldAlt,
  FaBell,
  FaCheckCircle,
  FaTimesCircle,
  FaArrowRight
} from "react-icons/fa";
import { formatDateTimeForBackend, getCurrentDateTimeForInput } from '../utils/dateUtils';

const AdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { showConfirmation } = useConfirmation();
  const navigate = useNavigate();
  const [upcomingDocuments, setUpcomingDocuments] = useState<Document[]>([]);
  const [expiredDocuments, setExpiredDocuments] = useState<Document[]>([]);
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // New state for EventModal
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [showEventModal, setShowEventModal] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [newEvent, setNewEvent] = useState({
    customer_id: null as number | null,
    event_type: '',
    description: '',
    scheduled_date: '',
    user_ids: [] as number[],
    user_id: null as number | null
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        // Fetch upcoming expirations
        const expirationsResponse = await getUpcomingExpirations();
        setUpcomingDocuments(expirationsResponse.documents);

        // Fetch expired documents
        const expiredResponse = await getExpiredDocuments();
        setExpiredDocuments(expiredResponse.documents);

        // Fetch dashboard metrics
        const metricsResponse = await getDashboardMetrics();
        setMetrics(metricsResponse);

        // Fetch recent activities
        const activitiesResponse = await getRecentActivities(10);
        setRecentActivities(activitiesResponse.activities);

        // Fetch customers and users for EventModal
        const customersResponse = await getAllCustomersNoPage();
        setCustomers(customersResponse.customers);

        const usersResponse = await getAllUsers();
        setUsers(usersResponse.users);
      } catch (err) {
        console.error('Error:', err);
        setError("Failed to fetch dashboard data");
      } finally {
        setLoading(false);
      }
    };
    fetchDashboardData();
  }, []);

  if (loading) {
    return <LoadingSpinner message="Loading dashboard data..." />;
  }

  const handleLogout = async () => {
    showConfirmation({
      title: "Logout Confirmation",
      message: "Are you sure you want to log out?",
      confirmText: "Logout",
      confirmButtonClass: "bg-blue-600 hover:bg-blue-700",
      onConfirm: async () => {
        await logout();
        navigate("/login");
      }
    });
  };

  const handleEventCreated = () => {
    const refreshData = async () => {
      try {
        // Refresh upcoming expirations
        const expirationsResponse = await getUpcomingExpirations();
        setUpcomingDocuments(expirationsResponse?.documents || []);

        // Refresh expired documents
        const expiredResponse = await getExpiredDocuments();
        setExpiredDocuments(expiredResponse?.documents || []);

        // Refresh metrics
        const metricsResponse = await getDashboardMetrics();
        setMetrics(metricsResponse);
      } catch (err) {
        setError("Failed to refresh dashboard data");
        console.error(err);
      }
    };
    refreshData();
  };

  const handleCreateEventFromDocument = (document: Document) => {
    // Store the selected document for later reference
    setSelectedDocument(document);

    // Determine event type based on document
    const isQuotation = document?.document_type === 'offerte';
    const eventType = isQuotation ? 'checklist oplevering installatie' : document.document_type;

    // Set default description
    let defaultDescription = '';
    if (isQuotation) {
      defaultDescription = `Installatie voor offerte (document ID: ${document.id})`;
    } else {
      defaultDescription = `Renew document: ${document.document_type} for customer ID: ${document.customer_id}`;
    }

    // Set up the new event
    setNewEvent({
      customer_id: document.customer_id,
      event_type: eventType,
      description: defaultDescription,
      scheduled_date: getCurrentDateTimeForInput(),
      user_ids: [],
      user_id: null
    });

    setShowEventModal(true);
  };

  const handleCreateEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const formattedDate = formatDateTimeForBackend(newEvent.scheduled_date);
      await createEvent(
        newEvent.customer_id,
        newEvent.event_type,
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : null
      );

      // Immediately remove the document from the lists to provide instant feedback
      if (selectedDocument) {
        setUpcomingDocuments(prev => prev.filter(doc => doc.id !== selectedDocument.id));
        setExpiredDocuments(prev => prev.filter(doc => doc.id !== selectedDocument.id));
      }

      setShowEventModal(false);
      setSelectedDocument(null);
      handleEventCreated();
      toast.success('Event created successfully - Document removed from expiry list');
    } catch (err) {
      console.error('Error creating event:', err);
      setError('Failed to create event');
      toast.error('Failed to create event');
      // Don't remove the document from the list if event creation failed
      // The selectedDocument will remain so user can try again
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Header */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-16 w-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <FaTachometerAlt className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="ml-6">
                    <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                      Admin Dashboard
                    </h1>
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <FaUserCog className="mr-2" />
                      <span className="font-medium">Welcome, {user?.name || user?.email}</span>
                      <span className="mx-2">•</span>
                      <span>System Overview</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-6 lg:mt-0 lg:ml-6">
                <button
                  onClick={handleLogout}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <FaSignOutAlt className="mr-2 h-4 w-4" />
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard Metrics */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <FaChartBar className="mr-2 h-5 w-5 text-blue-600" />
              Dashboard Metrics
            </h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Total Events Card */}
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer" onClick={() => navigate('/events')}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600">Total Events</p>
                    <p className="text-2xl font-bold text-blue-900">{metrics?.events.total || 0}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-500 rounded-lg flex items-center justify-center">
                    <FaCalendarAlt className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="mt-3 flex justify-between text-xs text-blue-700">
                  <span className="flex items-center">
                    <FaClock className="mr-1" />
                    Pending: {metrics?.events.pending || 0}
                  </span>
                  <span className="flex items-center">
                    <FaCheckCircle className="mr-1" />
                    Completed: {metrics?.events.completed || 0}
                  </span>
                </div>
              </div>

              {/* Upcoming Events Card */}
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer" onClick={() => navigate('/calendar')}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600">Upcoming Events</p>
                    <p className="text-2xl font-bold text-orange-900">{metrics?.events.upcoming || 0}</p>
                  </div>
                  <div className="h-12 w-12 bg-orange-500 rounded-lg flex items-center justify-center">
                    <FaClock className="h-6 w-6 text-white" />
                  </div>
                </div>
                <p className="mt-3 text-xs text-orange-700">Events in next 7 days</p>
              </div>

              {/* Users Card */}
              <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer" onClick={() => navigate('/users')}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600">Total Users</p>
                    <p className="text-2xl font-bold text-green-900">{metrics?.users.total || 0}</p>
                  </div>
                  <div className="h-12 w-12 bg-green-500 rounded-lg flex items-center justify-center">
                    <FaUsers className="h-6 w-6 text-white" />
                  </div>
                </div>
                <p className="mt-3 text-xs text-green-700">Active system users</p>
              </div>

              {/* Customers Card */}
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer" onClick={() => navigate('/customers')}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600">Total Customers</p>
                    <p className="text-2xl font-bold text-purple-900">{metrics?.customers.total || 0}</p>
                  </div>
                  <div className="h-12 w-12 bg-purple-500 rounded-lg flex items-center justify-center">
                    <FaBuilding className="h-6 w-6 text-white" />
                  </div>
                </div>
                <p className="mt-3 text-xs text-purple-700">Registered customers</p>
              </div>
            </div>
          </div>
        </div>

        {/* Document Metrics */}
        {metrics?.documents && (
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <FaFile className="mr-2 h-5 w-5 text-blue-600" />
                Document Metrics
              </h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                {/* Total Active Documents */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active Documents</p>
                      <p className="text-2xl font-bold text-gray-900">{metrics.documents.total_active || 0}</p>
                    </div>
                    <div className="h-12 w-12 bg-gray-500 rounded-lg flex items-center justify-center">
                      <FaFile className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <p className="mt-2 text-xs text-gray-600">Total active documents in system</p>
                </div>

                {/* Expiring Documents */}
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-orange-600">Expiring Soon</p>
                      <p className="text-2xl font-bold text-orange-900">{metrics.documents.expiring_soon || 0}</p>
                    </div>
                    <div className="h-12 w-12 bg-orange-500 rounded-lg flex items-center justify-center">
                      <FaExclamation className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <p className="mt-2 text-xs text-orange-700">Documents expiring in next 60 days</p>
                </div>

                {/* Expired Documents */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-red-600">Expired Documents</p>
                      <p className="text-2xl font-bold text-red-900">{metrics.documents.expired || 0}</p>
                    </div>
                    <div className="h-12 w-12 bg-red-500 rounded-lg flex items-center justify-center">
                      <FaExclamationTriangle className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <p className="mt-2 text-xs text-red-700">Documents that have already expired</p>
                </div>
              </div>

              {/* Document Status Distribution */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Document Status Chart */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <ChartWidget
                    title="Documents by Status"
                    type="doughnut"
                    data={{
                      labels: ['Critical', 'Warning', 'Good'],
                      datasets: [
                        {
                          data: [
                            metrics.documents.by_status.red || 0,
                            metrics.documents.by_status.orange || 0,
                            metrics.documents.by_status.green || 0
                          ],
                          backgroundColor: ['#EF4444', '#F59E0B', '#10B981'],
                          borderColor: ['#EF4444', '#F59E0B', '#10B981'],
                          borderWidth: 1,
                        },
                      ],
                    }}
                    options={{
                      plugins: {
                        legend: {
                          position: 'bottom',
                        },
                      },
                      cutout: '60%',
                    }}
                    height={250}
                  />
                </div>

                {/* Document Expiry Timeline */}
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <ExpiryTimeline
                    documents={upcomingDocuments}
                    maxItems={5}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Event Types Distribution and Activity */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Event Types Chart */}
          {metrics?.events.by_type && Object.keys(metrics.events.by_type).length > 0 && (
            <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Events by Type</h3>
              </div>
              <div className="p-6">
                <ChartWidget
                  title=""
                  type="bar"
                  data={{
                    labels: Object.keys(metrics.events.by_type),
                    datasets: [
                      {
                        label: 'Event Count',
                        data: Object.values(metrics.events.by_type),
                        backgroundColor: '#3B82F6',
                        borderColor: '#2563EB',
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    plugins: {
                      legend: {
                        display: false,
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          precision: 0
                        }
                      }
                    }
                  }}
                  height={300}
                />
              </div>
            </div>
          )}

          {/* Recent Activity */}
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
            </div>
            <div className="p-6">
              <ActivityWidget
                activities={recentActivities}
                maxItems={5}
                onViewAll={() => navigate('/audit')}
              />
            </div>
          </div>
        </div>

        {/* Quick Actions Widget */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <FaShieldAlt className="mr-2 h-5 w-5 text-blue-600" />
              Quick Actions
            </h2>
          </div>
          <div className="p-6">
            <QuickActionsWidget
              actions={[
                {
                  id: 'users',
                  title: 'Manage Users',
                  icon: <FaUsers size={24} />,
                  path: '/users',
                  color: 'text-blue-500'
                },
                {
                  id: 'customers',
                  title: 'Manage Customers',
                  icon: <FaBuilding size={24} />,
                  path: '/customers',
                  color: 'text-green-500'
                },
                {
                  id: 'calendar',
                  title: 'Calendar View',
                  icon: <FaCalendarAlt size={24} />,
                  path: '/calendar',
                  color: 'text-purple-500'
                },
                {
                  id: 'templates',
                  title: 'Document Templates',
                  icon: <FaFileAlt size={24} />,
                  path: '/document-templates',
                  color: 'text-orange-500'
                },
                {
                  id: 'events',
                  title: 'Manage Events',
                  icon: <FaClock size={24} />,
                  path: '/events',
                  color: 'text-red-500'
                },
                {
                  id: 'timetracking',
                  title: 'Time Tracking',
                  icon: <FaUserClock size={24} />,
                  path: '/time-tracking-admin',
                  color: 'text-teal-500'
                },
                {
                  id: 'audit',
                  title: 'Audit Logs',
                  icon: <FaListAlt size={24} />,
                  path: '/audit',
                  color: 'text-gray-500'
                }
              ]}
            />
          </div>
        </div>

        {/* Upcoming Document Expirations */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <FaBell className="mr-2 h-5 w-5 text-orange-500" />
              Upcoming Document Expirations
            </h2>
          </div>
          <div className="p-6">
            {error ? (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <FaExclamationTriangle className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            ) : upcomingDocuments.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {upcomingDocuments.slice(0, 6).map((doc) => (
                  <div
                    key={doc.id}
                    onClick={() => handleCreateEventFromDocument(doc)}
                    className={`bg-white border rounded-lg p-4 hover:shadow-md transition-shadow duration-300 cursor-pointer ${
                      doc.expiry_status === "red" ? "border-red-300 bg-red-50" : 
                      doc.expiry_status === "orange" ? "border-orange-300 bg-orange-50" : 
                      "border-green-300 bg-green-50"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        <FaExclamationTriangle className={`mr-2 ${
                          doc.expiry_status === "red" ? "text-red-500" : 
                          doc.expiry_status === "orange" ? "text-orange-500" : 
                          "text-green-500"
                        }`} />
                        <span className="font-semibold text-gray-900">{doc.document_type}</span>
                      </div>
                      <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                        doc.expiry_status === "red" ? "bg-red-100 text-red-700" : 
                        doc.expiry_status === "orange" ? "bg-orange-100 text-orange-700" : 
                        "bg-green-100 text-green-700"
                      }`}>
                        {doc.expiry_status === "red" ? "Critical" : 
                         doc.expiry_status === "orange" ? "Warning" : "Good"}
                      </span>
                    </div>
                    <div className="space-y-2 text-sm text-gray-600">
                      <p><span className="font-medium">Customer:</span> {doc.customer_name || `ID: ${doc.customer_id}`}</p>
                      <p><span className="font-medium">Expiry Date:</span> {doc.expiry_date ? new Date(doc.expiry_date).toLocaleDateString() : "N/A"}</p>
                    </div>
                    <div className="mt-4 flex justify-end">
                      <button
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCreateEventFromDocument(doc);
                        }}
                      >
                        <FaPlus className="mr-1 h-3 w-3" />
                        Create Event
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FaCheckCircle className="mx-auto h-12 w-12 text-green-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No upcoming expirations</h3>
                <p className="text-gray-500">All documents are up to date!</p>
              </div>
            )}
          </div>
        </div>

        {/* Expired Documents */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <FaTimesCircle className="mr-2 h-5 w-5 text-red-500" />
              Expired Documents
            </h2>
          </div>
          <div className="p-6">
            {error ? (
              <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <FaExclamationTriangle className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            ) : expiredDocuments.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {expiredDocuments.slice(0, 6).map((doc) => (
                  <div
                    key={doc.id}
                    onClick={() => handleCreateEventFromDocument(doc)}
                    className="bg-red-50 border border-red-300 rounded-lg p-4 hover:shadow-md transition-shadow duration-300 cursor-pointer"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        <FaExclamationTriangle className="mr-2 text-red-500" />
                        <span className="font-semibold text-gray-900">{doc.document_type}</span>
                      </div>
                      <span className="text-xs font-medium px-2 py-1 rounded-full bg-red-100 text-red-700">
                        Expired
                      </span>
                    </div>
                    <div className="space-y-2 text-sm text-gray-600">
                      <p><span className="font-medium">Customer:</span> {doc.customer_name || `ID: ${doc.customer_id}`}</p>
                      <p><span className="font-medium">Expiry Date:</span> {doc.expiry_date ? new Date(doc.expiry_date).toLocaleDateString() : "N/A"}</p>
                    </div>
                    <div className="mt-4 flex justify-end">
                      <button
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCreateEventFromDocument(doc);
                        }}
                      >
                        <FaPlus className="mr-1 h-3 w-3" />
                        Create Event
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FaCheckCircle className="mx-auto h-12 w-12 text-green-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No expired documents</h3>
                <p className="text-gray-500">All documents are current!</p>
              </div>
            )}
          </div>
        </div>

        {/* Event Modal */}
        {showEventModal && (
          <EventModal
            event={newEvent}
            onClose={() => {
              setShowEventModal(false);
              setSelectedDocument(null);
            }}
            onSubmit={handleCreateEvent}
            setEvent={setNewEvent}
            isEditing={false}
            submitting={submitting}
            customers={customers}
            users={users}
          />
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
