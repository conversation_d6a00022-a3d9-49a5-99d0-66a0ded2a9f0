import React, { useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaArrowLeft, FaSignature, FaEraser, FaCheck, <PERSON>aEye, FaFileAlt } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import {
  MobileContainer,
  MobilePageHeader,
  ProgressSteps,
  CustomerSummary,
  PriceSummary,
  InfoCard,
  EnhancedButton
} from '../components/common/MobileUtils';
import SignatureCanvas from 'react-signature-canvas';
import simpleOfferteService from '../services/simpleOfferteService';

interface CustomerData {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_address: string;
  customer_city: string;
  customer_postal_code: string;
}

const SimpleQuoteSignaturePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const signatureRef = useRef<SignatureCanvas>(null);
  
  // Get quote configuration from previous page
  const quoteConfig = location.state?.quoteConfig;
  
  const [signatureData, setSignatureData] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSignature, setShowSignature] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Redirect if no quote config
  if (!quoteConfig) {
    navigate('/simple-quotes');
    return null;
  }

  const { customerData, category, calculations } = quoteConfig;

  const clearSignature = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
      setSignatureData('');
    }
  };

  const saveSignature = () => {
    if (signatureRef.current) {
      const signature = signatureRef.current.toDataURL();
      setSignatureData(signature);
      setShowSignature(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!signatureData) {
      alert('Handtekening is verplicht');
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert to backend format
      const backendFormat = simpleOfferteService.convertToBackendFormat(
        quoteConfig.category,
        quoteConfig.paymentTerms
      );

      // Create the offerte with customer data
      const offerteData = {
        ...customerData,
        category: backendFormat.category,
        payment_terms: backendFormat.payment_terms,
        discount_percentage: quoteConfig.discountPercentage,
        videodoorbell_free: quoteConfig.videodoorbellFree,
        videodoorbell_paid: quoteConfig.videodoorbellPaid,
        signature_data: signatureData,
        ...quoteConfig.extraProducts
      };

      const result = await simpleOfferteService.createOfferte(offerteData);

      // Navigate to success page
      navigate('/simple-quotes/success', {
        state: {
          customerId: result.customer.id,
          customerName: result.customer.name,
          documentId: result.document.id,
          documentName: result.document.name
        }
      });

    } catch (error) {
      console.error('Error submitting quote:', error);
      alert('Er is een fout opgetreden bij het opslaan van de offerte');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCategoryTitle = () => {
    switch (category) {
      case 'alarm': return 'Alarm Systeem';
      case 'cameras': return 'Camera Systeem';
      case 'alarm_cameras': return 'Alarm + Camera Systeem';
      default: return 'Offerte';
    }
  };

  return (
    <MobileContainer>
      <Breadcrumbs />
      
      <MobilePageHeader
        title="Offerte Ondertekenen"
        subtitle={`Controleer uw offerte en onderteken, ${customerData.customer_name}`}
      />

      <div className="space-y-6">
        {/* Header with back button */}
        <div className="flex items-center justify-between">
          <EnhancedButton
            variant="outline"
            size="sm"
            onClick={() => navigate(-1)}
            icon={<FaArrowLeft />}
          >
            Terug naar configuratie
          </EnhancedButton>
        </div>

        {/* Progress indicator */}
        <ProgressSteps steps={[
          { id: 'customer', label: 'Klantgegevens', status: 'completed' },
          { id: 'system', label: 'Systeem kiezen', status: 'completed' },
          { id: 'config', label: 'Configureren', status: 'completed' },
          { id: 'signature', label: 'Ondertekenen', status: 'current' }
        ]} />

        {/* Customer summary */}
        <CustomerSummary
          name={customerData.customer_name}
          email={customerData.customer_email}
          phone={customerData.customer_phone}
          address={customerData.customer_address}
          city={customerData.customer_city}
          systemType={getCategoryTitle()}
        />

        {/* Quote summary */}
        {calculations && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900 dark:text-dark-text flex items-center text-lg">
                <FaFileAlt className="mr-2 text-blue-600" />
                Offerte Overzicht
              </h3>
              <EnhancedButton
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
                icon={<FaEye />}
              >
                {showPreview ? 'Verberg details' : 'Toon details'}
              </EnhancedButton>
            </div>

            <PriceSummary
              items={[
                { label: 'Basis pakket', value: 999.99 },
                ...(calculations.selected_extra_products?.map((product: any) => ({
                  label: `${product.quantity}x ${product.name}`,
                  value: product.total_price
                })) || []),
                ...(calculations.discount_amount > 0 ? [{
                  label: 'Korting',
                  value: -calculations.discount_amount,
                  highlight: true
                }] : []),
                { label: 'Apparatuur (maandelijks)', value: calculations.monthly_equipment_cost, subtext: 'Per maand' },
                { label: 'Meldkamer (maandelijks)', value: calculations.monthly_monitoring_cost, subtext: 'Per maand' },
                { label: 'Onderhoud (maandelijks)', value: calculations.monthly_maintenance_cost, subtext: 'Per maand' }
              ]}
              total={{
                label: 'Totaal overzicht',
                value: `€${calculations.final_installation_cost.toFixed(2)} + €${calculations.total_monthly_cost.toFixed(2)}/maand`,
                subtext: `${getCategoryTitle()} - Klaar voor ondertekening`
              }}
            />

            {showPreview && (
              <InfoCard
                title="Gedetailleerde informatie"
                icon={<FaFileAlt />}
                variant="info"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Klantgegevens:</h4>
                    <div className="space-y-2 text-sm">
                      <p><strong>Naam:</strong> {customerData.customer_name}</p>
                      {customerData.customer_email && <p><strong>Email:</strong> {customerData.customer_email}</p>}
                      {customerData.customer_phone && <p><strong>Telefoon:</strong> {customerData.customer_phone}</p>}
                      {customerData.customer_address && <p><strong>Adres:</strong> {customerData.customer_address}</p>}
                      {customerData.customer_city && <p><strong>Plaats:</strong> {customerData.customer_city}</p>}
                      {customerData.customer_postal_code && <p><strong>Postcode:</strong> {customerData.customer_postal_code}</p>}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Systeem details:</h4>
                    <div className="space-y-2 text-sm">
                      <p><strong>Systeem:</strong> {getCategoryTitle()}</p>
                      <p><strong>Installatiekosten:</strong> €{calculations.final_installation_cost.toFixed(2)}</p>
                      <p><strong>Maandelijkse kosten:</strong> €{calculations.total_monthly_cost.toFixed(2)}</p>
                      {calculations.discount_amount > 0 && (
                        <p><strong>Toegepaste korting:</strong> €{calculations.discount_amount.toFixed(2)}</p>
                      )}
                    </div>
                  </div>
                </div>
              </InfoCard>
            )}
          </div>
        )}

        {/* Important information before signing */}
        <InfoCard
          title="Belangrijk - Lees voordat u ondertekent"
          icon={<FaCheck />}
          variant="warning"
        >
          <ul className="space-y-2 text-sm">
            <li className="flex items-start">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
              <span>Door te ondertekenen gaat u akkoord met de offerte en de algemene voorwaarden</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
              <span>De offerte is 30 dagen geldig vanaf vandaag</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
              <span>U heeft 14 dagen bedenktijd na ondertekening</span>
            </li>
            <li className="flex items-start">
              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
              <span>Installatie wordt binnen 2-3 weken na ondertekening gepland</span>
            </li>
          </ul>
        </InfoCard>

        {/* Signature section */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white dark:bg-dark-secondary rounded-xl shadow-sm border border-gray-200 dark:border-dark-border p-4 sm:p-6">
            <h3 className="font-semibold text-gray-900 dark:text-dark-text mb-6 flex items-center text-base sm:text-lg">
              <FaSignature className="mr-3 text-blue-600" />
              Digitale Handtekening *
            </h3>

            {!signatureData ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaSignature className="text-2xl text-blue-600 dark:text-blue-400" />
                </div>
                <p className="text-gray-600 dark:text-dark-text-light mb-6">
                  Plaats uw handtekening om de offerte te bevestigen
                </p>
                <EnhancedButton
                  type="button"
                  variant="primary"
                  size="lg"
                  onClick={() => setShowSignature(true)}
                  icon={<FaSignature />}
                >
                  Handtekening plaatsen
                </EnhancedButton>
              </div>
            ) : (
              <div className="text-center">
                <div className="mb-6 inline-block border-2 border-green-200 rounded-lg p-4 bg-green-50 dark:bg-green-900/20">
                  <img
                    src={signatureData}
                    alt="Handtekening"
                    className="max-w-full h-32 object-contain"
                  />
                </div>
                <div className="flex items-center justify-center">
                  <FaCheck className="text-green-600 mr-2" />
                  <span className="text-green-600 font-medium mr-4">Handtekening geplaatst</span>
                  <EnhancedButton
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSignatureData('');
                      setShowSignature(true);
                    }}
                    icon={<FaEraser />}
                  >
                    Wijzigen
                  </EnhancedButton>
                </div>
              </div>
            )}
          </div>

          {/* Submit button */}
          <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3 bg-white dark:bg-dark-secondary rounded-xl shadow-sm border border-gray-200 dark:border-dark-border p-4">
            <EnhancedButton
              type="button"
              variant="outline"
              size="md"
              onClick={() => navigate(-1)}
              icon={<FaArrowLeft />}
              className="order-2 sm:order-1"
            >
              Terug naar configuratie
            </EnhancedButton>

            <EnhancedButton
              type="submit"
              variant="success"
              size="lg"
              disabled={isSubmitting || !signatureData}
              loading={isSubmitting}
              icon={isSubmitting ? undefined : <FaCheck />}
              className="order-1 sm:order-2"
              fullWidth={true}
            >
              {isSubmitting ? 'Bezig met opslaan...' : 'Offerte ondertekenen en opslaan'}
            </EnhancedButton>
          </div>
        </form>
      </div>

      {/* Signature modal */}
      {showSignature && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50 p-0 sm:p-4">
          <div className="bg-white dark:bg-dark-secondary rounded-t-xl sm:rounded-xl p-4 sm:p-6 w-full sm:max-w-lg shadow-2xl mobile-modal-content">
            {/* Mobile handle */}
            <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4 sm:hidden"></div>

            <h3 className="font-semibold text-gray-900 dark:text-dark-text mb-4 text-lg text-center">
              Plaats uw handtekening
            </h3>

            <p className="text-sm text-gray-600 dark:text-dark-text-light text-center mb-4">
              Teken met uw vinger of stylus in het vak hieronder
            </p>

            <div className="border-2 border-gray-300 dark:border-dark-border rounded-lg mb-6 bg-gray-50 dark:bg-dark-tertiary overflow-hidden">
              <SignatureCanvas
                ref={signatureRef}
                canvasProps={{
                  width: window.innerWidth > 640 ? 400 : Math.min(window.innerWidth - 80, 350),
                  height: 200,
                  className: 'signature-canvas w-full rounded-lg touch-action-none'
                }}
              />
            </div>

            <div className="flex flex-col sm:flex-row justify-between gap-3">
              <EnhancedButton
                type="button"
                variant="danger"
                size="md"
                onClick={clearSignature}
                icon={<FaEraser />}
                className="order-3 sm:order-1"
              >
                Wissen
              </EnhancedButton>

              <div className="flex gap-3 order-1 sm:order-2">
                <EnhancedButton
                  type="button"
                  variant="outline"
                  size="md"
                  onClick={() => setShowSignature(false)}
                  fullWidth={true}
                >
                  Annuleren
                </EnhancedButton>
                <EnhancedButton
                  type="button"
                  variant="success"
                  size="md"
                  onClick={saveSignature}
                  icon={<FaCheck />}
                  fullWidth={true}
                >
                  Opslaan
                </EnhancedButton>
              </div>
            </div>
          </div>
        </div>
      )}
    </MobileContainer>
  );
};

export default SimpleQuoteSignaturePage;
