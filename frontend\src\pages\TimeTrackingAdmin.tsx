import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { getAllUsers } from '../services/userService';
import { getPendingTimeEntries, getPendingMileageEntries } from '../services/timeTrackingService';
import { User } from '../types/user';
import LoadingSpinner from '../components/LoadingSpinner';
import { FaUserClock, FaSearch, FaEye, FaCheckCircle, FaTimesCircle, FaClock, FaCar, FaFilter } from 'react-icons/fa';
import { MobileContainer, MobileCard, MobileSearch, MobilePageHeader, MobileButtonGroup } from '../components/common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

const TimeTrackingAdmin: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { isMobile } = useMobile();

  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pendingTimeCount, setPendingTimeCount] = useState(0);
  const [pendingMileageCount, setPendingMileageCount] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch users
        const usersResponse = await getAllUsers();
        setUsers(usersResponse.users);

        // Fetch pending time entries count
        const timeResponse = await getPendingTimeEntries(1, 1);
        setPendingTimeCount(timeResponse.total);

        // Fetch pending mileage entries count
        const mileageResponse = await getPendingMileageEntries(1, 1);
        setPendingMileageCount(mileageResponse.total);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Er is een fout opgetreden bij het ophalen van de gegevens.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleUserClick = (userId: number) => {
    navigate(`/time-tracking-admin/user/${userId}`);
  };

  const filteredUsers = searchTerm
    ? users.filter(user =>
        (user.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (user.email.toLowerCase()).includes(searchTerm.toLowerCase())
      )
    : users;

  if (!user || user.role !== 'administrator') {
    return (
      <MobileContainer>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Je hebt geen toegang tot deze pagina. Alleen administrators kunnen deze pagina bekijken.
        </div>
      </MobileContainer>
    );
  }

  return (
    <MobileContainer>
      <MobilePageHeader
        title="Uren en kilometers beheer"
        subtitle={
          (pendingTimeCount > 0 || pendingMileageCount > 0) ? (
            <div className="flex flex-col sm:flex-row gap-2 mt-2">
              {pendingTimeCount > 0 && (
                <div className="bg-blue-50 border border-blue-200 text-blue-800 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-200 px-3 py-1 rounded-lg flex items-center text-sm">
                  <FaClock className="mr-2" />
                  <span className="font-semibold">{pendingTimeCount}</span> uren wachtend
                </div>
              )}
              {pendingMileageCount > 0 && (
                <div className="bg-green-50 border border-green-200 text-green-800 dark:bg-green-900 dark:border-green-700 dark:text-green-200 px-3 py-1 rounded-lg flex items-center text-sm">
                  <FaCar className="mr-2" />
                  <span className="font-semibold">{pendingMileageCount}</span> kilometers wachtend
                </div>
              )}
            </div>
          ) : undefined
        }
      />

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-lg shadow-sm mb-6 flex items-start">
          <div className="bg-red-100 p-1 rounded-full mr-3 mt-0.5">
            <FaTimesCircle className="text-red-600" />
          </div>
          <div>{error}</div>
        </div>
      )}

      <MobileCard>
        <div className="mobile-header">
          <h2 className="responsive-subheading">Gebruikers</h2>
        </div>

        <MobileSearch
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Zoek gebruiker..."
          className="mb-4"
        />

        {loading ? (
          <LoadingSpinner />
        ) : filteredUsers.length > 0 ? (
          <>
            {/* Desktop table view */}
            <div className="hidden md:block overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Naam
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Email
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Rol
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Acties
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredUsers.map(user => (
                    <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text">
                        {user.name || <span className="text-gray-500 italic">Geen naam</span>}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                        {user.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                        <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 capitalize">
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                        <button
                          onClick={() => handleUserClick(user.id)}
                          className="inline-flex items-center px-3 py-1.5 border border-blue-600 text-blue-600 bg-white hover:bg-blue-50 dark:bg-transparent dark:text-blue-400 dark:border-blue-400 dark:hover:bg-blue-900/30 rounded-md transition-colors"
                        >
                          <FaEye className="mr-2" /> Bekijk uren
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile card view */}
            <div className="md:hidden space-y-3">
              {filteredUsers.map(user => (
                <div key={user.id} className="mobile-card-enhanced">
                  <div className="mobile-card-header">
                    <div className="flex-1">
                      <h3 className="mobile-card-title">
                        {user.name || <span className="text-gray-500 italic">Geen naam</span>}
                      </h3>
                      <p className="mobile-card-value text-sm mt-1">{user.email}</p>
                    </div>
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 capitalize">
                      {user.role}
                    </span>
                  </div>
                  <div className="mobile-card-actions">
                    <button
                      onClick={() => handleUserClick(user.id)}
                      className="btn btn-primary mobile-touch-target w-full"
                    >
                      <FaEye className="mr-2" /> Bekijk uren
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <p className="text-gray-500 dark:text-gray-400 text-center py-4">
            {searchTerm ? 'Geen gebruikers gevonden die overeenkomen met je zoekopdracht.' : 'Geen gebruikers gevonden.'}
          </p>
        )}
      </MobileCard>

      <MobileCard>
        <div className="mobile-header">
          <h2 className="responsive-subheading">Snelle acties</h2>
        </div>

        <div className="responsive-grid">
          <div
            className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 p-5 rounded-lg border border-blue-200 dark:border-blue-700 shadow-sm hover:shadow-md transition-all transform hover:-translate-y-1 cursor-pointer"
            onClick={() => navigate('/time-tracking-admin/pending')}
          >
            <div className="flex items-center mb-3">
              <div className="bg-blue-500 text-white p-2 rounded-lg mr-3">
                <FaCheckCircle size={20} />
              </div>
              <h3 className="font-semibold text-lg text-blue-800 dark:text-blue-200">Goedkeuren</h3>
            </div>
            <p className="text-blue-700 dark:text-blue-300 ml-10">
              Bekijk en keur uren en kilometers goed
            </p>
            {(pendingTimeCount > 0 || pendingMileageCount > 0) && (
              <div className="mt-3 ml-10 flex items-center">
                <span className="bg-blue-200 dark:bg-blue-700 text-blue-800 dark:text-blue-200 text-xs font-semibold px-2.5 py-1 rounded-full">
                  {pendingTimeCount + pendingMileageCount} wachtend
                </span>
              </div>
            )}
          </div>

          <div
            className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 p-5 rounded-lg border border-green-200 dark:border-green-700 shadow-sm hover:shadow-md transition-all transform hover:-translate-y-1 cursor-pointer"
            onClick={() => navigate('/time-tracking-admin/reports')}
          >
            <div className="flex items-center mb-3">
              <div className="bg-green-500 text-white p-2 rounded-lg mr-3">
                <FaFilter size={20} />
              </div>
              <h3 className="font-semibold text-lg text-green-800 dark:text-green-200">Rapportages</h3>
            </div>
            <p className="text-green-700 dark:text-green-300 ml-10">
              Bekijk overzichten en rapportages
            </p>
          </div>
        </div>
      </MobileCard>
    </MobileContainer>
  );
};

export default TimeTrackingAdmin;