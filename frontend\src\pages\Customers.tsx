// frontend/src/pages/Customers.tsx
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { getAllCustomers, createCustomer, updateCustomer, deleteCustomer, searchCustomers, bulkDeleteCustomers, deleteAllCustomers, getCustomerLinkedDataCount } from "../services/customerService";
import { Customer } from "../types/customer";
import LoadingSpinner from '../components/LoadingSpinner';
import CustomerModal from '../components/CustomerModal';
import Pagination from '../components/Pagination';
import ExportImportCustomers from '../components/ExportImportCustomers';
import CustomerDeletionDialog from '../components/CustomerDeletionDialog';
import { useConfirmation } from '../context/ConfirmationContext';
import { useAuth } from '../context/AuthContext';
import { 
  FaPlus, 
  FaFileExport, 
  FaTrash, 
  FaSearch, 
  FaBuilding, 
  FaUsers, 
  FaFilter,
  FaEye,
  FaEdit,
  FaFileAlt,
  FaCalendarAlt,
  FaArrowRight,
  FaTimes,
  FaExclamationTriangle,
  FaCheckCircle
} from "react-icons/fa";
import { useMobile } from '../hooks/useMobile';

const Customers: React.FC = () => {
  const { showConfirmation } = useConfirmation();
  const { user } = useAuth();
  const { isMobile } = useMobile();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [totalItems, setTotalItems] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [newCustomer, setNewCustomer] = useState<Partial<Customer>>({});
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [showExportImport, setShowExportImport] = useState(false);
  const [selectedCustomers, setSelectedCustomers] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [showDeletionDialog, setShowDeletionDialog] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<{ id: number; name: string } | null>(null);

  const fetchCustomers = async (page: number, perPage: number) => {
    try {
      const response = await getAllCustomers(page, perPage);
      setCustomers(response.customers);
      setTotalItems(response.total);
      setSelectedCustomers(new Set()); // Clear selection when fetching new customers
      setLoading(false);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch customers");
      setLoading(false);
    }
  };

  useEffect(() => {
    if (searchTerm.trim() === '') {
      fetchCustomers(currentPage, itemsPerPage);
    } else if (searchTerm.trim().length >= 2) {
      // Perform search when search term is at least 2 characters
      const performSearch = async () => {
        setIsSearching(true);
        try {
          const response = await searchCustomers(searchTerm);
          setCustomers(response.customers);
          setSelectedCustomers(new Set()); // Clear selection when searching
          // When searching, we don't have pagination info
          setTotalItems(response.customers.length);
        } catch (err: any) {
          setError(err.response?.data?.error || "Failed to search customers");
        } finally {
          setIsSearching(false);
        }
      };

      // Debounce the search to avoid too many API calls
      const timeoutId = setTimeout(() => {
        performSearch();
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [currentPage, itemsPerPage, searchTerm]);

  // Update selectAll state based on selected customers
  useEffect(() => {
    if (customers.length === 0) {
      setSelectAll(false);
    } else {
      setSelectAll(selectedCustomers.size === customers.length);
    }
  }, [selectedCustomers, customers]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (perPage: number) => {
    setItemsPerPage(perPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    try {
      const customer = await createCustomer(newCustomer);
      setCustomers([...customers, customer]);
      setNewCustomer({});
      setShowModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to create customer");
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingCustomer) return;
    setSubmitting(true);
    try {
      const customer = await updateCustomer(editingCustomer.id, newCustomer);
      setCustomers(customers.map((c) => (c.id === editingCustomer.id ? customer : c)));
      setEditingCustomer(null);
      setNewCustomer({});
      setShowModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to update customer");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (customerId: number) => {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) return;

    setCustomerToDelete({ id: customerId, name: customer.name });
    setShowDeletionDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!customerToDelete) return;

    setSubmitting(true);
    try {
      await deleteCustomer(customerToDelete.id);
      setCustomers(customers.filter((c) => c.id !== customerToDelete.id));
      setSelectedCustomers(prev => {
        const newSet = new Set(prev);
        newSet.delete(customerToDelete.id);
        return newSet;
      });
      setError(null);
      setShowDeletionDialog(false);
      setCustomerToDelete(null);
    } catch (err: any) {
      console.error('Error deleting customer:', err);
      setError(err.response?.data?.error || "Failed to delete customer. Please make sure all associated data can be removed.");
    } finally {
      setSubmitting(false);
    }
  };

  const handleCloseDeletionDialog = () => {
    setShowDeletionDialog(false);
    setCustomerToDelete(null);
  };

  const handleSelectCustomer = (customerId: number) => {
    setSelectedCustomers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(customerId)) {
        newSet.delete(customerId);
      } else {
        newSet.add(customerId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedCustomers.size === customers.length) {
      setSelectedCustomers(new Set());
    } else {
      setSelectedCustomers(new Set(customers.map(c => c.id)));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedCustomers.size === 0) return;

    const selectedCustomerNames = customers
      .filter(c => selectedCustomers.has(c.id))
      .map(c => c.name)
      .slice(0, 5); // Show first 5 names

    const namesList = selectedCustomerNames.join(', ');
    const additionalCount = selectedCustomers.size - selectedCustomerNames.length;
    const displayNames = additionalCount > 0 ? `${namesList} and ${additionalCount} more` : namesList;

    showConfirmation({
      title: "Delete Selected Customers",
      message: `⚠️ WARNING: You are about to permanently delete ${selectedCustomers.size} customer(s) and ALL their associated data.

Selected customers: ${displayNames}

This will delete for each customer:
• All documents uploaded for the customer
• All events assigned to the customer
• All customer notes
• All quotations for the customer
• Any sub-documents linked to main documents

This action cannot be undone and may take several minutes to complete if there are many associated records.

Are you sure you want to proceed?`,
      confirmText: "Yes, Delete All Selected",
      cancelText: "Cancel",
      confirmButtonClass: "bg-red-600 hover:bg-red-700",
      onConfirm: async () => {
        setSubmitting(true);
        try {
          const result = await bulkDeleteCustomers(Array.from(selectedCustomers));
          setCustomers(customers.filter(c => !selectedCustomers.has(c.id)));
          setSelectedCustomers(new Set());
          setSelectAll(false);
          setError(null);

          if (result.failed > 0) {
            setError(`Successfully deleted ${result.deleted} customers. Failed to delete ${result.failed} customers.`);
          }
        } catch (err: any) {
          console.error('Error bulk deleting customers:', err);
          setError(err.response?.data?.error || "Failed to delete customers. Please try again.");
        } finally {
          setSubmitting(false);
        }
      }
    });
  };

  const handleDeleteAllCustomers = async () => {
    showConfirmation({
      title: "Delete ALL Customers",
      message: `🚨 EXTREME DANGER: This will permanently delete ALL customers in the entire database and ALL their associated data.

This action will delete:
• ALL customer records in the system
• ALL documents for every customer
• ALL events for every customer
• ALL customer notes
• ALL quotations for every customer
• ALL sub-documents linked to main documents

⚠️ This affects ALL customers in the database, not just the ones visible on this page.
⚠️ This operation cannot be undone and may take several minutes to complete.
⚠️ This will essentially reset your entire customer database.

Are you absolutely certain you want to proceed with this destructive action?`,
      confirmText: "Yes, Delete EVERYTHING",
      cancelText: "Cancel",
      confirmButtonClass: "bg-red-600 hover:bg-red-700",
      onConfirm: async () => {
        setSubmitting(true);
        try {
          const result = await deleteAllCustomers();
          setCustomers([]);
          setSelectedCustomers(new Set());
          setSelectAll(false);
          setTotalItems(0);
          setError(null);

          showConfirmation({
            title: "All Customers Deleted",
            message: `Successfully deleted ${result.deleted} customers. ${result.failed > 0 ? `Failed to delete ${result.failed} customers.` : ''}`,
            confirmText: "OK",
            confirmButtonClass: "bg-green-600 hover:bg-green-700",
            onConfirm: () => {}
          });
        } catch (err: any) {
          console.error('Error deleting all customers:', err);
          setError(err.response?.data?.error || "Failed to delete all customers. Please try again.");
        } finally {
          setSubmitting(false);
        }
      }
    });
  };

  if (loading) {
    return <LoadingSpinner message="Loading customers..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Header */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-16 w-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <FaUsers className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="ml-6">
                    <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                      Customer Management
                    </h1>
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <FaBuilding className="mr-2" />
                      <span className="font-medium">{totalItems} customers</span>
                      {selectedCustomers.size > 0 && (
                        <>
                          <span className="mx-2">•</span>
                          <span className="text-blue-600 font-medium">{selectedCustomers.size} selected</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-6 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row gap-3">
                {user?.role === 'administrator' && (
                  <button
                    onClick={() => setShowExportImport(!showExportImport)}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                  >
                    <FaFileExport className="mr-2 h-4 w-4" />
                    {showExportImport ? 'Hide Export/Import' : 'Export/Import'}
                  </button>
                )}
                <button
                  onClick={() => {
                    setNewCustomer({});
                    setEditingCustomer(null);
                    setShowModal(true);
                  }}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Create Customer
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Export/Import Section */}
        {user?.role === 'administrator' && showExportImport && (
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-6">
            <div className="px-6 py-4">
              <ExportImportCustomers />
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-6">
          <div className="px-6 py-4">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaSearch className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search customers by name..."
                    className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  {searchTerm && (
                    <button
                      onClick={() => {
                        setSearchTerm('');
                        fetchCustomers(currentPage, itemsPerPage);
                      }}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <FaTimes className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    </button>
                  )}
                </div>
              </div>

              {/* Bulk Actions */}
              {user?.role === 'administrator' && selectedCustomers.size > 0 && (
                <div className="flex gap-2">
                  <button
                    onClick={handleBulkDelete}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
                    disabled={submitting}
                  >
                    <FaTrash className="mr-2 h-4 w-4" />
                    Delete Selected ({selectedCustomers.size})
                  </button>
                </div>
              )}
            </div>

            {/* Search Status */}
            {searchTerm.trim().length === 1 && (
              <p className="mt-2 text-sm text-gray-500">
                Type at least 2 characters to search
              </p>
            )}

            {isSearching && (
              <div className="mt-2 flex items-center text-sm text-gray-500">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                Searching...
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Customer List */}
        {searchTerm.trim().length >= 2 && customers.length === 0 && !isSearching ? (
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-8 text-center">
            <FaSearch className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
            <p className="text-gray-500">No customers match your search for "{searchTerm}"</p>
          </div>
        ) : (
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg overflow-hidden">
            {/* Table Header */}
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">Customer List</h2>
                {user?.role === 'administrator' && totalItems > itemsPerPage && (
                  <div className="flex items-center text-xs text-orange-600 bg-orange-50 px-3 py-1 rounded-full">
                    <FaExclamationTriangle className="mr-1 h-3 w-3" />
                    "Select All" only selects customers on this page
                  </div>
                )}
              </div>
            </div>

            {/* Desktop Table */}
            <div className="hidden lg:block">
              <table className="w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {user?.role === 'administrator' && (
                      <th className="px-3 py-3 text-left w-10">
                        <input
                          type="checkbox"
                          checked={selectAll}
                          onChange={handleSelectAll}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                    )}
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                      Customer
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">
                      Contact
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                      Address
                    </th>
                    <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-56">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {customers.map((customer) => (
                    <tr 
                      key={customer.id} 
                      className="hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
                      onClick={() => window.location.href = `/customers/${customer.id}`}
                    >
                      {user?.role === 'administrator' && (
                        <td className="px-3 py-4">
                          <input
                            type="checkbox"
                            checked={selectedCustomers.has(customer.id)}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleSelectCustomer(customer.id);
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                      )}
                      <td className="px-3 py-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
                              <FaBuilding className="h-4 w-4 text-blue-600" />
                            </div>
                          </div>
                          <div className="ml-2 min-w-0 flex-1">
                            <div className="text-sm font-medium text-gray-900 truncate">{customer.name}</div>
                            <div className="text-xs text-gray-500">ID: {customer.id}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-3 py-4">
                        <div className="text-sm text-gray-900 truncate">
                          {customer.contact_person || '—'}
                        </div>
                        <div className="text-xs text-gray-500 truncate">
                          {customer.email ? (
                            <a href={`mailto:${customer.email}`} className="text-blue-600 hover:text-blue-800">
                              {customer.email}
                            </a>
                          ) : (
                            '—'
                          )}
                        </div>
                        <div className="text-xs text-gray-500 truncate">
                          {customer.phone ? (
                            <a href={`tel:${customer.phone}`} className="text-blue-600 hover:text-blue-800">
                              {customer.phone}
                            </a>
                          ) : (
                            '—'
                          )}
                        </div>
                      </td>
                      <td className="px-3 py-4">
                        <div className="text-sm text-gray-900 truncate">
                          {customer.address || '—'}
                        </div>
                        <div className="text-xs text-gray-500 truncate">
                          {customer.city ? `${customer.postal_code || ''} ${customer.city}` : '—'}
                        </div>
                      </td>
                      <td className="px-3 py-4 text-right text-sm font-medium">
                        <div className="flex items-center justify-end gap-1">
                          <Link
                            to={`/customers/${customer.id}`}
                            className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200"
                            title="View customer details"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <FaEye className="h-3 w-3" />
                          </Link>
                          <Link
                            to={`/customers/${customer.id}/documents`}
                            className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 transition-colors duration-200"
                            title="View documents"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <FaFileAlt className="h-3 w-3" />
                          </Link>
                          {user?.role === 'administrator' && (
                            <>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingCustomer(customer);
                                  setNewCustomer({
                                    date: customer.date,
                                    code: customer.code,
                                    name: customer.name,
                                    kvk_number: customer.kvk_number,
                                    contact_person: customer.contact_person,
                                    gender: customer.gender,
                                    title: customer.title,
                                    address: customer.address,
                                    postal_code: customer.postal_code,
                                    city: customer.city,
                                    country: customer.country,
                                    address2: customer.address2,
                                    postal_code2: customer.postal_code2,
                                    city2: customer.city2,
                                    country2: customer.country2,
                                    phone: customer.phone,
                                    mobile: customer.mobile,
                                    fax: customer.fax,
                                    email: customer.email,
                                    invoice_email: customer.invoice_email,
                                    reminder_email: customer.reminder_email,
                                    website: customer.website,
                                    bank_account: customer.bank_account,
                                    giro_account: customer.giro_account,
                                    vat_number: customer.vat_number,
                                    iban: customer.iban,
                                    bic: customer.bic,
                                    sepa_auth_type: customer.sepa_auth_type,
                                    mandate_reference: customer.mandate_reference,
                                    mandate_date: customer.mandate_date,
                                    customer_type: customer.customer_type,
                                    no_email: customer.no_email,
                                    payment_term: customer.payment_term,
                                    newsletter_groups: customer.newsletter_groups,
                                    subscriptions: customer.subscriptions,
                                    notes: customer.notes,
                                  });
                                  setShowModal(true);
                                }}
                                className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 transition-colors duration-200"
                                title="Edit customer"
                              >
                                <FaEdit className="h-3 w-3" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDelete(customer.id);
                                }}
                                className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200"
                                disabled={submitting}
                                title="Delete customer"
                              >
                                <FaTrash className="h-3 w-3" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="lg:hidden">
              {user?.role === 'administrator' && customers.length > 0 && (
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Select All ({customers.length})
                    </span>
                    {selectedCustomers.size > 0 && (
                      <span className="ml-auto text-sm text-blue-600 font-medium">
                        {selectedCustomers.size} selected
                      </span>
                    )}
                  </label>
                </div>
              )}
              
              <div className="divide-y divide-gray-200">
                {customers.map((customer) => (
                  <div key={customer.id} className="px-6 py-4 hover:bg-gray-50 transition-colors duration-150">
                    <div className="flex items-start space-x-4">
                      {user?.role === 'administrator' && (
                        <input
                          type="checkbox"
                          checked={selectedCustomers.has(customer.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleSelectCustomer(customer.id);
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-1"
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
                              <FaBuilding className="h-4 w-4 text-blue-600" />
                            </div>
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-gray-900 truncate">{customer.name}</h3>
                            <p className="text-xs text-gray-500">ID: {customer.id}</p>
                          </div>
                        </div>
                        
                        <div className="mt-3 space-y-1">
                          {customer.contact_person && (
                            <p className="text-sm text-gray-600">{customer.contact_person}</p>
                          )}
                          {customer.email && (
                            <p className="text-sm text-gray-600">
                              <a href={`mailto:${customer.email}`} className="text-blue-600 hover:text-blue-800">
                                {customer.email}
                              </a>
                            </p>
                          )}
                          {customer.phone && (
                            <p className="text-sm text-gray-600">
                              <a href={`tel:${customer.phone}`} className="text-blue-600 hover:text-blue-800">
                                {customer.phone}
                              </a>
                            </p>
                          )}
                          {customer.address && (
                            <p className="text-sm text-gray-600">
                              {customer.address}
                              {customer.city && `, ${customer.postal_code || ''} ${customer.city}`}
                            </p>
                          )}
                        </div>

                        <div className="mt-4 flex flex-wrap gap-2">
                          <Link
                            to={`/customers/${customer.id}`}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <FaEye className="mr-1 h-3 w-3" />
                            View
                          </Link>
                          <Link
                            to={`/customers/${customer.id}/documents`}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 transition-colors duration-200"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <FaFileAlt className="mr-1 h-3 w-3" />
                            Documents
                          </Link>
                          {user?.role === 'administrator' && (
                            <>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setEditingCustomer(customer);
                                  setNewCustomer({
                                    date: customer.date,
                                    code: customer.code,
                                    name: customer.name,
                                    kvk_number: customer.kvk_number,
                                    contact_person: customer.contact_person,
                                    gender: customer.gender,
                                    title: customer.title,
                                    address: customer.address,
                                    postal_code: customer.postal_code,
                                    city: customer.city,
                                    country: customer.country,
                                    address2: customer.address2,
                                    postal_code2: customer.postal_code2,
                                    city2: customer.city2,
                                    country2: customer.country2,
                                    phone: customer.phone,
                                    mobile: customer.mobile,
                                    fax: customer.fax,
                                    email: customer.email,
                                    invoice_email: customer.invoice_email,
                                    reminder_email: customer.reminder_email,
                                    website: customer.website,
                                    bank_account: customer.bank_account,
                                    giro_account: customer.giro_account,
                                    vat_number: customer.vat_number,
                                    iban: customer.iban,
                                    bic: customer.bic,
                                    sepa_auth_type: customer.sepa_auth_type,
                                    mandate_reference: customer.mandate_reference,
                                    mandate_date: customer.mandate_date,
                                    customer_type: customer.customer_type,
                                    no_email: customer.no_email,
                                    payment_term: customer.payment_term,
                                    newsletter_groups: customer.newsletter_groups,
                                    subscriptions: customer.subscriptions,
                                    notes: customer.notes,
                                  });
                                  setShowModal(true);
                                }}
                                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 transition-colors duration-200"
                              >
                                <FaEdit className="mr-1 h-3 w-3" />
                                Edit
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDelete(customer.id);
                                }}
                                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200"
                                disabled={submitting}
                              >
                                <FaTrash className="mr-1 h-3 w-3" />
                                Delete
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Empty State */}
            {customers.length === 0 && (
              <div className="px-6 py-12 text-center">
                <FaUsers className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
                <p className="text-gray-500 mb-6">Get started by creating your first customer.</p>
                <button
                  onClick={() => {
                    setNewCustomer({});
                    setEditingCustomer(null);
                    setShowModal(true);
                  }}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Create Customer
                </button>
              </div>
            )}
          </div>
        )}

        {/* Pagination */}
        {searchTerm.trim() === '' && (
          <div className="mt-6">
            <div className="flex flex-col sm:flex-row justify-between items-center mb-4">
              <div className="text-sm text-gray-600 mb-2 sm:mb-0">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} customers
                {selectedCustomers.size > 0 && (
                  <span className="ml-2 text-blue-600 font-medium">
                    ({selectedCustomers.size} selected on this page)
                  </span>
                )}
              </div>
              {user?.role === 'administrator' && totalItems > itemsPerPage && (
                <button
                  onClick={handleDeleteAllCustomers}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200"
                  disabled={submitting}
                  title="Delete ALL customers in the database (not just visible ones)"
                >
                  <FaTrash className="mr-1 h-3 w-3" />
                  Delete ALL Customers
                </button>
              )}
            </div>
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalItems / itemsPerPage)}
              onPageChange={handlePageChange}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={handleItemsPerPageChange}
            />
          </div>
        )}

        {/* Modals */}
        {showModal && (
          <CustomerModal
            customer={newCustomer}
            onClose={() => {
              setShowModal(false);
              setEditingCustomer(null);
              setNewCustomer({});
            }}
            onSubmit={editingCustomer ? handleUpdate : handleCreate}
            setCustomer={setNewCustomer}
            isEditing={!!editingCustomer}
            submitting={submitting}
          />
        )}

        {showDeletionDialog && customerToDelete && (
          <CustomerDeletionDialog
            isOpen={showDeletionDialog}
            onClose={handleCloseDeletionDialog}
            onConfirm={handleConfirmDelete}
            customerId={customerToDelete.id}
            customerName={customerToDelete.name}
          />
        )}
      </div>
    </div>
  );
};

export default Customers;
