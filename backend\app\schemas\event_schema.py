"""
Event schema module.
This module defines the schema for Event model validation.
"""
from marshmallow import fields, validate, ValidationError
from app.schemas import ma
from app.models.event import Event, VALID_EVENT_TYPES

def validate_event_type(value):
    """Custom validator for event_type that allows None or valid types."""
    if value is None or value == '':
        return True
    if value not in VALID_EVENT_TYPES:
        raise ValidationError(f"Invalid event type. Must be one of: {', '.join(VALID_EVENT_TYPES)} or None for general events.")
    return True

class EventSchema(ma.SQLAlchemySchema):
    """Schema for Event model."""

    class Meta:
        """Meta class for EventSchema."""
        model = Event
        load_instance = True

    id = ma.auto_field(dump_only=True)
    customer_id = fields.Integer(allow_none=True)
    user_ids = fields.List(fields.Integer(), allow_none=True)
    # Keep legacy field for backward compatibility
    user_id = fields.Integer(allow_none=True)
    document_id = fields.Integer(allow_none=True)
    event_type = fields.String(required=False, allow_none=True, validate=validate_event_type, missing=None)
    description = fields.String(required=True)
    scheduled_date = fields.DateTime(required=True)
    status = fields.String(dump_only=True)
    completed_at = fields.DateTime(allow_none=True, dump_only=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    # Additional fields for response
    customer_name = fields.String(dump_only=True)
    customer_address = fields.String(dump_only=True)
    user_emails = fields.List(fields.String(), dump_only=True)
    user_names = fields.List(fields.String(), dump_only=True)
    # Keep legacy fields for backward compatibility
    user_email = fields.String(dump_only=True)

    # Validation removed due to Marshmallow compatibility issues

# Initialize schemas
event_schema = EventSchema()
events_schema = EventSchema(many=True)
