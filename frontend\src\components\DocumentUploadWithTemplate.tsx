import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { Customer } from '../types/customer';
import DocumentTemplateSelector from './DocumentTemplateSelector';
import TemplateFormEditor from './TemplateFormEditor';
import { createDocument } from '../services/documentService';
import { getCustomerById } from '../services/customerService';
import LoadingSpinner from './LoadingSpinner';
import { FaArrowLeft, FaUpload } from 'react-icons/fa';
import { MobileContainer, MobileCard, MobileFormGroup, MobileFormActions } from './common/MobileUtils';
import { useMobile } from '../hooks/useMobile';
import { getTwelveMonthExpiryDate } from '../utils/dateUtils';

interface DocumentUploadWithTemplateProps {
  customerId: number;
  documentType: string;
  onSuccess: (documentId: number) => void;
  onCancel: () => void;
}

const DocumentUploadWithTemplate: React.FC<DocumentUploadWithTemplateProps> = ({
  customerId,
  documentType,
  onSuccess,
  onCancel
}) => {
  const { isMobile } = useMobile();
  const [step, setStep] = useState<'select' | 'edit' | 'upload'>('select');
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loadingCustomer, setLoadingCustomer] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  const [expiryDate, setExpiryDate] = useState<string>('');
  const [expiryType, setExpiryType] = useState<"date" | "niet_van_toepassing">("date");
  const [useVersionStatus, setUseVersionStatus] = useState<boolean>(true);
  const [versionStatus, setVersionStatus] = useState<"active" | "inactive">("active");

  useEffect(() => {
    loadCustomer();
  }, [customerId]);

  const loadCustomer = async () => {
    try {
      setLoadingCustomer(true);
      const customerData = await getCustomerById(customerId);
      setCustomer(customerData);
    } catch (err: any) {
      console.error('Failed to load customer:', err);
      setError('Failed to load customer information');
    } finally {
      setLoadingCustomer(false);
    }
  };

  const handleTemplateSelected = (template: DocumentTemplate) => {
    setSelectedTemplate(template);
    setStep('edit');
    
    // Auto-set 12-month expiration for onderhoudsbon documents
    if (documentType === 'onderhoudsbon') {
      setExpiryType('date');
      setExpiryDate(getTwelveMonthExpiryDate());
    }
  };

  const handleSave = async (blob: Blob, fileName: string) => {
    try {
      setUploading(true);
      setError(null);

      // Create a File object from the Blob
      const file = new File([blob], fileName, { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });

      // Upload the document
      const document = await createDocument(
        customerId,
        null, // No event ID
        file,
        documentType,
        expiryType, // Expiry type (date or niet_van_toepassing)
        expiryType === 'date' ? expiryDate : undefined, // Expiry date if applicable
        undefined, // No related document
        true, // This is an admin direct upload
        false, // Not "not applicable"
        useVersionStatus, // Use version status
        versionStatus // Version status
      );

      onSuccess(document.id);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to upload document');
    } finally {
      setUploading(false);
    }
  };

  const handleBack = () => {
    if (step === 'edit') {
      setStep('select');
    } else {
      onCancel();
    }
  };

  if (loadingCustomer) {
    return (
      <MobileCard>
        <div className="flex justify-center items-center p-8">
          <LoadingSpinner />
          <span className="ml-2 text-amspm-text dark:text-dark-text">Loading customer information...</span>
        </div>
      </MobileCard>
    );
  }

  return (
    <MobileCard>
      <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-center'} mb-6`}>
        <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">
          {step === 'select' ? 'Select Document Template' : `Edit Document - ${customer?.name || 'Unknown Customer'}`}
        </h2>
        {step === 'edit' && (
          <button
            onClick={() => setStep('select')}
            className="btn btn-outline btn-sm flex items-center mobile-touch-target"
          >
            <FaArrowLeft className="mr-2" /> {isMobile ? 'Back' : 'Back to Templates'}
          </button>
        )}
      </div>

      {error && (
        <div className={`mb-4 p-3 rounded-lg ${
          error.startsWith('Test mode:')
            ? 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border border-green-200 dark:border-green-800'
            : 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 border border-red-200 dark:border-red-800'
        }`}>
          {error}
        </div>
      )}

      {uploading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <MobileCard className="max-w-md w-full">
            <div className="text-center">
              <LoadingSpinner />
              <p className="mt-4 text-amspm-text dark:text-dark-text">Uploading document...</p>
            </div>
          </MobileCard>
        </div>
      )}

      {step === 'select' && (
        <DocumentTemplateSelector
          documentType={documentType}
          onTemplateSelected={handleTemplateSelected}
        />
      )}

      {step === 'edit' && selectedTemplate && (
        <div className="space-y-4">
          <div className={`grid ${isMobile ? 'grid-cols-1 gap-4' : 'grid-cols-1 md:grid-cols-3 gap-4'} mb-6`}>
            {/* Expiry Type Selection */}
            <MobileFormGroup label="Expiry Type">
              <select
                value={expiryType}
                onChange={(e) => {
                  setExpiryType(e.target.value as "date" | "niet_van_toepassing");
                  if (e.target.value === "niet_van_toepassing") {
                    setExpiryDate("");
                  } else if (e.target.value === "date" && documentType === 'onderhoudsbon') {
                    // Auto-set 12-month expiration for onderhoudsbon
                    setExpiryDate(getTwelveMonthExpiryDate());
                  }
                }}
                className="mobile-form-input"
                disabled={uploading}
              >
                <option value="date">Date</option>
                <option value="niet_van_toepassing">Niet van toepassing</option>
              </select>
            </MobileFormGroup>

            {/* Expiry Date Field */}
            {expiryType === "date" && (
              <MobileFormGroup label="Expiry Date">
                <input
                  type="datetime-local"
                  value={expiryDate}
                  onChange={(e) => setExpiryDate(e.target.value)}
                  className="mobile-form-input"
                  disabled={uploading}
                />
                {documentType === 'onderhoudsbon' && (
                  <p className="text-xs text-blue-600 mt-1">
                    Automatically set to 12 months from today for onderhoudsbon documents
                  </p>
                )}
              </MobileFormGroup>
            )}

            {/* Version Status Field */}
            <MobileFormGroup label="Version Status">
              <div className="mb-2">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    checked={useVersionStatus}
                    onChange={(e) => setUseVersionStatus(e.target.checked)}
                    disabled={uploading}
                    className="form-checkbox"
                  />
                  <span className="ml-2 text-amspm-text dark:text-dark-text text-sm">Use Version Status</span>
                </label>
              </div>
              {useVersionStatus && (
                <select
                  value={versionStatus}
                  onChange={(e) => setVersionStatus(e.target.value as "active" | "inactive")}
                  className="mobile-form-input"
                  disabled={uploading}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              )}
            </MobileFormGroup>
          </div>

          <div className="border-t pt-4">
            <TemplateFormEditor
              template={selectedTemplate}
              customer={customer}
              onSave={handleSave}
              onCancel={() => setStep('select')}
            />
          </div>
        </div>
      )}
    </MobileCard>
  );
};

export default DocumentUploadWithTemplate;
