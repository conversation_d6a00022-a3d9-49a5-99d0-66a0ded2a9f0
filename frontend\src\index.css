@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&display=swap');
@import './styles/variables.css';
@import './styles/modal.css';
@import './styles/mobile.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply font-montserrat;
    /* Mobile viewport improvements */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Dark mode transitions */
  body {
    @apply transition-colors duration-200 ease-in-out;
    /* Mobile performance improvements */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Prevent horizontal scrolling */
    overflow-x: hidden;
  }

  /* Dark mode scrollbar */
  .dark ::-webkit-scrollbar {
    width: 12px;
  }

  .dark ::-webkit-scrollbar-track {
    @apply bg-dark-secondary;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-dark-border rounded-full;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-dark-hover;
  }
}

@layer components {
  /* Base Layout */
  .app {
    @apply min-h-screen bg-amspm-background dark:bg-dark-primary flex flex-col;
  }
  .container {
    @apply w-full max-w-7xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8;
  }

  /* Mobile-first responsive containers */
  .mobile-container {
    @apply w-full px-3 py-2 sm:px-4 sm:py-3 md:px-6 md:py-4;
  }

  .mobile-safe-area {
    @apply pb-safe-bottom pl-safe-left pr-safe-right;
  }

  /* Navbar and Sidebar */
  .navbar {
    @apply bg-amspm-secondary dark:bg-dark-secondary text-amspm-text dark:text-dark-text p-4 flex justify-between items-center border-b border-amspm-light-gray dark:border-gray-700 sticky top-0 z-20;
  }
  .navbar-left {
    @apply flex items-center;
  }
  .navbar-brand {
    @apply text-2xl font-bold text-amspm-primary uppercase;
  }
  .navbar-links {
    @apply flex flex-col md:flex-row md:space-x-6 space-y-4 md:space-y-0;
  }
  .navbar-link {
    @apply text-amspm-text uppercase font-medium hover:text-amspm-primary transition duration-200;
  }
  .navbar-link.active {
    @apply text-amspm-primary font-bold;
  }

  /* Sidebar */
  .sidebar {
    @apply fixed top-0 left-0 h-full bg-white dark:bg-dark-secondary shadow-lg z-30 transition-all duration-300 ease-in-out overflow-hidden;
  }
  .sidebar-header {
    @apply flex items-center justify-between h-16 px-4 border-b border-amspm-light-gray dark:border-gray-700;
  }
  .sidebar-content {
    @apply flex flex-col h-[calc(100%-4rem)];
  }
  .sidebar-footer {
    @apply mt-auto border-t border-amspm-light-gray dark:border-gray-700 p-4;
  }
  .sidebar-nav-item {
    @apply flex items-center py-3 px-4 w-full rounded-lg transition-colors duration-200;
  }
  .sidebar-nav-item.active {
    @apply bg-amspm-primary text-amspm-secondary font-medium;
  }
  .sidebar-nav-item:not(.active) {
    @apply text-amspm-text hover:bg-amspm-light-gray dark:text-dark-text dark:hover:bg-gray-700;
  }

  /* Headings */
  h1 {
    @apply text-3xl md:text-4xl font-bold text-amspm-primary dark:text-dark-accent uppercase mb-6;
  }
  h2 {
    @apply text-2xl font-semibold text-amspm-text dark:text-dark-text uppercase mb-4;
  }
  h3 {
    @apply text-xl font-medium text-amspm-text dark:text-dark-text mb-2;
  }

  /* Cards */
  .card {
    @apply bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-sm border border-amspm-light-gray dark:border-dark-border;
  }
  .card-header {
    @apply border-b border-amspm-light-gray dark:border-dark-border pb-3 mb-4;
  }
  .card-content {
    @apply space-y-4 text-amspm-text dark:text-dark-text;
  }

  /* Generic dark mode overrides for all cards and tables */
  .dark div[class*="bg-white"] {
    @apply bg-dark-secondary;
  }

  .dark div[class*="rounded-lg shadow"] {
    @apply border-dark-border;
  }

  .dark th[class*="bg-gray-50"] {
    @apply bg-dark-tertiary text-dark-text-light border-dark-border;
  }

  .dark td, .dark th {
    @apply border-dark-border text-dark-text;
  }

  .dark tr {
    @apply border-dark-border;
  }

  .dark tr:hover {
    @apply bg-dark-hover;
  }

  .dark tbody {
    @apply divide-dark-border;
  }

  .dark h3[class*="text-amspm-primary"] {
    @apply text-dark-accent;
  }

  .dark p[class*="text-gray-600"],
  .dark p[class*="text-gray-500"],
  .dark span[class*="text-gray-500"] {
    @apply text-dark-text-light;
  }

  .dark span[class*="bg-green-100"] {
    @apply bg-green-900 text-green-200;
  }

  .dark span[class*="bg-yellow-100"] {
    @apply bg-yellow-900 text-yellow-200;
  }

  .dark div[class*="border-t border-gray-200"] {
    @apply border-dark-border;
  }

  /* Fix for search boxes and inputs */
  .dark input[class*="bg-white"] {
    @apply bg-dark-input text-dark-text;
  }

  /* Fix for "No results found" messages */
  .dark div[class*="text-center text-gray-500"] {
    @apply text-dark-text-light;
  }

  /* Fix for modals and dialogs */
  .dark div[class*="bg-white p-4"] {
    @apply bg-dark-secondary text-dark-text;
  }

  /* Fix for status badges */
  .dark span[class*="bg-red-100"] {
    @apply bg-red-900 text-red-200;
  }

  .dark span[class*="bg-blue-100"] {
    @apply bg-blue-900 text-blue-200;
  }

  /* Fix for section headers */
  .dark h2[class*="text-amspm-text"] {
    @apply text-dark-text;
  }

  /* Fix for links */
  .dark a[class*="text-amspm-primary"],
  .dark button[class*="text-amspm-primary"],
  .dark span[class*="text-amspm-primary"] {
    @apply text-dark-accent hover:text-dark-accent-hover;
  }

  /* Fix for table backgrounds */
  .dark table {
    @apply bg-dark-secondary;
  }

  /* Fix for pagination */
  .dark .pagination button,
  .dark .pagination span,
  .dark .pagination div {
    @apply text-dark-text border-dark-border;
  }

  .dark .pagination button:hover {
    @apply bg-dark-hover;
  }

  .dark .pagination button.active,
  .dark .pagination button[aria-current="true"] {
    @apply bg-dark-accent text-white border-dark-accent;
  }

  /* Fix for pagination numbers */
  .dark .pagination button[class*="bg-blue-500"],
  .dark .pagination button[class*="bg-amspm-primary"] {
    @apply bg-dark-accent text-white border-dark-accent;
  }

  /* Fix for empty state messages */
  .dark td[class*="text-center"] {
    @apply text-dark-text-light;
  }

  /* Fix for profile page and forms */
  .dark label[class*="text-gray-700"],
  .dark label[class*="text-gray-600"] {
    @apply text-dark-text-light;
  }

  .dark p[class*="text-gray-700"],
  .dark p[class*="text-gray-600"] {
    @apply text-dark-text-light;
  }

  .dark input[type="text"],
  .dark input[type="email"],
  .dark input[type="password"],
  .dark input[type="number"],
  .dark input[type="tel"],
  .dark input[type="url"],
  .dark input[type="date"],
  .dark input[type="datetime-local"],
  .dark textarea,
  .dark select {
    @apply bg-dark-input text-dark-text border-dark-border;
  }

  .dark input:focus,
  .dark textarea:focus,
  .dark select:focus {
    @apply bg-dark-input-focus border-dark-accent;
  }

  /* Fix for profile info */
  .dark div[class*="border-b border-gray-200"] {
    @apply border-dark-border;
  }

  /* Buttons */
  .btn {
    @apply bg-amspm-secondary dark:bg-dark-tertiary text-amspm-text dark:text-dark-text border border-amspm-text dark:border-dark-border-light px-3 sm:px-4 py-2 rounded uppercase text-sm sm:text-base font-medium hover:bg-amspm-primary hover:text-amspm-secondary dark:hover:bg-dark-accent dark:hover:text-white transition duration-200 flex items-center justify-center min-h-[40px] touch-manipulation;
    /* Ensure text is always visible */
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
  }
  .btn-secondary {
    @apply bg-amspm-primary dark:bg-dark-accent text-amspm-secondary dark:text-white px-3 sm:px-4 py-2 rounded uppercase text-sm sm:text-base font-medium hover:bg-opacity-90 dark:hover:bg-dark-accent-hover transition duration-200 flex items-center justify-center min-h-[40px] touch-manipulation;
    /* Ensure text is always visible */
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
  }
  .btn-danger {
    @apply bg-red-500 dark:bg-dark-error text-white px-3 sm:px-4 py-2 rounded uppercase text-sm sm:text-base font-medium hover:bg-opacity-90 dark:hover:bg-opacity-80 transition duration-200 flex items-center justify-center min-h-[40px] touch-manipulation;
    /* Ensure text is always visible */
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
  }
  .btn-outline {
    @apply border border-amspm-primary dark:border-dark-accent text-amspm-primary dark:text-dark-accent px-3 sm:px-4 py-2 rounded uppercase text-sm sm:text-base font-medium hover:bg-amspm-primary hover:text-amspm-secondary dark:hover:bg-dark-accent dark:hover:text-white transition duration-200 flex items-center justify-center min-h-[40px] touch-manipulation;
    /* Ensure text is always visible */
    color: inherit !important;
    font-size: inherit !important;
    line-height: inherit !important;
  }
  .btn-ghost {
    @apply text-amspm-primary dark:text-dark-accent hover:text-amspm-text dark:hover:text-dark-text-light transition duration-200 flex items-center justify-center touch-manipulation;
  }

  /* Disabled button states */
  .btn:disabled, .btn-secondary:disabled, .btn-danger:disabled, .btn-outline:disabled {
    @apply opacity-50 cursor-not-allowed hover:bg-opacity-100;
  }

  /* Mobile-specific button styles */
  .btn-mobile-full {
    @apply w-full sm:w-auto;
  }

  .btn-group-mobile {
    @apply flex flex-col sm:flex-row gap-2 sm:gap-3;
  }

  .btn-group-mobile-stacked {
    @apply flex flex-col gap-2;
  }

  .btn-sm {
    @apply px-2 py-1.5 sm:px-3 sm:py-1.5 text-xs sm:text-sm min-h-[36px];
  }

  .btn-lg {
    @apply px-4 py-3 sm:px-6 sm:py-3 text-base sm:text-lg min-h-[48px];
  }

  /* Tables */
  .table {
    @apply w-full border-collapse;
  }
  .table-header {
    @apply bg-amspm-primary dark:bg-dark-tertiary text-amspm-secondary dark:text-dark-text;
  }
  .table-row {
    @apply border-b border-amspm-light-gray dark:border-dark-border hover:bg-amspm-light-gray dark:hover:bg-dark-hover transition duration-150;
  }
  .table-head {
    @apply p-2 sm:p-3 text-left font-semibold uppercase text-xs sm:text-sm text-amspm-text dark:text-dark-text-light;
  }
  .table-cell {
    @apply p-2 sm:p-3 text-amspm-text dark:text-dark-text text-sm;
  }

  /* Responsive tables */
  .table-responsive {
    @apply w-full overflow-x-auto -mx-4 px-4;
  }

  /* Mobile card view for tables */
  .mobile-card {
    @apply bg-white dark:bg-dark-secondary p-3 sm:p-4 rounded-lg shadow-sm border border-amspm-light-gray dark:border-dark-border mb-3 sm:mb-4;
  }

  .mobile-card-header {
    @apply flex justify-between items-start mb-3;
  }

  .mobile-card-title {
    @apply font-bold text-amspm-primary dark:text-dark-accent text-base sm:text-lg;
  }

  .mobile-card-content {
    @apply space-y-2;
  }

  .mobile-card-row {
    @apply flex justify-between items-center py-1;
  }

  .mobile-card-label {
    @apply text-xs sm:text-sm font-medium text-gray-500 dark:text-dark-text-light;
  }

  .mobile-card-value {
    @apply text-sm sm:text-base text-amspm-text dark:text-dark-text;
  }

  .mobile-card-actions {
    @apply flex flex-col sm:flex-row gap-2 mt-3 pt-3 border-t border-amspm-light-gray dark:border-dark-border;
  }

  .mobile-card-label {
    @apply text-sm text-gray-600 dark:text-dark-text-light font-medium;
  }

  .mobile-card-value {
    @apply text-amspm-text dark:text-dark-text;
  }

  /* Forms and Inputs */
  .form-group {
    @apply mb-4;
  }
  .form-group label {
    @apply block text-amspm-text dark:text-dark-text-light font-medium mb-1 uppercase text-sm sm:text-base;
  }
  .input {
    @apply w-full p-2 border border-amspm-light-gray dark:border-dark-border rounded focus:outline-none focus:ring-2 focus:ring-amspm-primary dark:focus:ring-dark-accent dark:bg-dark-input dark:text-dark-text transition duration-200 text-sm sm:text-base min-h-[40px] touch-manipulation;
  }
  .input:disabled {
    @apply bg-gray-100 dark:bg-dark-tertiary dark:text-dark-text-light cursor-not-allowed;
  }
  .input:focus {
    @apply dark:bg-dark-input-focus;
  }

  /* Form layouts */
  .form-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4;
  }

  .form-row {
    @apply flex flex-col sm:flex-row gap-3 sm:gap-4 mb-3 sm:mb-4;
  }

  /* Mobile-specific form layouts */
  .form-mobile-stack {
    @apply flex flex-col gap-3;
  }

  .form-mobile-inline {
    @apply flex flex-col sm:flex-row gap-2 sm:gap-4 items-start sm:items-center;
  }

  .form-actions {
    @apply flex flex-col sm:flex-row gap-2 sm:gap-3 mt-4 sm:mt-6;
  }

  .form-actions-mobile-full {
    @apply flex flex-col gap-2;
  }

  /* Form feedback */
  .form-error {
    @apply text-red-600 dark:text-dark-error text-sm mt-1;
  }

  .form-help {
    @apply text-gray-500 dark:text-dark-text-light text-sm mt-1;
  }

  /* Select inputs */
  select {
    @apply appearance-none bg-white dark:bg-dark-input text-amspm-text dark:text-dark-text border border-amspm-light-gray dark:border-dark-border rounded p-2 pr-8 focus:outline-none focus:ring-2 focus:ring-amspm-primary dark:focus:ring-dark-accent text-sm sm:text-base min-h-[40px] touch-manipulation;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23666666'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
  }

  select:focus {
    @apply dark:bg-dark-input-focus;
  }

  .dark select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23b3b3b3'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  }

  /* FullCalendar */
  .fc {
    @apply bg-white dark:bg-dark-secondary rounded-lg shadow-sm p-4 !important;
  }
  
  /* Calendar header and toolbar styling */
  .fc .fc-toolbar {
    margin-bottom: 1.5rem !important;
  }
  
  .fc .fc-toolbar-title {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    text-align: center !important;
  }
  
  .dark .fc .fc-toolbar-title {
    color: #ffffff !important;
  }
  
  .fc .fc-toolbar-chunk {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }
  .fc .fc-daygrid-day {
    @apply border-amspm-light-gray dark:border-dark-border !important;
    min-height: 100px !important;
  }
  .fc .fc-col-header-cell {
    @apply bg-amspm-primary dark:bg-dark-tertiary text-amspm-secondary dark:text-dark-text !important;
  }
  .fc .fc-daygrid-day-frame {
    @apply dark:bg-dark-secondary !important;
  }
  .fc .fc-day-today {
    @apply dark:bg-dark-hover !important;
  }
  .fc .fc-daygrid-day-number {
    @apply dark:text-dark-text !important;
  }
  .fc .fc-daygrid-event {
    @apply bg-amspm-primary dark:bg-dark-accent text-amspm-secondary dark:text-white rounded-sm !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 2px 4px !important;
    font-size: 0.75rem !important;
    white-space: normal !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-height: 40px !important;
  }
  .fc .fc-daygrid-event-harness {
    margin-top: 1px !important;
    margin-bottom: 1px !important;
    width: 100% !important;
  }
  .fc .fc-button {
    @apply bg-amspm-primary dark:bg-dark-accent text-amspm-secondary dark:text-white hover:bg-opacity-90 dark:hover:bg-dark-accent-hover !important;
    padding: 8px 16px !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease-in-out !important;
    border: 1px solid transparent !important;
  }
  
  .fc .fc-button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }
  
  .fc .fc-button:active {
    transform: translateY(0) !important;
  }
  
  .fc .fc-button-active {
    background-color: #1f2937 !important;
    color: #ffffff !important;
    border-color: #374151 !important;
  }
  
  .dark .fc .fc-button-active {
    background-color: #374151 !important;
    color: #ffffff !important;
    border-color: #4b5563 !important;
  }
  .fc-daygrid-day-events {
    min-height: 20px !important;
  }
  .fc-event-title {
    font-weight: 500 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }
  .completed-event {
    @apply bg-green-500 dark:bg-dark-success !important;
    border-color: #10b981 !important;
  }
  .dark .completed-event {
    border-color: #0d9668 !important;
  }
  .pending-event {
    border-width: 1px !important;
  }

  /* Enhanced Calendar Styling - Fixed readability issues */
  .fc .fc-daygrid-day {
    min-height: 120px !important;
  }
  
  .fc .fc-daygrid-event {
    @apply rounded-md !important;
    margin: 2px !important;
    padding: 6px 8px !important;
    font-size: 0.75rem !important;
    white-space: normal !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-height: none !important;
    min-height: 60px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  }
  
  .fc .fc-daygrid-event-harness {
    margin: 2px !important;
    width: calc(100% - 4px) !important;
  }
  
  .fc .fc-daygrid-day-events {
    margin-top: 8px !important;
    min-height: 20px !important;
  }
  
  .fc .fc-daygrid-day-number {
    padding: 4px !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
  }
  
  .fc .fc-day-today {
    background-color: rgba(59, 130, 246, 0.1) !important;
  }
  
  .dark .fc .fc-day-today {
    background-color: rgba(59, 130, 246, 0.2) !important;
  }
  
  /* Event content styling for different views */
  .event-content-month {
    font-size: 0.75rem !important;
  }
  
  .event-content-month .truncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }
  
  .event-content-week {
    font-size: 0.75rem !important;
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(4px) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    margin: 2px !important;
    transition: all 0.2s ease !important;
  }
  
  .event-content-week:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  }
  
  .event-content-list {
    font-size: 0.875rem !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(4px) !important;
    border-radius: 8px !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    margin: 4px !important;
    transition: all 0.2s ease !important;
    color: #374151 !important;
  }
  
  .event-content-list:hover {
    background: rgba(255, 255, 255, 0.98) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  }
  
  /* Time grid styling */
  .fc .fc-timegrid-slot {
    min-height: 40px !important;
    border-color: #e5e7eb !important;
  }
  
  .fc .fc-timegrid-event {
    border-radius: 8px !important;
    margin: 2px !important;
    padding: 6px 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
    min-height: 60px !important;
    backdrop-filter: blur(4px) !important;
  }
  
  /* List view styling */
  .fc .fc-list-event {
    border-color: #e5e7eb !important;
    border-radius: 8px !important;
    margin-bottom: 6px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    overflow: visible !important;
  }
  
  .fc .fc-list-event-graphic {
    border-radius: 6px 0 0 6px !important;
  }
  
  .fc .fc-list-event-title {
    padding: 0 !important;
  }
  
  .fc .fc-list-event:hover {
    background-color: rgba(59, 130, 246, 0.05) !important;
    transform: translateY(-1px) !important;
    transition: all 0.2s ease !important;
  }
  
  /* More events link */
  .fc-daygrid-more-link {
    background-color: rgba(59, 130, 246, 0.1) !important;
    color: #3b82f6 !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    text-decoration: none !important;
  }
  
  .fc-daygrid-more-link:hover {
    background-color: rgba(59, 130, 246, 0.2) !important;
  }
  
  .dark .fc-daygrid-more-link {
    background-color: rgba(59, 130, 246, 0.2) !important;
    color: #60a5fa !important;
  }
  
  .dark .fc-daygrid-more-link:hover {
    background-color: rgba(59, 130, 246, 0.3) !important;
  }
  
  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .fc .fc-daygrid-day {
      min-height: 80px !important;
    }
    
    .fc .fc-daygrid-event {
      min-height: 40px !important;
      padding: 3px 5px !important;
    }
    
    .event-content-month {
      font-size: 0.625rem !important;
    }
  }
  
  /* Prevent text overflow and overlapping */
  .fc-event-title {
    font-weight: 500 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: normal !important;
    line-height: 1.2 !important;
  }
  
  /* Better event content organization */
  .event-content-month > div,
  .event-content-week > div,
  .event-content-list > div {
    margin-bottom: 3px !important;
  }
  
  .event-content-month > div:last-child,
  .event-content-week > div:last-child,
  .event-content-list > div:last-child {
    margin-bottom: 0 !important;
  }
  
  /* Improve status badge visibility */
  .fc-event .bg-orange-600,
  .fc-event .bg-green-600 {
    display: inline-block !important;
    width: fit-content !important;
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }
  
  /* Additional calendar improvements */
  .fc .fc-daygrid-day-frame {
    padding: 4px !important;
  }
  
  .fc .fc-daygrid-day-events {
    position: relative !important;
    z-index: 1 !important;
  }
  
  /* Ensure events don't overlap with day numbers */
  .fc .fc-daygrid-day-top {
    position: relative !important;
    z-index: 2 !important;
    margin-bottom: 4px !important;
  }
  
  /* Better event positioning */
  .fc .fc-daygrid-event-harness {
    position: relative !important;
    z-index: 1 !important;
  }
  
  /* Prevent text from going outside event boundaries */
  .fc-event-main {
    overflow: hidden !important;
    word-wrap: break-word !important;
    hyphens: auto !important;
  }
  
  /* Ensure proper event height calculation */
  .fc .fc-daygrid-event {
    height: auto !important;
    min-height: 60px !important;
    max-height: none !important;
  }
  
  /* Better event content organization */
  .event-content-month > div,
  .event-content-week > div,
  .event-content-list > div {
    margin-bottom: 3px !important;
  }
  
  .event-content-month > div:last-child,
  .event-content-week > div:last-child,
  .event-content-list > div:last-child {
    margin-bottom: 0 !important;
  }
  
  /* Ensure proper event spacing */
  .fc .fc-daygrid-event-harness {
    margin: 2px !important;
    width: calc(100% - 4px) !important;
  }
  
  /* Better event content layout */
  .event-content-month > div,
  .event-content-week > div,
  .event-content-list > div {
    margin-bottom: 4px !important;
  }
  
  /* Ensure status badges don't overlap */
  .fc-event .bg-orange-600,
  .fc-event .bg-green-600 {
    margin-top: 4px !important;
    margin-bottom: 2px !important;
  }
  
  /* Better event title handling */
  .fc-event-title {
    margin-bottom: 4px !important;
    font-weight: 600 !important;
  }
  
  /* Ensure proper line breaks */
  .fc-event-main {
    white-space: normal !important;
    word-break: break-word !important;
  }
  
  /* Better event container */
  .fc-event-main-frame {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* Ensure events don't overflow their containers */
  .fc .fc-daygrid-day {
    overflow: hidden !important;
  }
  
  .fc .fc-daygrid-day-frame {
    overflow: hidden !important;
  }
  
  /* Better event content spacing */
  .event-content-month,
  .event-content-week,
  .event-content-list {
    line-height: 1.3 !important;
  }
  
  /* Ensure proper text contrast */
  .fc-event {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
  
  /* Ensure proper event spacing */
  .fc .fc-daygrid-event-harness {
    margin: 2px !important;
    width: calc(100% - 4px) !important;
  }
  
  /* Better event content layout */
  .event-content-month > div,
  .event-content-week > div,
  .event-content-list > div {
    margin-bottom: 4px !important;
  }
  
  /* Ensure status badges don't overlap */
  .fc-event .bg-orange-600,
  .fc-event .bg-green-600 {
    margin-top: 4px !important;
    margin-bottom: 2px !important;
  }
  
  /* Better event title handling */
  .fc-event-title {
    margin-bottom: 4px !important;
    font-weight: 600 !important;
  }
  
  /* Ensure proper line breaks */
  .fc-event-main {
    white-space: normal !important;
    word-break: break-word !important;
  }
  
  /* Better event container */
  .fc-event-main-frame {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* Ensure events don't overflow their containers */
  .fc .fc-daygrid-day {
    overflow: hidden !important;
  }
  
  .fc .fc-daygrid-day-frame {
    overflow: hidden !important;
  }
  
  /* Dark mode specific improvements */
  .dark .fc .fc-daygrid-day {
    background-color: #1f2937 !important;
  }
  
  .dark .fc .fc-daygrid-day-frame {
    background-color: #1f2937 !important;
  }
  
  .dark .fc .fc-col-header-cell {
    background-color: #374151 !important;
    color: #ffffff !important;
    border-color: #4b5563 !important;
  }
  
  .dark .fc .fc-daygrid-day-number {
    color: #ffffff !important;
  }
  
  .dark .fc .fc-timegrid-slot {
    border-color: #4b5563 !important;
  }
  
  .dark .fc .fc-timegrid-slot-label {
    color: #9ca3af !important;
  }
  
  /* Ensure proper contrast for event text */
  .dark .fc-event {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  }
  
  /* Better event colors in dark mode */
  .dark .fc-event.bg-orange-600 {
    background-color: #ea580c !important;
  }
  
  .dark .fc-event.bg-green-600 {
    background-color: #16a34a !important;
  }
  
  .dark .fc-event.bg-blue-600 {
    background-color: #2563eb !important;
  }
  
  .dark .fc-event.bg-purple-600 {
    background-color: #9333ea !important;
  }
  
  /* Additional spacing and layout improvements */
  .fc .fc-daygrid-day-events {
    margin-top: 12px !important;
    position: relative !important;
    z-index: 1 !important;
  }
  
  .fc .fc-daygrid-day-top {
    margin-bottom: 8px !important;
    position: relative !important;
    z-index: 2 !important;
  }
  
  /* Ensure proper event spacing */
  .fc .fc-daygrid-event-harness + .fc .fc-daygrid-event-harness {
    margin-top: 4px !important;
  }
  
  /* Better event content layout */
  .event-content-month > div,
  .event-content-week > div,
  .event-content-list > div {
    margin-bottom: 4px !important;
  }
  
  /* Ensure status badges don't overlap */
  .fc-event .bg-orange-600,
  .fc-event .bg-green-600 {
    margin-top: 4px !important;
    margin-bottom: 2px !important;
    display: inline-block !important;
    width: fit-content !important;
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }
  
  /* Better event title handling */
  .fc-event-title {
    margin-bottom: 4px !important;
    font-weight: 600 !important;
  }
  
  /* Ensure proper line breaks */
  .fc-event-main {
    white-space: normal !important;
    word-break: break-word !important;
  }
  
  /* Better event container */
  .fc-event-main-frame {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* Ensure events don't overflow their containers */
  .fc .fc-daygrid-day {
    overflow: hidden !important;
  }
  
  .fc .fc-daygrid-day-frame {
    overflow: hidden !important;
  }
  


  /* Status Dots */
  .status-dot {
    @apply inline-block w-3 h-3 rounded-full mr-2;
  }
  .status-dot-green {
    @apply bg-green-500 dark:bg-dark-success;
  }
  .status-dot-orange {
    @apply bg-orange-500 dark:bg-dark-warning;
  }
  .status-dot-red {
    @apply bg-red-500 dark:bg-dark-error;
  }
  .status-dot-gray {
    @apply bg-gray-500 dark:bg-gray-600;
  }

  /* Modals */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
    pointer-events: auto !important;
  }
  .modal-content {
    @apply bg-white dark:bg-dark-secondary rounded-lg shadow-sm p-6 w-full max-w-md border border-amspm-light-gray dark:border-dark-border text-amspm-text dark:text-dark-text;
    pointer-events: auto !important;
    position: relative;
    z-index: 60;
  }

  .modal-button {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative;
    z-index: 100;
  }

  /* Ensure all modal buttons and close buttons work */
  .modal-content button,
  .modal-overlay button {
    pointer-events: auto !important;
    cursor: pointer !important;
  }

  /* Loading Spinner */
  .spinner {
    @apply w-8 h-8 border-4 border-amspm-light-gray dark:border-dark-border rounded-full border-t-amspm-primary dark:border-t-dark-accent animate-spin mx-auto;
  }

  /* Pagination */
  .pagination {
    @apply flex justify-center items-center space-x-4 mt-6;
  }

  /* Toastify */
  .Toastify__toast--success {
    @apply bg-green-500 text-white rounded-lg !important;
  }
  .Toastify__toast--error {
    @apply bg-red-500 text-white rounded-lg !important;
  }

  /* Document Editor */
  .editor-container {
    @apply bg-white border border-gray-300 rounded-lg;
  }

  .editor-content {
    @apply p-4 outline-none;
  }

  .docx-content p {
    @apply my-2;
  }

  .docx-content h1 {
    @apply text-2xl font-bold mb-4 text-amspm-text uppercase;
  }

  .docx-content h2 {
    @apply text-xl font-semibold mb-3 text-amspm-text;
  }

  .docx-content h3 {
    @apply text-lg font-medium mb-2 text-amspm-text;
  }

  .docx-content ul {
    @apply list-disc pl-5 my-2;
  }

  .docx-content ol {
    @apply list-decimal pl-5 my-2;
  }

  .docx-content table {
    @apply border-collapse border border-gray-300 my-4 w-full;
  }

  .docx-content td, .docx-content th {
    @apply border border-gray-300 p-2;
  }

  .docx-content .caption {
    @apply text-sm text-gray-600 italic text-center my-2;
  }

  .docx-content .checkbox {
    @apply inline-block w-4 h-4 border border-gray-400 rounded align-middle mx-1;
  }

  .docx-content .doc-image {
    @apply max-w-full h-auto my-4 mx-auto block;
  }

  /* Empty paragraphs should still take up space */
  .docx-content p:empty {
    @apply h-5;
  }

  /* Utility Classes */
  .flex-center {
    @apply flex items-center justify-center;
  }
  .space-y-4 > * + * {
    @apply mt-4;
  }

  /* Responsive utilities */
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .responsive-grid-tight {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3;
  }

  .responsive-flex {
    @apply flex flex-col sm:flex-row flex-wrap gap-4;
  }

  .responsive-container {
    @apply w-full px-3 sm:px-4 md:px-6 py-3 sm:py-4 md:py-6;
  }

  .responsive-card {
    @apply bg-white dark:bg-dark-secondary p-3 sm:p-4 md:p-6 rounded-lg shadow-sm border border-amspm-light-gray dark:border-dark-border;
  }

  .responsive-text {
    @apply text-sm sm:text-base text-amspm-text dark:text-dark-text;
  }

  .responsive-text-light {
    @apply text-sm sm:text-base text-amspm-text-light dark:text-dark-text-light;
  }

  .responsive-heading {
    @apply text-lg sm:text-xl md:text-2xl font-bold text-amspm-primary dark:text-dark-accent;
  }

  .responsive-subheading {
    @apply text-base sm:text-lg font-semibold text-amspm-text dark:text-dark-text;
  }

  .touch-target {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }

  /* Mobile-specific spacing utilities */
  .mobile-spacing {
    @apply p-3 sm:p-4 md:p-6;
  }

  .mobile-margin {
    @apply m-2 sm:m-3 md:m-4;
  }

  .mobile-gap {
    @apply gap-2 sm:gap-3 md:gap-4;
  }

  /* Mobile navigation utilities */
  .mobile-nav-item {
    @apply block w-full text-left px-3 py-2 sm:px-4 sm:py-2 rounded-lg hover:bg-amspm-light-gray dark:hover:bg-dark-hover transition-colors duration-200 touch-manipulation;
  }

  /* Mobile table utilities */
  .mobile-table-wrapper {
    @apply overflow-x-auto -mx-2 sm:-mx-4;
  }

  .mobile-table {
    @apply min-w-full;
  }

  /* Mobile modal utilities */
  .mobile-modal {
    @apply w-full max-w-sm sm:max-w-md md:max-w-lg mx-auto;
  }

  /* Mobile header utilities */
  .mobile-header {
    @apply flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6;
  }

  .mobile-header-title {
    @apply text-xl sm:text-2xl md:text-3xl font-bold text-amspm-primary dark:text-dark-accent;
  }

  .mobile-header-actions {
    @apply flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto;
  }

  /* Dark mode specific utilities */
  .dark-mode-text {
    @apply text-amspm-text dark:text-dark-text;
  }

  .dark-mode-text-light {
    @apply text-amspm-text-light dark:text-dark-text-light;
  }

  .dark-mode-bg {
    @apply bg-white dark:bg-dark-secondary;
  }

  .dark-mode-border {
    @apply border-amspm-light-gray dark:border-dark-border;
  }

  /* Animations */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
