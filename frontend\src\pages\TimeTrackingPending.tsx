import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useConfirmation } from '../context/ConfirmationContext';
import {
  getPendingTimeEntries,
  getPendingMileageEntries,
  approveTimeEntry,
  rejectTimeEntry,
  approveMileageEntry,
  rejectMileageEntry
} from '../services/timeTrackingService';
import { getAllUsers } from '../services/userService';
import { TimeEntry } from '../types/timeEntry';
import { MileageEntry } from '../types/mileageEntry';
import { User } from '../types/user';
import LoadingSpinner from '../components/LoadingSpinner';
import SortableTableHeader from '../components/common/SortableTableHeader';
import useSortableData from '../hooks/useSortableData';
import {
  FaUser<PERSON><PERSON>,
  FaArrowLeft,
  Fa<PERSON><PERSON><PERSON>,
  FaT<PERSON>,
  <PERSON>a<PERSON>lock,
  <PERSON>aCar,
  <PERSON>a<PERSON>ser,
  FaFilter
} from 'react-icons/fa';
import { MobileContainer, MobileCard, MobileButtonGroup, MobilePageHeader } from '../components/common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

const TimeTrackingPending: React.FC = () => {
  const { user: currentUser } = useAuth();
  const navigate = useNavigate();
  const { showConfirmation } = useConfirmation();
  const { isMobile } = useMobile();

  // Data
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [mileageEntries, setMileageEntries] = useState<MileageEntry[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  // Totals
  const [totalPendingHours, setTotalPendingHours] = useState<number>(0);
  const [totalPendingKilometers, setTotalPendingKilometers] = useState<number>(0);
  const [totalApprovedHours, setTotalApprovedHours] = useState<number>(0);
  const [totalApprovedKilometers, setTotalApprovedKilometers] = useState<number>(0);

  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'time' | 'mileage'>('time');
  const [selectedUser, setSelectedUser] = useState<number | null>(null);

  // We'll use the useSortableData hook later with the filtered data

  // Pagination
  const [timeEntriesPage, setTimeEntriesPage] = useState(1);
  const [mileageEntriesPage, setMileageEntriesPage] = useState(1);
  const [timeEntriesTotal, setTimeEntriesTotal] = useState(0);
  const [mileageEntriesTotal, setMileageEntriesTotal] = useState(0);
  const perPage = 10;

  // Load data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch users
        const usersResponse = await getAllUsers();
        setUsers(usersResponse.users);

        // Fetch pending time entries
        const timeResponse = await getPendingTimeEntries(timeEntriesPage, perPage);
        setTimeEntries(timeResponse.entries);
        setTimeEntriesTotal(timeResponse.total);

        // Calculate total pending hours
        let pendingHours = 0;
        timeResponse.entries.forEach(entry => {
          const startTime = new Date(`2000-01-01T${entry.start_time}`);
          const endTime = new Date(`2000-01-01T${entry.end_time}`);
          const diffMs = endTime.getTime() - startTime.getTime();
          const diffHours = diffMs / (1000 * 60 * 60);
          pendingHours += diffHours;
        });
        setTotalPendingHours(Math.round(pendingHours * 100) / 100);

        // Fetch pending mileage entries
        const mileageResponse = await getPendingMileageEntries(mileageEntriesPage, perPage);
        setMileageEntries(mileageResponse.entries);
        setMileageEntriesTotal(mileageResponse.total);

        // Calculate total pending kilometers
        let pendingKilometers = 0;
        mileageResponse.entries.forEach(entry => {
          pendingKilometers += entry.kilometers;
        });
        setTotalPendingKilometers(Math.round(pendingKilometers * 100) / 100);

        // Initialize approved totals
        setTotalApprovedHours(0);
        setTotalApprovedKilometers(0);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Er is een fout opgetreden bij het ophalen van de gegevens.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeEntriesPage, mileageEntriesPage]);

  // Approval functions
  const handleApproveTimeEntry = async (entryId: number) => {
    showConfirmation({
      title: 'Uren goedkeuren',
      message: 'Weet je zeker dat je deze uren wilt goedkeuren?',
      confirmText: 'Goedkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          // Find the entry being approved
          const approvedEntry = timeEntries.find(entry => entry.id === entryId);

          if (approvedEntry) {
            // Calculate hours for this entry
            const startTime = new Date(`2000-01-01T${approvedEntry.start_time}`);
            const endTime = new Date(`2000-01-01T${approvedEntry.end_time}`);
            const diffMs = endTime.getTime() - startTime.getTime();
            const diffHours = diffMs / (1000 * 60 * 60);

            // Update totals
            setTotalApprovedHours(prev => Math.round((prev + diffHours) * 100) / 100);
            setTotalPendingHours(prev => Math.round((prev - diffHours) * 100) / 100);
          }

          await approveTimeEntry(entryId);
          // Update the entries list
          setTimeEntries(timeEntries.filter(entry => entry.id !== entryId));
          setTimeEntriesTotal(prev => prev - 1);
        } catch (err) {
          console.error('Error approving time entry:', err);
          setError('Er is een fout opgetreden bij het goedkeuren van de uren.');
        }
      }
    });
  };

  const handleRejectTimeEntry = async (entryId: number) => {
    showConfirmation({
      title: 'Uren afkeuren',
      message: 'Weet je zeker dat je deze uren wilt afkeuren?',
      confirmText: 'Afkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          // Find the entry being rejected
          const rejectedEntry = timeEntries.find(entry => entry.id === entryId);

          if (rejectedEntry) {
            // Calculate hours for this entry
            const startTime = new Date(`2000-01-01T${rejectedEntry.start_time}`);
            const endTime = new Date(`2000-01-01T${rejectedEntry.end_time}`);
            const diffMs = endTime.getTime() - startTime.getTime();
            const diffHours = diffMs / (1000 * 60 * 60);

            // Update totals - only need to reduce pending hours
            setTotalPendingHours(prev => Math.round((prev - diffHours) * 100) / 100);
          }

          await rejectTimeEntry(entryId);
          // Update the entries list
          setTimeEntries(timeEntries.filter(entry => entry.id !== entryId));
          setTimeEntriesTotal(prev => prev - 1);
        } catch (err) {
          console.error('Error rejecting time entry:', err);
          setError('Er is een fout opgetreden bij het afkeuren van de uren.');
        }
      }
    });
  };

  const handleApproveMileageEntry = async (entryId: number) => {
    showConfirmation({
      title: 'Kilometers goedkeuren',
      message: 'Weet je zeker dat je deze kilometers wilt goedkeuren?',
      confirmText: 'Goedkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          // Find the entry being approved
          const approvedEntry = mileageEntries.find(entry => entry.id === entryId);

          if (approvedEntry) {
            // Get kilometers for this entry
            const kilometers = approvedEntry.kilometers;

            // Update totals
            setTotalApprovedKilometers(prev => Math.round((prev + kilometers) * 100) / 100);
            setTotalPendingKilometers(prev => Math.round((prev - kilometers) * 100) / 100);
          }

          await approveMileageEntry(entryId);
          // Update the entries list
          setMileageEntries(mileageEntries.filter(entry => entry.id !== entryId));
          setMileageEntriesTotal(prev => prev - 1);
        } catch (err) {
          console.error('Error approving mileage entry:', err);
          setError('Er is een fout opgetreden bij het goedkeuren van de kilometers.');
        }
      }
    });
  };

  const handleRejectMileageEntry = async (entryId: number) => {
    showConfirmation({
      title: 'Kilometers afkeuren',
      message: 'Weet je zeker dat je deze kilometers wilt afkeuren?',
      confirmText: 'Afkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          // Find the entry being rejected
          const rejectedEntry = mileageEntries.find(entry => entry.id === entryId);

          if (rejectedEntry) {
            // Get kilometers for this entry
            const kilometers = rejectedEntry.kilometers;

            // Update totals - only need to reduce pending kilometers
            setTotalPendingKilometers(prev => Math.round((prev - kilometers) * 100) / 100);
          }

          await rejectMileageEntry(entryId);
          // Update the entries list
          setMileageEntries(mileageEntries.filter(entry => entry.id !== entryId));
          setMileageEntriesTotal(prev => prev - 1);
        } catch (err) {
          console.error('Error rejecting mileage entry:', err);
          setError('Er is een fout opgetreden bij het afkeuren van de kilometers.');
        }
      }
    });
  };

  // Helper functions
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('nl-NL', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5); // Extract HH:MM from HH:MM:SS
  };

  const calculateHours = (startTime: string, endTime: string) => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    return diffHours.toFixed(2);
  };

  // Filter entries by selected user
  const filteredTimeEntries = selectedUser
    ? timeEntries.filter(entry => entry.user_id === selectedUser)
    : [...timeEntries];

  const filteredMileageEntries = selectedUser
    ? mileageEntries.filter(entry => entry.user_id === selectedUser)
    : [...mileageEntries];

  // Custom sort functions for time entries
  const timeEntrySortFunctions = {
    hours: (a: TimeEntry, b: TimeEntry) => {
      const aHours = parseFloat(calculateHours(a.start_time, a.end_time));
      const bHours = parseFloat(calculateHours(b.start_time, b.end_time));
      return aHours - bHours;
    }
  };

  // Use the sortable data hook
  const {
    items: sortedTimeEntries,
    requestSort: requestTimeSort,
    sortConfig: timeSortConfig
  } = useSortableData<TimeEntry>(filteredTimeEntries, 'date', 'none', timeEntrySortFunctions);

  const {
    items: sortedMileageEntries,
    requestSort: requestMileageSort,
    sortConfig: mileageSortConfig
  } = useSortableData<MileageEntry>(filteredMileageEntries, 'date', 'none');

  // Check if the user is authorized
  if (!currentUser || currentUser.role !== 'administrator') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Je hebt geen toegang tot deze pagina. Alleen administrators kunnen deze pagina bekijken.
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header with back button */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <button
            onClick={() => navigate('/time-tracking-admin')}
            className="mr-4 text-amspm-text dark:text-dark-text hover:text-amspm-primary dark:hover:text-amspm-primary"
            aria-label="Terug naar overzicht"
          >
            <FaArrowLeft size={20} />
          </button>
          <h1 className="text-2xl font-bold text-amspm-text dark:text-dark-text">
            <FaUserClock className="inline mr-2" /> Goedkeuring uren en kilometers
          </h1>
        </div>

        {/* Tab selector */}
        <div className="flex space-x-2">
          <button
            onClick={() => setActiveTab('time')}
            className={`btn btn-sm ${activeTab === 'time' ? 'btn-primary' : 'btn-secondary'}`}
          >
            <FaClock className="mr-1" /> Uren
          </button>
          <button
            onClick={() => setActiveTab('mileage')}
            className={`btn btn-sm ${activeTab === 'mileage' ? 'btn-primary' : 'btn-secondary'}`}
          >
            <FaCar className="mr-1" /> Kilometers
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">
            {activeTab === 'time' ? 'Openstaande uren' : 'Openstaande kilometers'}
          </h2>

          <div className="flex items-center space-x-2">
            <div className="relative">
              <select
                value={selectedUser || ''}
                onChange={(e) => setSelectedUser(e.target.value ? parseInt(e.target.value) : null)}
                className="input pr-8"
              >
                <option value="">Alle gebruikers</option>
                {users.map(user => (
                  <option key={user.id} value={user.id}>
                    {user.name || user.email}
                  </option>
                ))}
              </select>
              <FaUser className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Summary cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Uren sectie */}
          <div className="bg-white dark:bg-dark-card rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-3 flex items-center">
              <FaClock className="mr-2" /> Uren
            </h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-4 flex items-center">
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-blue-700 dark:text-blue-200">Goedgekeurd</h4>
                  <p className="text-2xl font-bold text-blue-800 dark:text-blue-100">{totalApprovedHours.toFixed(2)}</p>
                </div>
              </div>
              <div className="bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4 flex items-center">
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-yellow-700 dark:text-yellow-200">Openstaand</h4>
                  <p className="text-2xl font-bold text-yellow-800 dark:text-yellow-100">{totalPendingHours.toFixed(2)}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Kilometers sectie */}
          <div className="bg-white dark:bg-dark-card rounded-lg p-4 shadow-sm">
            <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-3 flex items-center">
              <FaCar className="mr-2" /> Kilometers
            </h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4 flex items-center">
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-green-700 dark:text-green-200">Goedgekeurd</h4>
                  <p className="text-2xl font-bold text-green-800 dark:text-green-100">{totalApprovedKilometers.toFixed(2)}</p>
                </div>
              </div>
              <div className="bg-orange-50 dark:bg-orange-900 rounded-lg p-4 flex items-center">
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-orange-700 dark:text-orange-200">Openstaand</h4>
                  <p className="text-2xl font-bold text-orange-800 dark:text-orange-100">{totalPendingKilometers.toFixed(2)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {loading ? (
          <LoadingSpinner />
        ) : (
          <>
            {activeTab === 'time' && (
              <>
                {filteredTimeEntries.length > 0 ? (
                  <>
                    {/* Desktop Table View */}
                    <div className="hidden md:block overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <SortableTableHeader
                            label="Gebruiker"
                            field="user_name"
                            currentSortField={timeSortConfig.field}
                            sortDirection={timeSortConfig.direction}
                            onSort={requestTimeSort}
                          />
                          <SortableTableHeader
                            label="Datum"
                            field="date"
                            currentSortField={timeSortConfig.field}
                            sortDirection={timeSortConfig.direction}
                            onSort={requestTimeSort}
                          />
                          <SortableTableHeader
                            label="Tijd"
                            field="start_time"
                            currentSortField={timeSortConfig.field}
                            sortDirection={timeSortConfig.direction}
                            onSort={requestTimeSort}
                          />
                          <SortableTableHeader
                            label="Uren"
                            field="hours"
                            currentSortField={timeSortConfig.field}
                            sortDirection={timeSortConfig.direction}
                            onSort={requestTimeSort}
                          />
                          <SortableTableHeader
                            label="Omschrijving"
                            field="description"
                            currentSortField={timeSortConfig.field}
                            sortDirection={timeSortConfig.direction}
                            onSort={requestTimeSort}
                          />
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Acties
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        {sortedTimeEntries.map(entry => (
                          <tr key={entry.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.user_name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatDate(entry.date)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatTime(entry.start_time)} - {formatTime(entry.end_time)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {calculateHours(entry.start_time, entry.end_time)}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                              {entry.description || <span className="text-gray-500 italic">Geen omschrijving</span>}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleApproveTimeEntry(entry.id)}
                                  className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                  title="Goedkeuren"
                                >
                                  <FaCheck />
                                </button>
                                <button
                                  onClick={() => handleRejectTimeEntry(entry.id)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  title="Afkeuren"
                                >
                                  <FaTimes />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    </div>

                    {/* Mobile Card View */}
                    <div className="md:hidden space-y-4">
                      {sortedTimeEntries.map(entry => (
                        <MobileCard key={entry.id}>
                          <div className="mobile-card-header">
                            <div className="flex-1">
                              <h3 className="mobile-card-title">{entry.user_name}</h3>
                              <p className="text-sm text-gray-500 dark:text-dark-text-light">
                                {formatDate(entry.date)}
                              </p>
                            </div>
                            <div className="text-right">
                              <span className="text-sm font-medium text-amspm-primary dark:text-dark-accent">
                                {calculateHours(entry.start_time, entry.end_time)} uur
                              </span>
                            </div>
                          </div>

                          <div className="mobile-card-content">
                            <div className="mobile-card-row">
                              <span className="mobile-card-label">Tijd:</span>
                              <span className="mobile-card-value">
                                {formatTime(entry.start_time)} - {formatTime(entry.end_time)}
                              </span>
                            </div>

                            <div className="mobile-card-row">
                              <span className="mobile-card-label">Omschrijving:</span>
                              <span className="mobile-card-value">
                                {entry.description || <span className="text-gray-500 italic">Geen omschrijving</span>}
                              </span>
                            </div>
                          </div>

                          <div className="mobile-card-actions">
                            <MobileButtonGroup direction="horizontal">
                              <button
                                onClick={() => handleApproveTimeEntry(entry.id)}
                                className="btn bg-green-600 hover:bg-green-700 text-white border-none flex items-center justify-center mobile-touch-target"
                              >
                                <FaCheck className="mr-2" />
                                Goedkeuren
                              </button>
                              <button
                                onClick={() => handleRejectTimeEntry(entry.id)}
                                className="btn bg-red-600 hover:bg-red-700 text-white border-none flex items-center justify-center mobile-touch-target"
                              >
                                <FaTimes className="mr-2" />
                                Afkeuren
                              </button>
                            </MobileButtonGroup>
                          </div>
                        </MobileCard>
                      ))}
                    </div>
                  </>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                    {selectedUser
                      ? 'Geen openstaande uren gevonden voor deze gebruiker.'
                      : 'Geen openstaande uren gevonden.'}
                  </p>
                )}

                {/* Pagination for time entries */}
                {timeEntriesTotal > perPage && !selectedUser && (
                  <div className="mt-6 flex justify-center">
                    <nav className="flex items-center">
                      <button
                        onClick={() => setTimeEntriesPage(prev => Math.max(prev - 1, 1))}
                        disabled={timeEntriesPage === 1}
                        className="px-3 py-1 rounded-md mr-2 bg-gray-200 dark:bg-gray-700 disabled:opacity-50"
                      >
                        Vorige
                      </button>
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Pagina {timeEntriesPage} van {Math.ceil(timeEntriesTotal / perPage)}
                      </span>
                      <button
                        onClick={() => setTimeEntriesPage(prev => prev + 1)}
                        disabled={timeEntriesPage >= Math.ceil(timeEntriesTotal / perPage)}
                        className="px-3 py-1 rounded-md ml-2 bg-gray-200 dark:bg-gray-700 disabled:opacity-50"
                      >
                        Volgende
                      </button>
                    </nav>
                  </div>
                )}
              </>
            )}

            {activeTab === 'mileage' && (
              <>
                {filteredMileageEntries.length > 0 ? (
                  <>
                    {/* Desktop Table View */}
                    <div className="hidden md:block overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <SortableTableHeader
                            label="Gebruiker"
                            field="user_name"
                            currentSortField={mileageSortConfig.field}
                            sortDirection={mileageSortConfig.direction}
                            onSort={requestMileageSort}
                          />
                          <SortableTableHeader
                            label="Datum"
                            field="date"
                            currentSortField={mileageSortConfig.field}
                            sortDirection={mileageSortConfig.direction}
                            onSort={requestMileageSort}
                          />
                          <SortableTableHeader
                            label="Kilometers"
                            field="kilometers"
                            currentSortField={mileageSortConfig.field}
                            sortDirection={mileageSortConfig.direction}
                            onSort={requestMileageSort}
                          />
                          <SortableTableHeader
                            label="Omschrijving"
                            field="description"
                            currentSortField={mileageSortConfig.field}
                            sortDirection={mileageSortConfig.direction}
                            onSort={requestMileageSort}
                          />
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Acties
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        {sortedMileageEntries.map(entry => (
                          <tr key={entry.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.user_name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatDate(entry.date)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.kilometers.toFixed(2)}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                              {entry.description || <span className="text-gray-500 italic">Geen omschrijving</span>}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleApproveMileageEntry(entry.id)}
                                  className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                  title="Goedkeuren"
                                >
                                  <FaCheck />
                                </button>
                                <button
                                  onClick={() => handleRejectMileageEntry(entry.id)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  title="Afkeuren"
                                >
                                  <FaTimes />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    </div>

                    {/* Mobile Card View */}
                    <div className="md:hidden space-y-4">
                      {sortedMileageEntries.map(entry => (
                        <MobileCard key={entry.id}>
                          <div className="mobile-card-header">
                            <div className="flex-1">
                              <h3 className="mobile-card-title">{entry.user_name}</h3>
                              <p className="text-sm text-gray-500 dark:text-dark-text-light">
                                {formatDate(entry.date)}
                              </p>
                            </div>
                            <div className="text-right">
                              <span className="text-sm font-medium text-amspm-primary dark:text-dark-accent">
                                {entry.kilometers.toFixed(2)} km
                              </span>
                            </div>
                          </div>

                          <div className="mobile-card-content">
                            <div className="mobile-card-row">
                              <span className="mobile-card-label">Omschrijving:</span>
                              <span className="mobile-card-value">
                                {entry.description || <span className="text-gray-500 italic">Geen omschrijving</span>}
                              </span>
                            </div>
                          </div>

                          <div className="mobile-card-actions">
                            <MobileButtonGroup direction="horizontal">
                              <button
                                onClick={() => handleApproveMileageEntry(entry.id)}
                                className="btn bg-green-600 hover:bg-green-700 text-white border-none flex items-center justify-center mobile-touch-target"
                              >
                                <FaCheck className="mr-2" />
                                Goedkeuren
                              </button>
                              <button
                                onClick={() => handleRejectMileageEntry(entry.id)}
                                className="btn bg-red-600 hover:bg-red-700 text-white border-none flex items-center justify-center mobile-touch-target"
                              >
                                <FaTimes className="mr-2" />
                                Afkeuren
                              </button>
                            </MobileButtonGroup>
                          </div>
                        </MobileCard>
                      ))}
                    </div>
                  </>
                ) : (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                    {selectedUser
                      ? 'Geen openstaande kilometers gevonden voor deze gebruiker.'
                      : 'Geen openstaande kilometers gevonden.'}
                  </p>
                )}

                {/* Pagination for mileage entries */}
                {mileageEntriesTotal > perPage && !selectedUser && (
                  <div className="mt-6 flex justify-center">
                    <nav className="flex items-center">
                      <button
                        onClick={() => setMileageEntriesPage(prev => Math.max(prev - 1, 1))}
                        disabled={mileageEntriesPage === 1}
                        className="px-3 py-1 rounded-md mr-2 bg-gray-200 dark:bg-gray-700 disabled:opacity-50"
                      >
                        Vorige
                      </button>
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Pagina {mileageEntriesPage} van {Math.ceil(mileageEntriesTotal / perPage)}
                      </span>
                      <button
                        onClick={() => setMileageEntriesPage(prev => prev + 1)}
                        disabled={mileageEntriesPage >= Math.ceil(mileageEntriesTotal / perPage)}
                        className="px-3 py-1 rounded-md ml-2 bg-gray-200 dark:bg-gray-700 disabled:opacity-50"
                      >
                        Volgende
                      </button>
                    </nav>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default TimeTrackingPending;