import React from 'react';
import { Event } from '../types/event';
import { 
  FaCalendarAlt, 
  FaUser, 
  FaUsers, 
  FaBuilding, 
  FaClock, 
  FaCheckCircle, 
  FaEdit, 
  FaTimes,
  FaMapMarkerAlt,
  FaFileAlt,
  FaExclamationTriangle,
  FaTrash,
  FaPhone,
  FaEnvelope,
  FaIdCard,
  FaCalendarDay,
  FaUserTie,
  FaInfoCircle
} from 'react-icons/fa';

interface EventDetailsModalProps {
  event: Event;
  onClose: () => void;
  onEdit?: () => void;
  onComplete?: () => void;
  onDelete?: () => void;
  canEdit?: boolean;
  canComplete?: boolean;
  canDelete?: boolean;
}

const EventDetailsModal: React.FC<EventDetailsModalProps> = ({
  event,
  onClose,
  onEdit,
  onComplete,
  onDelete,
  canEdit = true,
  canComplete = true,
  canDelete = false
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('nl-NL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100 border-green-200';
      case 'pending':
        return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'cancelled':
        return 'text-red-600 bg-red-100 border-red-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getEventTypeColor = (eventType: string | null) => {
    if (!eventType) return 'text-gray-600 bg-gray-100 border-gray-200';
    
    switch (eventType) {
      case 'offerte':
        return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'werkbon':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'onderhoudsbon':
        return 'text-purple-600 bg-purple-100 border-purple-200';
      case 'factuur':
        return 'text-red-600 bg-red-100 border-red-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const isOverdue = event.status === 'pending' && new Date(event.scheduled_date) < new Date();

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 p-2 sm:p-4 flex items-center justify-center z-50"
      onClick={onClose}
    >
      <div
        className="bg-white shadow-xl rounded-lg w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex justify-between items-start p-4 sm:p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex-1 min-w-0">
            <div className="flex items-start space-x-3 mb-3">
              <div className="h-10 w-10 sm:h-12 sm:w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <FaCalendarAlt className="text-white" size={20} />
              </div>
              <div className="min-w-0 flex-1">
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate">Event Details</h2>
                <div className="flex flex-wrap items-center gap-2 mt-2">
                  <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium border ${getStatusColor(event.status)}`}>
                    {event.status === 'completed' ? '✅ Completed' : '⏳ Pending'}
                  </span>
                  <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium border capitalize ${getEventTypeColor(event.event_type)}`}>
                    {event.event_type ? event.event_type.replace(/_/g, ' ') : 'Algemeen'}
                  </span>
                  {isOverdue && (
                    <span className="px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium text-red-600 bg-red-100 border border-red-200 flex items-center">
                      <FaExclamationTriangle className="mr-1" size={10} />
                      Overdue
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-full flex-shrink-0"
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Event Information */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            <div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                <FaInfoCircle className="mr-2 text-blue-600 flex-shrink-0" size={18} />
                <span className="truncate">Event Information</span>
              </h3>
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 space-y-3 sm:space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Event Type</label>
                  <p className="text-base sm:text-lg font-medium text-gray-900 capitalize mt-1">
                    {event.event_type ? event.event_type.replace(/_/g, ' ') : 'Algemeen'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Description</label>
                  <p className="text-gray-900 mt-1 leading-relaxed break-words">{event.description}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Event ID</label>
                  <p className="text-gray-900 font-mono mt-1 text-sm">#{event.id}</p>
                </div>
              </div>
            </div>

            {/* Schedule */}
            <div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                <FaClock className="mr-2 text-green-600 flex-shrink-0" size={18} />
                <span className="truncate">Schedule</span>
              </h3>
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4 space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-600">Scheduled Date</label>
                  <p className="text-gray-900 font-medium mt-1 text-sm sm:text-base break-words">{formatDate(event.scheduled_date)}</p>
                </div>
                {isOverdue && (
                  <div className="bg-red-50 border border-red-200 rounded p-3">
                    <p className="text-red-700 text-sm font-medium">
                      ⚠️ This event is overdue by {Math.ceil((new Date().getTime() - new Date(event.scheduled_date).getTime()) / (1000 * 60 * 60 * 24))} days
                    </p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium text-gray-600">Created Date</label>
                  <p className="text-gray-900 mt-1 text-sm sm:text-base break-words">{event.created_at ? formatDate(event.created_at) : 'N/A'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Customer Information - Enhanced */}
          {event.customer_name && (
            <div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                <FaBuilding className="mr-2 text-purple-600 flex-shrink-0" size={18} />
                <span className="truncate">Customer Information</span>
              </h3>
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 sm:p-6 border border-purple-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-3 sm:space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Customer Name</label>
                      <p className="text-lg sm:text-xl font-bold text-gray-900 mt-1 break-words">{event.customer_name}</p>
                    </div>
                    {event.customer_address && (
                      <div>
                        <label className="text-sm font-medium text-gray-600 flex items-center">
                          <FaMapMarkerAlt className="mr-2 flex-shrink-0" size={14} />
                          Address
                        </label>
                        <p className="text-gray-900 mt-1 leading-relaxed break-words">{event.customer_address}</p>
                      </div>
                    )}
                    {event.customer_id && (
                      <div>
                        <label className="text-sm font-medium text-gray-600 flex items-center">
                          <FaIdCard className="mr-2 flex-shrink-0" size={14} />
                          Customer ID
                        </label>
                        <p className="text-gray-900 font-mono mt-1 text-sm">#{event.customer_id}</p>
                      </div>
                    )}
                  </div>
                  <div className="space-y-3 sm:space-y-4">
                    {/* Additional customer fields can be added here */}
                    <div className="bg-white rounded-lg p-3 sm:p-4 border border-purple-200">
                      <h4 className="font-medium text-gray-900 mb-2 text-sm sm:text-base">Quick Actions</h4>
                      <div className="space-y-2">
                        <button className="w-full text-left text-xs sm:text-sm text-purple-600 hover:text-purple-800 hover:bg-purple-50 p-2 rounded transition-colors">
                          📋 View Customer Profile
                        </button>
                        <button className="w-full text-left text-xs sm:text-sm text-purple-600 hover:text-purple-800 hover:bg-purple-50 p-2 rounded transition-colors">
                          📄 View Customer Documents
                        </button>
                        <button className="w-full text-left text-xs sm:text-sm text-purple-600 hover:text-purple-800 hover:bg-purple-50 p-2 rounded transition-colors">
                          📅 View Customer Events
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Assigned Users - Enhanced */}
          {event.user_names && event.user_names.length > 0 && (
            <div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                <FaUsers className="mr-2 text-indigo-600 flex-shrink-0" size={18} />
                <span className="truncate">Assigned Users ({event.user_names.length})</span>
              </h3>
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                  {event.user_names.map((userName, index) => (
                    <div key={index} className="bg-white rounded-lg p-3 sm:p-4 border border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <FaUser className="text-white" size={14} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 text-sm sm:text-base truncate">{userName}</p>
                          {event.user_emails && event.user_emails[index] && (
                            <p className="text-xs sm:text-sm text-gray-600 truncate">{event.user_emails[index]}</p>
                          )}
                          {event.user_ids && event.user_ids[index] && (
                            <p className="text-xs text-gray-500 font-mono">ID: {event.user_ids[index]}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Completion Information */}
          {event.status === 'completed' && event.completed_at && (
            <div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                <FaCheckCircle className="mr-2 text-green-600 flex-shrink-0" size={18} />
                <span className="truncate">Completion Details</span>
              </h3>
              <div className="bg-green-50 rounded-lg p-3 sm:p-4 border border-green-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <label className="text-sm font-medium text-green-700">Completed on</label>
                    <p className="text-green-900 font-medium mt-1 text-sm sm:text-base break-words">{formatDate(event.completed_at)}</p>
                  </div>
                  {event.completed_by_name && (
                    <div>
                      <label className="text-sm font-medium text-green-700">Completed by</label>
                      <p className="text-green-900 font-medium mt-1 text-sm sm:text-base break-words">{event.completed_by_name}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Document Information */}
          {event.document && (
            <div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center">
                <FaFileAlt className="mr-2 text-orange-600 flex-shrink-0" size={18} />
                <span className="truncate">Related Document</span>
              </h3>
              <div className="bg-orange-50 rounded-lg p-3 sm:p-4 border border-orange-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <label className="text-sm font-medium text-orange-700">Document Type</label>
                    <p className="text-orange-900 font-medium mt-1 capitalize text-sm sm:text-base break-words">{event.document.document_type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-orange-700">Filename</label>
                    <p className="text-orange-900 font-medium mt-1 text-sm sm:text-base break-words">{event.document.name}</p>
                  </div>
                  {event.document.expiry_date && (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-orange-700">Expiry Date</label>
                      <p className="text-orange-900 font-medium mt-1 text-sm sm:text-base">
                        {new Date(event.document.expiry_date).toLocaleDateString('nl-NL')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Actions - Enhanced */}
        <div className="bg-gray-50 border-t border-gray-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3">
            {canDelete && (
              <button
                onClick={onDelete}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
              >
                <FaTrash className="mr-2" size={14} />
                <span className="truncate">Delete Event</span>
              </button>
            )}
            {canComplete && event.status === 'pending' && (
              <button
                onClick={onComplete}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
              >
                <FaCheckCircle className="mr-2" size={14} />
                <span className="truncate">Complete Event</span>
              </button>
            )}
            {canEdit && (
              <button
                onClick={onEdit}
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                <FaEdit className="mr-2" size={14} />
                <span className="truncate">Edit Event</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventDetailsModal;
