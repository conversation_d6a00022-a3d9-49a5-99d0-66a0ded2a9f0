import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaShieldAlt, FaVideo, FaCogs, FaArrowLeft, FaUser } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import {
  MobileContainer,
  MobilePageHeader,
  MobileFormGroup,
  MobileInput,
  ProgressSteps,
  ProductCard,
  InfoCard,
  EnhancedButton
} from '../components/common/MobileUtils';

interface CustomerData {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_address: string;
  customer_city: string;
  customer_postal_code: string;
}

const SimpleQuotePage: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<'customer' | 'category'>('customer');
  const [customerData, setCustomerData] = useState<CustomerData>({
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    customer_address: '',
    customer_city: '',
    customer_postal_code: ''
  });

  const categories = [
    {
      id: 'alarm',
      title: 'ALARM',
      description: 'Basis alarmsysteem - VERPLICHT minimaal pakket voor beveiliging',
      icon: <FaShieldAlt className="text-5xl text-red-500" />,
      color: 'border-red-500 hover:bg-red-50 hover:border-red-600',
      gradient: 'from-red-50 to-red-100',
      features: [
        '1x CENTRALE HUB',
        '1x BEDIENDEEL',
        '1x SIRENE',
        '2x PIRCAM',
        '2x SHOCK SENSOR',
        '1x MAGNEETCONTACT',
        '2x BRANDMELDER'
      ],
      basePrice: '€999,99',
      monthlyPrice: '€49,99',
      maxDiscount: '25% (€749,99 min)',
      popular: true
    },
    {
      id: 'cameras',
      title: 'CAMERAS',
      description: 'Camera bewakingssysteem voor visuele beveiliging',
      icon: <FaVideo className="text-5xl text-blue-500" />,
      color: 'border-blue-500 hover:bg-blue-50 hover:border-blue-600',
      gradient: 'from-blue-50 to-blue-100',
      features: [
        'IP Camera\'s binnen/buiten',
        'Network Video Recorder',
        'Mobiele app toegang',
        'Cloud opslag optie',
        'Nachtzicht functie',
        '+ Video Deurbel optie'
      ],
      basePrice: 'Op maat',
      monthlyPrice: 'Op maat',
      maxDiscount: 'Variabel',
      popular: false
    },
    {
      id: 'alarm_cameras',
      title: 'ALARM + CAMERAS',
      description: 'Complete beveiligingsoplossing - Alarm basis + Camera systeem',
      icon: <FaCogs className="text-5xl text-green-500" />,
      color: 'border-green-500 hover:bg-green-50 hover:border-green-600',
      gradient: 'from-green-50 to-green-100',
      features: [
        'Volledige ALARM basis pakket',
        'IP Camera systeem',
        'Geïntegreerde bediening',
        'Mobiele app controle',
        'Professionele monitoring',
        '+ Video Deurbel optie'
      ],
      basePrice: 'Vanaf €999,99',
      monthlyPrice: 'Vanaf €49,99',
      maxDiscount: '25% op alarm deel',
      popular: false
    }
  ];

  const handleCustomerSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!customerData.customer_name.trim()) {
      alert('Naam is verplicht');
      return;
    }
    setStep('category');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCustomerData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCategorySelect = (categoryId: string) => {
    // Navigate to quote builder with customer data and selected category
    navigate('/simple-quotes/builder', {
      state: {
        customerData,
        category: categoryId
      }
    });
  };

  if (step === 'customer') {
    const progressSteps = [
      { id: 'customer', label: 'Klantgegevens', status: 'current' as const },
      { id: 'system', label: 'Systeem kiezen', status: 'upcoming' as const },
      { id: 'config', label: 'Configureren', status: 'upcoming' as const }
    ];

    return (
      <MobileContainer>
        <Breadcrumbs />

        <MobilePageHeader
          title="Nieuwe Offerte"
          subtitle="Vul eerst uw contactgegevens in voor een persoonlijke offerte"
        />

        <div className="space-y-6">
          {/* Back button */}
          <div className="flex items-center">
            <EnhancedButton
              variant="outline"
              size="sm"
              onClick={() => navigate(-1)}
              icon={<FaArrowLeft />}
            >
              Terug
            </EnhancedButton>
          </div>

          {/* Progress indicator */}
          <ProgressSteps steps={progressSteps} />

          {/* Customer form */}
          <div className="bg-white dark:bg-dark-secondary rounded-xl shadow-sm border border-gray-200 dark:border-dark-border p-4 sm:p-6">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center mr-4">
                <FaUser className="text-xl" />
              </div>
              <div>
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-dark-text">Uw contactgegevens</h3>
                <p className="text-sm text-gray-600 dark:text-dark-text-light">Vul uw gegevens in voor een persoonlijke offerte</p>
              </div>
            </div>

            <form onSubmit={handleCustomerSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <MobileFormGroup
                  label="Volledige naam"
                  required
                  helpText="Bijv. Jan de Vries"
                >
                  <MobileInput
                    type="text"
                    name="customer_name"
                    value={customerData.customer_name}
                    onChange={handleInputChange}
                    required
                    placeholder="Vul uw volledige naam in"
                  />
                </MobileFormGroup>

                <MobileFormGroup
                  label="Email adres"
                  helpText="Voor bevestiging van uw offerte"
                >
                  <MobileInput
                    type="email"
                    name="customer_email"
                    value={customerData.customer_email}
                    onChange={handleInputChange}
                    placeholder="bijv. <EMAIL>"
                  />
                </MobileFormGroup>

                <MobileFormGroup
                  label="Telefoonnummer"
                  helpText="Voor contact over de installatie"
                >
                  <MobileInput
                    type="tel"
                    name="customer_phone"
                    value={customerData.customer_phone}
                    onChange={handleInputChange}
                    placeholder="06-12345678"
                  />
                </MobileFormGroup>

                <MobileFormGroup
                  label="Postcode"
                  helpText="Voor installatiekosten berekening"
                >
                  <MobileInput
                    type="text"
                    name="customer_postal_code"
                    value={customerData.customer_postal_code}
                    onChange={handleInputChange}
                    placeholder="1234 AB"
                  />
                </MobileFormGroup>

                <MobileFormGroup label="Plaats">
                  <MobileInput
                    type="text"
                    name="customer_city"
                    value={customerData.customer_city}
                    onChange={handleInputChange}
                    placeholder="Amsterdam"
                  />
                </MobileFormGroup>

                <MobileFormGroup label="Straat en huisnummer">
                  <MobileInput
                    type="text"
                    name="customer_address"
                    value={customerData.customer_address}
                    onChange={handleInputChange}
                    placeholder="Hoofdstraat 123"
                  />
                </MobileFormGroup>
              </div>

              <div className="flex justify-end pt-6">
                <EnhancedButton
                  type="submit"
                  variant="primary"
                  size="lg"
                  icon={<FaArrowLeft className="rotate-180" />}
                  className="w-full sm:w-auto"
                >
                  Verder naar systeem keuze
                </EnhancedButton>
              </div>
            </form>
          </div>

          {/* Info section */}
          <InfoCard
            title="Waarom eerst uw gegevens?"
            icon={<FaShieldAlt />}
            variant="info"
          >
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Uw gegevens worden automatisch in de offerte verwerkt</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Persoonlijke offerte met uw naam en adres</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Sneller proces zonder dubbel invoeren</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Uw gegevens worden veilig opgeslagen</span>
              </li>
            </ul>
          </InfoCard>
        </div>
      </MobileContainer>
    );
  }

  // Category selection step
  const progressSteps = [
    { id: 'customer', label: 'Klantgegevens', status: 'completed' as const },
    { id: 'system', label: 'Systeem kiezen', status: 'current' as const },
    { id: 'config', label: 'Configureren', status: 'upcoming' as const }
  ];

  return (
    <MobileContainer>
      <Breadcrumbs />

      <MobilePageHeader
        title={`Hallo ${customerData.customer_name}!`}
        subtitle="Kies het beveiligingssysteem dat het beste bij u past"
      />

      <div className="space-y-6">
        {/* Back button and progress */}
        <div className="flex items-center justify-between">
          <EnhancedButton
            variant="outline"
            size="sm"
            onClick={() => setStep('customer')}
            icon={<FaArrowLeft />}
          >
            Gegevens wijzigen
          </EnhancedButton>
        </div>

        {/* Progress indicator */}
        <ProgressSteps steps={progressSteps} />

        {/* Category selection */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {categories.map((category) => (
            <ProductCard
              key={category.id}
              title={category.title}
              description={category.description}
              price={category.basePrice}
              monthlyPrice={category.monthlyPrice}
              features={category.features}
              icon={category.icon}
              popular={category.popular}
              onClick={() => handleCategorySelect(category.id)}
              gradient={category.gradient}
              borderColor={category.color}
            />
          ))}
        </div>

        {/* Additional info */}
        <InfoCard
          title="Belangrijke informatie"
          variant="info"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Alle prijzen zijn inclusief BTW</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Installatiekosten zijn eenmalig</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Maandelijks: apparatuur + meldkamer + onderhoud</span>
              </li>
            </ul>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Tot 25% korting bij betaling in 1 termijn</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Video deurbel kan gratis toegevoegd worden</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Professionele installatie inbegrepen</span>
              </li>
            </ul>
          </div>
        </InfoCard>

        {/* Contact info */}
        <div className="text-center bg-gray-50 dark:bg-dark-tertiary rounded-xl p-4 border border-gray-200 dark:border-dark-border">
          <div className="flex items-center justify-center mb-2">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-sm text-gray-600 dark:text-dark-text-light">Vragen over de offerte?</span>
          </div>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4">
            <a href="tel:020-1234567" className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors">
              📞 020-1234567
            </a>
            <span className="hidden sm:inline text-gray-400">|</span>
            <a href="mailto:<EMAIL>" className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors">
              ✉️ <EMAIL>
            </a>
          </div>
        </div>
      </div>
    </MobileContainer>
  );
};

export default SimpleQuotePage;
