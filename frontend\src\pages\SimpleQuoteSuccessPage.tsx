import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaCheckCircle, FaFileDownload, FaHome, FaPlus, FaClock, FaPhone, FaEnvelope, FaStar } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import {
  MobileContainer,
  MobilePageHeader,
  InfoCard,
  EnhancedButton
} from '../components/common/MobileUtils';
import { auth } from '../firebase';

const SimpleQuoteSuccessPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const { customerId, customerName, documentId, documentName, quoteNumber } = location.state || {};

  // Redirect if no data
  if (!customerId || !documentId) {
    navigate('/simple-quotes');
    return null;
  }

  const handleDownloadPDF = async () => {
    try {
      // Use the correct document file download API
      const response = await fetch(`/api/documents/${documentId}/file`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${await auth.currentUser?.getIdToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to download document');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = documentName || 'offerte.pdf';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert('Fout bij downloaden van PDF');
    }
  };

  return (
    <MobileContainer>
      <Breadcrumbs />
      
      <MobilePageHeader
        title="Offerte Succesvol Aangemaakt"
        subtitle="Uw offerte is ondertekend en opgeslagen"
      />

      <div className="space-y-6">
        {/* Success message with animation */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl p-6 sm:p-8 text-center shadow-lg">
          <div className="animate-bounce mb-6">
            <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto shadow-lg">
              <FaCheckCircle className="text-white text-4xl" />
            </div>
          </div>

          <h2 className="text-2xl sm:text-3xl font-bold text-green-900 mb-3">
            🎉 Gefeliciteerd, {customerName}!
          </h2>

          <p className="text-green-800 mb-6 text-base sm:text-lg">
            Uw beveiligingsofferte is succesvol aangemaakt en ondertekend.
          </p>

          <div className="bg-white rounded-xl p-4 sm:p-6 inline-block shadow-md border border-green-200">
            <div className="flex items-center justify-center mb-2">
              <FaStar className="text-yellow-500 mr-2" />
              <span className="text-sm font-medium text-gray-600">Offertenummer</span>
            </div>
            <div className="text-2xl sm:text-3xl font-bold text-gray-900 tracking-wider">
              {quoteNumber}
            </div>
            <p className="text-xs text-gray-500 mt-2">Bewaar dit nummer voor uw administratie</p>
          </div>
        </div>

        {/* Next steps timeline */}
        <InfoCard
          title="Wat gebeurt er nu?"
          icon={<FaClock />}
          variant="info"
        >
          <div className="space-y-4">
            {[
              { step: 1, title: 'Verwerking', description: 'Uw offerte wordt verwerkt door ons team', time: 'Direct' },
              { step: 2, title: 'Bevestiging', description: 'U ontvangt binnen 24 uur een bevestiging per email', time: '< 24 uur' },
              { step: 3, title: 'Contact', description: 'Onze technicus neemt contact op voor de installatie afspraak', time: '1-2 dagen' },
              { step: 4, title: 'Installatie', description: 'Professionele installatie op de afgesproken datum', time: '2-3 weken' }
            ].map((item, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 mr-4">
                  <div className="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold shadow-md">
                    {item.step}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-semibold text-sm">{item.title}</h4>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">{item.time}</span>
                  </div>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </InfoCard>

        {/* Contact information */}
        <InfoCard
          title="Contact & Support"
          icon={<FaPhone />}
          variant="success"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center">
                <FaPhone className="text-green-600 mr-3 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Telefoon</p>
                  <a href="tel:020-1234567" className="text-green-600 hover:text-green-800 transition-colors">
                    020-1234567
                  </a>
                </div>
              </div>
              <div className="flex items-center">
                <FaEnvelope className="text-green-600 mr-3 flex-shrink-0" />
                <div>
                  <p className="font-medium text-sm">Email</p>
                  <a href="mailto:<EMAIL>" className="text-green-600 hover:text-green-800 transition-colors">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
            <div>
              <div className="flex items-start">
                <FaClock className="text-green-600 mr-3 flex-shrink-0 mt-1" />
                <div>
                  <p className="font-medium text-sm">Openingstijden</p>
                  <p className="text-sm text-gray-600">Ma-Vr 08:00-17:00</p>
                  <p className="text-sm text-gray-600">Za-Zo Gesloten</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-4 p-3 bg-green-50 rounded-lg">
            <p className="text-sm text-green-800">
              💡 <strong>Tip:</strong> Heeft u vragen over uw offerte? Vermeld altijd uw offertenummer <strong>{quoteNumber}</strong> bij contact.
            </p>
          </div>
        </InfoCard>

        {/* Action buttons */}
        <div className="space-y-4">
          {/* Primary action - Download PDF */}
          <EnhancedButton
            onClick={handleDownloadPDF}
            variant="primary"
            size="lg"
            icon={<FaFileDownload />}
            fullWidth={true}
            className="shadow-lg"
          >
            📄 Download Offerte PDF
          </EnhancedButton>

          {/* Secondary actions */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <EnhancedButton
              onClick={() => navigate('/simple-quotes')}
              variant="success"
              size="md"
              icon={<FaPlus />}
              fullWidth={true}
            >
              Nieuwe Offerte
            </EnhancedButton>

            <EnhancedButton
              onClick={() => navigate('/dashboard')}
              variant="secondary"
              size="md"
              icon={<FaHome />}
              fullWidth={true}
            >
              Naar Dashboard
            </EnhancedButton>
          </div>
        </div>

        {/* Important notes */}
        <InfoCard
          title="Belangrijke informatie"
          icon={<FaClock />}
          variant="warning"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <ul className="space-y-2 text-sm">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Bewaar uw offertenummer voor eventuele vragen</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>De offerte is 30 dagen geldig</span>
              </li>
            </ul>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Wijzigingen zijn mogelijk tot de installatie datum</span>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <span>Bij annulering binnen 14 dagen geen kosten</span>
              </li>
            </ul>
          </div>
        </InfoCard>

        {/* Social proof / testimonial */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 text-center shadow-sm">
          <div className="flex justify-center mb-3">
            {[...Array(5)].map((_, i) => (
              <FaStar key={i} className="text-yellow-400 text-xl" />
            ))}
          </div>
          <blockquote className="text-gray-700 italic mb-4 text-sm sm:text-base">
            "Uitstekende service en professionele installatie. Het team was vriendelijk, punctueel en heeft alles perfect geïnstalleerd. Zeer tevreden met ons nieuwe beveiligingssysteem!"
          </blockquote>
          <footer className="text-sm text-gray-600">
            <strong>Familie van der Berg</strong> • Amsterdam • ⭐⭐⭐⭐⭐
          </footer>
          <div className="mt-4 text-xs text-blue-600">
            Meer dan 2.500+ tevreden klanten sinds 2018
          </div>
        </div>
      </div>
    </MobileContainer>
  );
};

export default SimpleQuoteSuccessPage;
