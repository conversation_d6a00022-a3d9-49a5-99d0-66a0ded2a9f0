import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { getCustomerById, updateCustomer } from '../services/customerService';
import { Customer } from '../types/customer';
import { useAuth } from '../context/AuthContext';
import { useConfirmation } from '../context/ConfirmationContext';
import LoadingSpinner from '../components/LoadingSpinner';
import Breadcrumbs from '../components/Breadcrumbs';
import CustomerNotes from '../components/CustomerNotes';
import { 
  FaUser, 
  FaFileAlt, 
  FaEdit, 
  FaCalendarAlt, 
  FaHistory, 
  FaChartBar,
  FaBuilding,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaIdCard,
  FaClock,
  FaCalendar,
  FaCog,
  FaArrowRight
} from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';

const CustomerPage: React.FC = () => {
  const { customerId } = useParams<{ customerId: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { showConfirmation } = useConfirmation();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      if (!customerId) return;

      try {
        setLoading(true);
        const data = await getCustomerById(parseInt(customerId));
        setCustomer(data);
        setError(null);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || 'Failed to fetch customer data');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [customerId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner message="Loading customer data..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
        <button
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          onClick={() => navigate('/customers')}
        >
          Back to Customers
        </button>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">Customer not found</p>
            </div>
          </div>
        </div>
        <button
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          onClick={() => navigate('/customers')}
        >
          Back to Customers
        </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumbs */}
        <Breadcrumbs
          customerName={customer.name}
          customerId={customer.id}
        />

        {/* Professional Header Section */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-16 w-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <FaBuilding className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="ml-6">
                    <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                      {customer.name}
                    </h1>
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <FaIdCard className="mr-2" />
                      <span className="font-medium">ID: {customer.id}</span>
                      {customer.kvk_number && (
                        <>
                          <span className="mx-2">•</span>
                          <span>KvK: {customer.kvk_number}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-6 lg:mt-0 lg:ml-6 flex flex-col sm:flex-row gap-3">
                <Link
                  to={`/customers/${customer.id}/edit`}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <FaEdit className="mr-2 h-4 w-4" />
                  Edit Customer
                </Link>
                <Link
                  to={`/customers/${customer.id}/documents`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <FaFileAlt className="mr-2 h-4 w-4" />
                  View Documents
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Customer Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contact Information Card */}
            <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <FaUser className="mr-2 h-5 w-5 text-blue-600" />
                  Contact Information
                </h2>
              </div>
              <div className="px-6 py-4">
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaUser className="mr-2 h-4 w-4" />
                      Contact Person
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900 font-medium">
                      {customer.contact_person || 'Not specified'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaEnvelope className="mr-2 h-4 w-4" />
                      Email Address
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {customer.email ? (
                        <a href={`mailto:${customer.email}`} className="text-blue-600 hover:text-blue-800">
                          {customer.email}
                        </a>
                      ) : (
                        'Not specified'
                      )}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaPhone className="mr-2 h-4 w-4" />
                      Phone Number
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {customer.phone ? (
                        <a href={`tel:${customer.phone}`} className="text-blue-600 hover:text-blue-800">
                          {customer.phone}
                        </a>
                      ) : (
                        'Not specified'
                      )}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaMapMarkerAlt className="mr-2 h-4 w-4" />
                      Address
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {customer.address ? (
                        <span>
                          {customer.address}
                          {customer.city && `, ${customer.city}`}
                        </span>
                      ) : (
                        'Not specified'
                      )}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Customer Details Card */}
            <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <FaChartBar className="mr-2 h-5 w-5 text-blue-600" />
                  Customer Details
                </h2>
              </div>
              <div className="px-6 py-4">
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Customer Code</dt>
                    <dd className="mt-1 text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">
                      {customer.code || 'N/A'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaClock className="mr-2 h-4 w-4" />
                      Created
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {customer.created_at ? new Date(customer.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) : 'N/A'}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500 flex items-center">
                      <FaCalendar className="mr-2 h-4 w-4" />
                      Last Updated
                    </dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {customer.updated_at ? new Date(customer.updated_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) : 'N/A'}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Notes Section */}
            {customer && (
              <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
                <CustomerNotes customerId={customer.id} />
              </div>
            )}
          </div>

          {/* Right Column - Quick Actions */}
          <div className="space-y-6">
            {/* Quick Actions Card */}
            <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <FaCog className="mr-2 h-5 w-5 text-blue-600" />
                  Quick Actions
                </h2>
              </div>
              <div className="p-6 space-y-4">
                <Link
                  to={`/customers/${customer.id}/documents`}
                  className="group flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
                >
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
                      <FaFileAlt className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-sm font-medium text-gray-900 group-hover:text-blue-900">
                      Documents
                    </h3>
                    <p className="text-sm text-gray-500 group-hover:text-blue-700">
                      Manage customer documents
                    </p>
                  </div>
                  <FaArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-600 transition-colors duration-200" />
                </Link>

                <Link
                  to={`/calendar?customerId=${customer.id}`}
                  className="group flex items-center p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-all duration-200"
                >
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-200">
                      <FaCalendarAlt className="h-5 w-5 text-green-600" />
                    </div>
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-sm font-medium text-gray-900 group-hover:text-green-900">
                      Events & Calendar
                    </h3>
                    <p className="text-sm text-gray-500 group-hover:text-green-700">
                      Schedule and view events
                    </p>
                  </div>
                  <FaArrowRight className="h-4 w-4 text-gray-400 group-hover:text-green-600 transition-colors duration-200" />
                </Link>

                <Link
                  to={`/customers/${customer.id}/history`}
                  className="group flex items-center p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-all duration-200"
                >
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-200">
                      <FaHistory className="h-5 w-5 text-purple-600" />
                    </div>
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-sm font-medium text-gray-900 group-hover:text-purple-900">
                      Activity History
                    </h3>
                    <p className="text-sm text-gray-500 group-hover:text-purple-700">
                      View customer activity
                    </p>
                  </div>
                  <FaArrowRight className="h-4 w-4 text-gray-400 group-hover:text-purple-600 transition-colors duration-200" />
                </Link>
              </div>
            </div>

            {/* Customer Status Card */}
            <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">Customer Status</h2>
              </div>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-3 w-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">Active Customer</p>
                    <p className="text-sm text-gray-500">All systems operational</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerPage;
