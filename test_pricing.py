#!/usr/bin/env python3
"""
AMSPM Lease Systeem - Pricing Test
Test de correcte berekeningslogica
"""

# Simuleer de pricing configuratie - NIEUWE FORMULES
PRICING_CONFIG = {
    'base_package_cost_excl_vat': 554.48,
    'base_package_cost_incl_vat': 670.92,  # 554.48 * 1.21
    'base_package_with_margin': 749.99,    # 670.92 * 1.1155
    'base_installation_cost_incl_vat': 999.99,  # Basis systeem wordt voor €999,99 verkocht
    'base_monthly_total': 49.99,
    'base_monthly_equipment': 32.99,
    'monthly_monitoring': 8.50,
    'monthly_maintenance': 8.50,
    'labor_per_extra_item': 10.0,
    'margin_percentage': 11.55,
    'vat_percentage': 21.0,
    'videodoorbell_price_incl_vat': 249.99,
    'videodoorbell_price_excl_vat': 206.61
}

def calculate_monthly_increase(extra_cost_incl_vat):
    """Bereken maandelijkse stijging - NIEUWE FORMULE."""
    base_sale_price = PRICING_CONFIG['base_package_with_margin']  # €749,99
    base_monthly = PRICING_CONFIG['base_monthly_equipment']       # €32,99

    if base_sale_price > 0:
        # Extra producten kosten (incl BTW + marge, ZONDER arbeid)
        extra_with_margin = extra_cost_incl_vat * (1 + PRICING_CONFIG['margin_percentage'] / 100)

        total_cost = base_sale_price + extra_with_margin
        percentage_increase = total_cost / base_sale_price
        return base_monthly * percentage_increase
    return base_monthly

def calculate_installation_cost(extra_cost_incl_vat, extra_items_count, videodoorbell_free=False, videodoorbell_paid=False):
    """Bereken installatiekosten - INCL BTW."""
    # Start met VASTE basis
    total_cost_incl_vat = PRICING_CONFIG['base_installation_cost_incl_vat']  # €999,99

    # Extra producten (al incl BTW)
    if extra_cost_incl_vat > 0:
        extra_with_margin = extra_cost_incl_vat * (1 + PRICING_CONFIG['margin_percentage'] / 100)
        extra_labor = extra_items_count * PRICING_CONFIG['labor_per_extra_item']
        total_cost_incl_vat += extra_with_margin + extra_labor

    # Videodeurbel
    if videodoorbell_paid and not videodoorbell_free:
        videodoorbell_with_margin = PRICING_CONFIG['videodoorbell_price_incl_vat'] * (1 + PRICING_CONFIG['margin_percentage'] / 100)
        total_cost_incl_vat += videodoorbell_with_margin + PRICING_CONFIG['labor_per_extra_item']
    elif videodoorbell_free:
        total_cost_incl_vat += PRICING_CONFIG['labor_per_extra_item']

    return total_cost_incl_vat

print("=== AMSPM LEASE SYSTEEM PRICING TEST ===")
print()

# Test 1: Basis pakket
print("TEST 1: Basis ALARM pakket")
print(f"Installatiekosten: €{PRICING_CONFIG['base_installation_cost_incl_vat']}")
print(f"Maandelijkse kosten: €{PRICING_CONFIG['base_monthly_total']}")
print()

# Test 2: 1 extra PIRCAM
print("TEST 2: Basis + 1 extra PIRCAM (€73,60)")
extra_cost_incl_vat = 73.60 * 1.21  # €89,06 incl BTW
extra_items = 1
installation_cost = calculate_installation_cost(extra_cost_incl_vat, extra_items)
monthly_cost = calculate_monthly_increase(extra_cost_incl_vat) + 17.00  # +meldkamer+onderhoud

print(f"Extra kosten: €73,60 ex BTW = €{extra_cost_incl_vat:.2f} incl BTW")
print(f"Installatiekosten: €{installation_cost:.2f}")
print(f"Maandelijkse kosten: €{monthly_cost:.2f}")
print()

# Test 3: Videodeurbel gratis
print("TEST 3: Basis + Videodeurbel GRATIS")
installation_cost_free = calculate_installation_cost(0, 0, videodoorbell_free=True)
print(f"Installatiekosten: €{installation_cost_free:.2f} (alleen €10 arbeid extra)")
print()

# Test 4: Videodeurbel betaald
print("TEST 4: Basis + Videodeurbel BETAALD")
installation_cost_paid = calculate_installation_cost(0, 0, videodoorbell_paid=True)
print(f"Installatiekosten: €{installation_cost_paid:.2f} (€249,99 + €10 arbeid)")
print()

# Test 5: Korting
print("TEST 5: Korting berekening")
base_cost = 999.99
for discount in [10, 15, 25]:
    discount_amount = base_cost * (discount / 100)
    final_cost = base_cost - discount_amount
    print(f"{discount}% korting: €{base_cost} - €{discount_amount:.2f} = €{final_cost:.2f}")

print()
print("=== MINIMUM CHECKS ===")
print(f"Minimum installatiekosten: €749,99 (vaste minimum)")
print(f"Minimum maandelijkse kosten: €{PRICING_CONFIG['base_monthly_total']}")
print()
print("=== VERIFICATIE NIEUWE FORMULES ===")
print("✓ Basis systeem €749,99 wordt verkocht voor €999,99")
print("✓ Extra producten: ex BTW × 1.21 × 1.1155 + €10 arbeid")
print("✓ Maandelijkse kosten: GEEN arbeid, alleen product + marge")
