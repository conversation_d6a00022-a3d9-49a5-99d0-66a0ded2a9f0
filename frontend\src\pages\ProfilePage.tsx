import React, { useState } from "react";
import { useAuth } from "../context/AuthContext";
import { updateCurrentUserName, updateCurrentUserPassword } from "../services/userService";
import { FaUser, FaLock, FaSave, FaTimes, FaCheck, FaShieldAlt, FaKey, FaUserEdit } from "react-icons/fa";
import { useConfirmation } from "../context/ConfirmationContext";
import Breadcrumbs from "../components/Breadcrumbs";

const ProfilePage: React.FC = () => {
  const { user, setUser } = useAuth();
  const { showConfirmation } = useConfirmation();

  const [editingName, setEditingName] = useState(false);
  const [name, setName] = useState(user?.name || "");
  const [originalName, setOriginalName] = useState(user?.name || "");

  const [editingPassword, setEditingPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [nameError, setNameError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const startEditingName = () => {
    setEditingName(true);
    setName(user?.name || "");
    setOriginalName(user?.name || "");
    setNameError(null);
  };

  const cancelEditingName = () => {
    setEditingName(false);
    setName(originalName);
    setNameError(null);
  };

  const startEditingPassword = () => {
    setEditingPassword(true);
    setPassword("");
    setConfirmPassword("");
    setPasswordError(null);
  };

  const cancelEditingPassword = () => {
    setEditingPassword(false);
    setPassword("");
    setConfirmPassword("");
    setPasswordError(null);
  };

  const handleSaveName = async () => {
    if (!name.trim()) {
      setNameError("Naam mag niet leeg zijn");
      return;
    }

    try {
      const updatedUser = await updateCurrentUserName(name);
      if (user) {
        setUser({ ...user, name: updatedUser.name });
      }
      setEditingName(false);
      setSuccessMessage("Naam succesvol bijgewerkt");
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      setNameError(error.response?.data?.error || "Fout bij het bijwerken van de naam");
    }
  };

  const handleSavePassword = async () => {
    // Reset errors
    setPasswordError(null);

    // Validate password
    if (password.length < 8) {
      setPasswordError("Wachtwoord moet minimaal 8 tekens lang zijn");
      return;
    }

    if (!/[A-Z]/.test(password)) {
      setPasswordError("Wachtwoord moet minimaal één hoofdletter bevatten");
      return;
    }

    if (!/[a-z]/.test(password)) {
      setPasswordError("Wachtwoord moet minimaal één kleine letter bevatten");
      return;
    }

    if (!/[0-9]/.test(password)) {
      setPasswordError("Wachtwoord moet minimaal één cijfer bevatten");
      return;
    }

    if (password !== confirmPassword) {
      setPasswordError("Wachtwoorden komen niet overeen");
      return;
    }

    try {
      await updateCurrentUserPassword(password);
      setEditingPassword(false);
      setPassword("");
      setConfirmPassword("");
      setSuccessMessage("Wachtwoord succesvol bijgewerkt");
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error: any) {
      setPasswordError(error.response?.data?.error || "Fout bij het bijwerken van het wachtwoord");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Header */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-8">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-16 w-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <FaUser className="h-8 w-8 text-white" />
                </div>
              </div>
              <div className="ml-6">
                <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                  Profile Settings
                </h1>
                <div className="mt-2 flex items-center text-sm text-gray-500">
                  <FaShieldAlt className="mr-2" />
                  <span>Manage your account settings and preferences</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Breadcrumbs */}
        <div className="mb-6">
          <Breadcrumbs
            items={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Profile", href: "/profile" }
            ]}
          />
        </div>

        {/* Success Message */}
        {successMessage && (
          <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaCheck className="h-5 w-5 text-green-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-700">{successMessage}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Profile Information */}
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <FaUserEdit className="mr-2 h-5 w-5 text-blue-600" />
                Profile Information
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {/* Current User Info */}
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-16 w-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                      <FaUser className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {user?.name || "No name set"}
                    </p>
                    <p className="text-sm text-gray-500">{user?.email}</p>
                    <p className="text-xs text-gray-400 capitalize">{user?.role}</p>
                  </div>
                </div>

                {/* Name Editing */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Display Name
                  </label>
                  {editingName ? (
                    <div className="space-y-3">
                      <input
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Enter your name"
                      />
                      {nameError && (
                        <p className="text-sm text-red-600">{nameError}</p>
                      )}
                      <div className="flex space-x-3">
                        <button
                          onClick={handleSaveName}
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <FaSave className="mr-2 h-4 w-4" />
                          Save
                        </button>
                        <button
                          onClick={cancelEditingName}
                          className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <FaTimes className="mr-2 h-4 w-4" />
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-900">
                        {user?.name || "No name set"}
                      </p>
                      <button
                        onClick={startEditingName}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <FaUserEdit className="mr-1 h-3 w-3" />
                        Edit
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Password Management */}
          <div className="bg-white shadow-sm border border-gray-200 rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <FaKey className="mr-2 h-5 w-5 text-blue-600" />
                Password Management
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <p className="text-sm text-gray-600 mb-4">
                    Update your password to keep your account secure. Your new password must meet the following requirements:
                  </p>
                  <ul className="text-sm text-gray-600 space-y-1 mb-4">
                    <li className="flex items-center">
                      <FaCheck className="mr-2 h-3 w-3 text-green-500" />
                      At least 8 characters long
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="mr-2 h-3 w-3 text-green-500" />
                      Contains at least one uppercase letter
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="mr-2 h-3 w-3 text-green-500" />
                      Contains at least one lowercase letter
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="mr-2 h-3 w-3 text-green-500" />
                      Contains at least one number
                    </li>
                  </ul>
                </div>

                {editingPassword ? (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        New Password
                      </label>
                      <input
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Enter new password"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password
                      </label>
                      <input
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="Confirm new password"
                      />
                    </div>
                    {passwordError && (
                      <p className="text-sm text-red-600">{passwordError}</p>
                    )}
                    <div className="flex space-x-3">
                      <button
                        onClick={handleSavePassword}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <FaSave className="mr-2 h-4 w-4" />
                        Update Password
                      </button>
                      <button
                        onClick={cancelEditingPassword}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <FaTimes className="mr-2 h-4 w-4" />
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">Password</p>
                      <p className="text-sm text-gray-500">Last changed: Unknown</p>
                    </div>
                    <button
                      onClick={startEditingPassword}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <FaLock className="mr-1 h-3 w-3" />
                      Change
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Account Security Tips */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <FaShieldAlt className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">Security Tips</h3>
              <div className="mt-2 text-sm text-blue-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>Use a strong, unique password that you don't use elsewhere</li>
                  <li>Consider enabling two-factor authentication if available</li>
                  <li>Keep your email address up to date for account recovery</li>
                  <li>Log out when using shared computers</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
