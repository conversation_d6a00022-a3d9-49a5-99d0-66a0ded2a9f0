import { TimeEntry, TimeEntryFormData, MonthlyTimeEntrySummary, TimeEntriesResponse } from '../types/timeEntry';
import { MileageEntry, MileageEntryFormData, MileageEntriesResponse } from '../types/mileageEntry';
import api from '../api';

// Time Entry Services
export const getAllTimeEntries = async (page = 1, perPage = 20) => {
  const response = await api.get(`/time-tracking/time-entries?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const checkTimeEntryOverlap = async (userId: number, date: string, startTime: string, endTime: string, excludeEntryId?: number) => {
  let url = `/time-tracking/time-entries/check-overlap?user_id=${userId}&date=${date}&start_time=${startTime}&end_time=${endTime}`;
  if (excludeEntryId) {
    url += `&exclude_entry_id=${excludeEntryId}`;
  }

  try {
    console.log('Checking for overlaps with backend API:', url);
    const response = await api.get(url);
    console.log('Backend overlap check response:', response.data);
    return response.data;
  } catch (error: any) {
    // If the backend returns a 404 or other error, fall back to client-side check
    console.warn('Backend overlap check failed:', error.message);
    console.warn('Falling back to client-side check');

    // Get all entries for this user, including pending ones
    const timeEntries = await getTimeEntriesByUser(userId);
    console.log('User time entries:', timeEntries);

    // Also get pending entries to make sure we check those too
    const pendingResponse = await getPendingTimeEntries();
    console.log('All pending entries:', pendingResponse);
    const pendingEntriesForUser = pendingResponse.entries.filter(entry => entry.user_id === userId);
    console.log('Pending entries for user:', pendingEntriesForUser);

    // Combine regular and pending entries
    const allEntries = [...timeEntries.entries, ...pendingEntriesForUser];
    console.log('All entries combined:', allEntries);

    // Filter for the specific date and exclude the current entry if editing
    const entriesForDate = allEntries.filter(entry =>
      entry.date === date &&
      (excludeEntryId ? entry.id !== excludeEntryId : true)
    );
    console.log('Entries for date:', entriesForDate);

    // Check for overlaps with any entry, regardless of status (approved, rejected, or pending)
    const hasOverlap = entriesForDate.some(entry => {
      const entryStart = entry.start_time;
      const entryEnd = entry.end_time;

      console.log(`Checking overlap: New (${startTime}-${endTime}) vs Existing (${entryStart}-${entryEnd})`);

      // Check if the new time range overlaps with an existing entry
      // Two time ranges overlap if one starts before the other ends and ends after the other starts
      const overlaps = (startTime < entryEnd && endTime > entryStart);
      console.log('Overlaps:', overlaps);

      return overlaps;
    });

    // Get the overlapping entries to show to the user
    const overlappingEntries = hasOverlap
      ? entriesForDate.filter(entry => {
          const entryStart = entry.start_time;
          const entryEnd = entry.end_time;
          return (startTime < entryEnd && endTime > entryStart);
        })
      : [];

    console.log('Overlapping entries:', overlappingEntries);

    return { overlaps: hasOverlap, entries: overlappingEntries };
  }
};

export const getTimeEntriesByUser = async (userId: number, page = 1, perPage = 20) => {
  const response = await api.get(`/time-tracking/time-entries?user_id=${userId}&page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getTimeEntriesByMonthYear = async (month: number, year: number, userId?: number, page = 1, perPage = 20): Promise<TimeEntriesResponse> => {
  let url = `/time-tracking/time-entries?month=${month}&year=${year}&page=${page}&per_page=${perPage}`;
  if (userId) {
    url += `&user_id=${userId}`;
  }
  const response = await api.get(url);
  return response.data;
};

export const getPendingTimeEntries = async (page = 1, perPage = 20): Promise<TimeEntriesResponse> => {
  const response = await api.get(`/time-tracking/time-entries?status=pending&page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getTimeEntryById = async (entryId: number) => {
  const response = await api.get(`/time-tracking/time-entries/${entryId}`);
  return response.data;
};

export const createTimeEntry = async (entryData: TimeEntryFormData) => {
  const response = await api.post('/time-tracking/time-entries', entryData);
  return response.data;
};

export const updateTimeEntry = async (entryId: number, entryData: Partial<TimeEntryFormData>) => {
  const response = await api.put(`/time-tracking/time-entries/${entryId}`, entryData);
  return response.data;
};

export const approveTimeEntry = async (entryId: number) => {
  const response = await api.post(`/time-tracking/time-entries/${entryId}/approve`);
  return response.data;
};

export const rejectTimeEntry = async (entryId: number) => {
  const response = await api.post(`/time-tracking/time-entries/${entryId}/reject`);
  return response.data;
};

export const deleteTimeEntry = async (entryId: number) => {
  const response = await api.delete(`/time-tracking/time-entries/${entryId}`);
  return response.data;
};

// Mileage Entry Services
export const getAllMileageEntries = async (page = 1, perPage = 20) => {
  const response = await api.get(`/time-tracking/mileage-entries?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getMileageEntriesByUser = async (userId: number, page = 1, perPage = 20) => {
  const response = await api.get(`/time-tracking/mileage-entries?user_id=${userId}&page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getMileageEntriesByMonthYear = async (month: number, year: number, userId?: number, page = 1, perPage = 20): Promise<MileageEntriesResponse> => {
  let url = `/time-tracking/mileage-entries?month=${month}&year=${year}&page=${page}&per_page=${perPage}`;
  if (userId) {
    url += `&user_id=${userId}`;
  }
  const response = await api.get(url);
  return response.data;
};

export const getPendingMileageEntries = async (page = 1, perPage = 20): Promise<MileageEntriesResponse> => {
  const response = await api.get(`/time-tracking/mileage-entries?status=pending&page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getMileageEntryById = async (entryId: number) => {
  const response = await api.get(`/time-tracking/mileage-entries/${entryId}`);
  return response.data;
};

export const checkMileageEntryOverlap = async (userId: number, date: string, excludeEntryId?: number) => {
  let url = `/time-tracking/mileage-entries/check-overlap?user_id=${userId}&date=${date}`;
  if (excludeEntryId) {
    url += `&exclude_entry_id=${excludeEntryId}`;
  }
  try {
    const response = await api.get(url);
    return response.data;
  } catch (error) {
    // If the backend doesn't support this endpoint yet, we'll implement a client-side check
    console.warn('Backend overlap check not available, falling back to client-side check');

    // Get all entries for this user, including pending ones
    const mileageEntries = await getMileageEntriesByUser(userId);

    // Also get pending entries to make sure we check those too
    const pendingResponse = await getPendingMileageEntries();
    const pendingEntriesForUser = pendingResponse.entries.filter(entry => entry.user_id === userId);

    // Combine regular and pending entries
    const allEntries = [...mileageEntries.entries, ...pendingEntriesForUser];

    // Filter for the specific date and exclude the current entry if editing
    const entriesForDate = allEntries.filter(entry =>
      entry.date === date &&
      (excludeEntryId ? entry.id !== excludeEntryId : true)
    );

    // For mileage entries, we'll just check if there's already an entry for this date
    return { overlaps: entriesForDate.length > 0, entries: entriesForDate };
  }
};

export const createMileageEntry = async (entryData: MileageEntryFormData) => {
  const response = await api.post('/time-tracking/mileage-entries', entryData);
  return response.data;
};

export const updateMileageEntry = async (entryId: number, entryData: Partial<MileageEntryFormData>) => {
  const response = await api.put(`/time-tracking/mileage-entries/${entryId}`, entryData);
  return response.data;
};

export const approveMileageEntry = async (entryId: number) => {
  const response = await api.post(`/time-tracking/mileage-entries/${entryId}/approve`);
  return response.data;
};

export const rejectMileageEntry = async (entryId: number) => {
  const response = await api.post(`/time-tracking/mileage-entries/${entryId}/reject`);
  return response.data;
};

export const deleteMileageEntry = async (entryId: number) => {
  const response = await api.delete(`/time-tracking/mileage-entries/${entryId}`);
  return response.data;
};

// Combined Services
export const getMonthlySummary = async (userId: number, year: number) => {
  const response = await api.get(`/time-tracking/monthly-summary/${userId}/${year}`);
  return response.data;
};