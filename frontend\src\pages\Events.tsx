import React, { useState, useEffect } from "react";
import { getAllEvents, createEvent, updateEvent, deleteEvent, getPendingEvents, getCompletedEvents } from "../services/eventService";
import { getAllUsers } from "../services/userService";
import { getAllCustomers } from "../services/customerService";
import { Event } from "../types/event";
import { User } from "../types/user";
import { Customer } from "../types/customer";
import LoadingSpinner from '../components/LoadingSpinner';
import EventModal from '../components/EventModal';
import Pagination from '../components/Pagination';
import { useConfirmation } from '../context/ConfirmationContext';
import { 
  FaCalendarAlt, 
  FaPlus, 
  FaClock, 
  FaCheckCircle, 
  FaEdit, 
  FaTrash, 
  FaUser, 
  FaBuilding, 
  FaMapMarkerAlt,
  FaCalendarDay,
  FaC<PERSON><PERSON><PERSON>ist,
  FaExclamationTriangle,
  FaArrowRight
} from "react-icons/fa";
import { formatDateTimeForBackend } from '../utils/dateUtils';

interface NewEvent {
  customer_id: number | null;
  customer_name?: string | null;
  customer_address?: string | null;
  event_type: string | null;
  description: string;
  scheduled_date: string;
  user_ids: number[];
  user_id: number | null; // Keep for backward compatibility
}

// Utility function to truncate text
const truncateText = (text: string, maxLength: number) => {
  if (!text) return text;
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

const Events: React.FC = () => {
  const { showConfirmation } = useConfirmation();
  const [pendingEvents, setPendingEvents] = useState<Event[]>([]);
  const [completedEvents, setCompletedEvents] = useState<Event[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [newEvent, setNewEvent] = useState<NewEvent>({
    customer_id: null,
    customer_name: null,
    customer_address: null,
    event_type: null,
    description: "",
    scheduled_date: "",
    user_ids: [] as number[],
    user_id: null // Keep for backward compatibility
  });
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [pendingLoading, setPendingLoading] = useState(true);
  const [completedLoading, setCompletedLoading] = useState(true);

  // Separate pagination for pending events
  const [pendingCurrentPage, setPendingCurrentPage] = useState(1);
  const [pendingItemsPerPage, setPendingItemsPerPage] = useState(10);
  const [pendingTotalItems, setPendingTotalItems] = useState(0);

  // Separate pagination for completed events
  const [completedCurrentPage, setCompletedCurrentPage] = useState(1);
  const [completedItemsPerPage, setCompletedItemsPerPage] = useState(10);
  const [completedTotalItems, setCompletedTotalItems] = useState(0);

  const fetchPendingEvents = async (page: number, perPage: number, cacheBust?: boolean) => {
    try {
      setPendingLoading(true);
      const response = await getPendingEvents(page, perPage, cacheBust);
      setPendingEvents(response.events);
      setPendingTotalItems(response.total);
      setPendingLoading(false);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch pending events");
      setPendingLoading(false);
    }
  };

  const fetchCompletedEvents = async (page: number, perPage: number) => {
    try {
      setCompletedLoading(true);
      const response = await getCompletedEvents(page, perPage);
      setCompletedEvents(response.events);
      setCompletedTotalItems(response.total);
      setCompletedLoading(false);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch completed events");
      setCompletedLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await getAllUsers();
      setUsers(response.users);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch users");
    }
  };

  const fetchCustomers = async () => {
    try {
      const response = await getAllCustomers();
      setCustomers(response.customers);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch customers");
    }
  };

  useEffect(() => {
    fetchPendingEvents(pendingCurrentPage, pendingItemsPerPage, false); // Don't cache bust for normal page changes
    fetchUsers();
    fetchCustomers();
  }, [pendingCurrentPage, pendingItemsPerPage]);

  // Refresh data when user returns to the tab to ensure fresh data
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchPendingEvents(pendingCurrentPage, pendingItemsPerPage, true);
        fetchCompletedEvents(completedCurrentPage, completedItemsPerPage);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [pendingCurrentPage, pendingItemsPerPage, completedCurrentPage, completedItemsPerPage]);

  useEffect(() => {
    fetchCompletedEvents(completedCurrentPage, completedItemsPerPage);
  }, [completedCurrentPage, completedItemsPerPage]);

  // Debug logging for pending events changes
  useEffect(() => {
    console.log('Pending events updated:', pendingEvents.length, 'events');
  }, [pendingEvents]);

  const handlePendingPageChange = (page: number) => {
    setPendingCurrentPage(page);
  };

  const handlePendingItemsPerPageChange = (perPage: number) => {
    setPendingItemsPerPage(perPage);
    setPendingCurrentPage(1);
  };

  const handleCompletedPageChange = (page: number) => {
    setCompletedCurrentPage(page);
  };

  const handleCompletedItemsPerPageChange = (perPage: number) => {
    setCompletedItemsPerPage(perPage);
    setCompletedCurrentPage(1);
  };

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    try {
      const formattedDate = formatDateTimeForBackend(newEvent.scheduled_date);
      const event = await createEvent(
        newEvent.customer_id || null,  // Ensure null if falsy
        newEvent.event_type || '',  // Convert null to empty string for API
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : null  // Use user_ids array
      );
      
      // Reset to first page and refresh the pending events list to ensure the new event appears
      setPendingCurrentPage(1);
      await fetchPendingEvents(1, pendingItemsPerPage, true); // Use cacheBust=true
      
      setShowModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to create event");
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingEvent) return;
    setSubmitting(true);
    try {
      const formattedDate = formatDateTimeForBackend(newEvent.scheduled_date);
      const event = await updateEvent(
        editingEvent.id,
        newEvent.customer_id !== null ? newEvent.customer_id : 0,
        newEvent.event_type || '',  // Convert null to empty string for API
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : undefined
      );

      // Update the event in the appropriate array based on its current status
      if (editingEvent.status === 'pending') {
        setPendingEvents(pendingEvents.map((e: Event) => (e.id === editingEvent.id ? event : e)));
      } else {
        setCompletedEvents(completedEvents.map((e: Event) => (e.id === editingEvent.id ? event : e)));
      }

      // Refresh the appropriate list to ensure data consistency
      if (editingEvent.status === 'pending') {
        await fetchPendingEvents(pendingCurrentPage, pendingItemsPerPage, true);
      } else {
        await fetchCompletedEvents(completedCurrentPage, completedItemsPerPage);
      }

      setEditingEvent(null);
      setNewEvent({
        customer_id: null,
        customer_name: null,
        customer_address: null,
        event_type: null,
        description: "",
        scheduled_date: "",
        user_ids: [],
        user_id: null
      });
      setShowModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to update event");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (eventId: number) => {
    // Find the event in either pending or completed arrays
    const pendingEvent = pendingEvents.find(e => e.id === eventId);
    const completedEvent = completedEvents.find(e => e.id === eventId);
    const event = pendingEvent || completedEvent;

    if (!event) return;

    showConfirmation({
      title: "Delete Event",
      message: `Are you sure you want to delete this ${event.event_type || 'Algemeen'} event for ${event.customer_name}? This action cannot be undone.`,
      confirmText: "Delete",
      confirmButtonClass: "bg-red-600 hover:bg-red-700",
      onConfirm: async () => {
        setSubmitting(true);
        try {
          await deleteEvent(eventId);

          // Remove from the appropriate array and update totals
          if (pendingEvent) {
            setPendingEvents(pendingEvents.filter((e: Event) => e.id !== eventId));
            setPendingTotalItems(prev => prev - 1);
            // Refresh pending events to ensure data consistency
            await fetchPendingEvents(pendingCurrentPage, pendingItemsPerPage, true);
          } else if (completedEvent) {
            setCompletedEvents(completedEvents.filter((e: Event) => e.id !== eventId));
            setCompletedTotalItems(prev => prev - 1);
            // Refresh completed events to ensure data consistency
            await fetchCompletedEvents(completedCurrentPage, completedItemsPerPage);
          }

          setError(null);
        } catch (err: any) {
          setError(err.response?.data?.error || "Failed to delete event");
        } finally {
          setSubmitting(false);
        }
      }
    });
  };

  // Separate component for rendering pending events (without "Completed By" column)
  const renderPendingEventTable = (eventList: Event[], title: string, icon: React.ReactNode, color: string) => (
    <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          {icon}
          <span className="ml-2">{title}</span>
          <span className="ml-auto bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
            {eventList.length} events
          </span>
        </h2>
      </div>
      <div className="overflow-hidden">
        {/* Desktop view - Table for Pending Events */}
        <div className="hidden lg:block">
          <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '30%'}}>
                  Customer
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '25%'}}>
                  Type
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '15%'}}>
                  Date
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '15%'}}>
                  Status
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '15%'}}>
                  Actions
                </th>
              </tr>
            </thead>
              <tbody className="bg-white divide-y divide-gray-200">
              {eventList.length > 0 ? (
                eventList.map((event) => (
                  <tr key={event.id} className="hover:bg-gray-50 transition-colors duration-150">
                    <td className="px-2 py-3">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-6 w-6">
                          <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                            <FaBuilding className="h-3 w-3 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-2 min-w-0 flex-1">
                          <div className="text-xs font-medium text-gray-900 truncate">{truncateText(event.customer_name || 'No customer', 20)}</div>
                          {event.customer_address && (
                            <div className="text-xs text-gray-500 truncate">{truncateText(event.customer_address, 25)}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      <div className="min-w-0">
                        <div className="text-xs font-medium text-gray-900 truncate">{truncateText(event.event_type || 'Algemeen', 15)}</div>
                        {event.description && (
                          <div className="text-xs text-gray-500 truncate">{truncateText(event.description, 30)}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      <div className="text-xs text-gray-900 truncate">
                        {new Date(event.scheduled_date).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      <span className="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        Pending
                      </span>
                    </td>
                    <td className="px-2 py-3 text-sm font-medium">
                      <div className="flex space-x-1">
                        <button
                          onClick={() => {
                            setEditingEvent(event);
                            setNewEvent({
                              customer_id: event.customer_id ?? null,
                              customer_name: event.customer_name,
                              customer_address: event.customer_address,
                              event_type: event.event_type,
                              description: event.description,
                              scheduled_date: event.scheduled_date.slice(0, 16),
                              user_ids: event.user_ids || [],
                              user_id: event.user_id ?? null,
                            });
                            setShowModal(true);
                          }}
                          className="inline-flex items-center px-1 py-0.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200"
                        >
                          <FaEdit className="h-3 w-3" />
                        </button>
                        <button
                          onClick={() => handleDelete(event.id)}
                          className="inline-flex items-center px-1 py-0.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200"
                        >
                          <FaTrash className="h-3 w-3" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <div className="text-gray-400 mb-4">
                      {icon}
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No {title.toLowerCase()} found</h3>
                    <p className="text-gray-500">There are no {title.toLowerCase()} at the moment.</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Mobile view - Cards */}
        <div className="lg:hidden">
          <div className="p-6">
            <div className="space-y-4">
              {eventList.length > 0 ? (
                eventList.map((event) => (
                  <div key={event.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 text-lg">{truncateText(event.event_type || 'Algemeen', 20)}</h3>
                        <p className="text-sm text-gray-600">{truncateText(event.description, 50)}</p>
                      </div>
                      <span className="ml-4 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        Pending
                      </span>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <FaBuilding className="mr-2 text-gray-400" />
                        <span className="font-medium">Customer:</span>
                        <span className="ml-2">{truncateText(event.customer_name || 'No customer', 25)}</span>
                      </div>
                      
                      {event.customer_address && (
                        <div className="flex items-center text-sm text-gray-600">
                          <FaMapMarkerAlt className="mr-2 text-gray-400" />
                          <span className="font-medium">Address:</span>
                          <span className="ml-2">{truncateText(event.customer_address, 35)}</span>
                        </div>
                      )}

                      <div className="flex items-center text-sm text-gray-600">
                        <FaCalendarDay className="mr-2 text-gray-400" />
                        <span className="font-medium">Scheduled:</span>
                        <span className="ml-2">
                          {new Date(event.scheduled_date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                    </div>

                    <div className="flex space-x-2 pt-3 border-t border-gray-200">
                      <button
                        onClick={() => {
                          setEditingEvent(event);
                          setNewEvent({
                            customer_id: event.customer_id ?? null,
                            customer_name: event.customer_name,
                            customer_address: event.customer_address,
                            event_type: event.event_type,
                            description: event.description,
                            scheduled_date: event.scheduled_date.slice(0, 16),
                            user_ids: event.user_ids || [],
                            user_id: event.user_id ?? null,
                          });
                          setShowModal(true);
                        }}
                        className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors duration-200"
                      >
                        <FaEdit className="mr-2 h-4 w-4" />
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(event.id)}
                        className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200"
                      >
                        <FaTrash className="mr-2 h-4 w-4" />
                        Delete
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-400 mb-4">
                    {icon}
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No {title.toLowerCase()} found</h3>
                  <p className="text-gray-500">There are no {title.toLowerCase()} at the moment.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Separate component for rendering completed events (with "Completed By" column)
  const renderCompletedEventTable = (eventList: Event[], title: string, icon: React.ReactNode, color: string) => (
    <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          {icon}
          <span className="ml-2">{title}</span>
          <span className="ml-auto bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-sm font-medium">
            {eventList.length} events
          </span>
        </h2>
      </div>
      <div className="overflow-hidden">
        {/* Desktop view - Table for Completed Events */}
        <div className="hidden lg:block">
          <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '25%'}}>
                  Customer
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '20%'}}>
                  Type
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '15%'}}>
                  Date
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '10%'}}>
                  Status
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '20%'}}>
                  Completed By
                </th>
                <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style={{width: '10%'}}>
                  Actions
                </th>
              </tr>
            </thead>
              <tbody className="bg-white divide-y divide-gray-200">
              {eventList.length > 0 ? (
                eventList.map((event) => (
                  <tr key={event.id} className="hover:bg-gray-50 transition-colors duration-150">
                    <td className="px-2 py-3">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-6 w-6">
                          <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                            <FaBuilding className="h-3 w-3 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-2 min-w-0 flex-1">
                          <div className="text-xs font-medium text-gray-900 truncate">{truncateText(event.customer_name || 'No customer', 20)}</div>
                          {event.customer_address && (
                            <div className="text-xs text-gray-500 truncate">{truncateText(event.customer_address, 25)}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      <div className="min-w-0">
                        <div className="text-xs font-medium text-gray-900 truncate">{truncateText(event.event_type || 'Algemeen', 15)}</div>
                        {event.description && (
                          <div className="text-xs text-gray-500 truncate">{truncateText(event.description, 30)}</div>
                        )}
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      <div className="text-xs text-gray-900 truncate">
                        {new Date(event.scheduled_date).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      <span className="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Done
                      </span>
                    </td>
                    <td className="px-2 py-3 text-xs text-gray-900">
                      {event.completed_by_name ? (
                        <div className="truncate">{event.completed_by_name}</div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-2 py-3 text-sm font-medium">
                      <button
                        onClick={() => handleDelete(event.id)}
                        className="inline-flex items-center px-1 py-0.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200"
                      >
                        <FaTrash className="h-3 w-3" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <div className="text-gray-400 mb-4">
                      {icon}
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No {title.toLowerCase()} found</h3>
                    <p className="text-gray-500">There are no {title.toLowerCase()} at the moment.</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Mobile view - Cards */}
        <div className="lg:hidden">
          <div className="p-6">
            <div className="space-y-4">
              {eventList.length > 0 ? (
                eventList.map((event) => (
                  <div key={event.id} className="bg-gray-50 border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 text-lg">{truncateText(event.event_type || 'Algemeen', 20)}</h3>
                        <p className="text-sm text-gray-600">{truncateText(event.description, 50)}</p>
                      </div>
                      <span className="ml-4 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Completed
                      </span>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <FaBuilding className="mr-2 text-gray-400" />
                        <span className="font-medium">Customer:</span>
                        <span className="ml-2">{truncateText(event.customer_name || 'No customer', 25)}</span>
                      </div>
                      
                      {event.customer_address && (
                        <div className="flex items-center text-sm text-gray-600">
                          <FaMapMarkerAlt className="mr-2 text-gray-400" />
                          <span className="font-medium">Address:</span>
                          <span className="ml-2">{truncateText(event.customer_address, 35)}</span>
                        </div>
                      )}

                      <div className="flex items-center text-sm text-gray-600">
                        <FaCalendarDay className="mr-2 text-gray-400" />
                        <span className="font-medium">Scheduled:</span>
                        <span className="ml-2">
                          {new Date(event.scheduled_date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>

                      {event.completed_at && (
                        <div className="flex items-center text-sm text-gray-600">
                          <FaCheckCircle className="mr-2 text-gray-400" />
                          <span className="font-medium">Completed:</span>
                          <span className="ml-2">
                            {new Date(event.completed_at).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
                      )}

                      {event.completed_by_name && (
                        <div className="flex items-center text-sm text-gray-600">
                          <FaUser className="mr-2 text-gray-400" />
                          <span className="font-medium">Completed by:</span>
                          <span className="ml-2">{event.completed_by_name}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex space-x-2 pt-3 border-t border-gray-200">
                      <button
                        onClick={() => handleDelete(event.id)}
                        className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors duration-200"
                      >
                        <FaTrash className="mr-2 h-4 w-4" />
                        Delete
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <div className="text-gray-400 mb-4">
                    {icon}
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No {title.toLowerCase()} found</h3>
                  <p className="text-gray-500">There are no {title.toLowerCase()} at the moment.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (pendingLoading && completedLoading) {
    return <LoadingSpinner message="Loading events..." />;
  }

  const handleModalClose = () => {
    setShowModal(false);
    setEditingEvent(null);
    setNewEvent({
      customer_id: null,
      customer_name: null,
      customer_address: null,
      event_type: null,
      description: "",
      scheduled_date: "",
      user_ids: [],
      user_id: null
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Professional Header */}
        <div className="bg-white shadow-sm border border-gray-200 rounded-lg mb-8">
          <div className="px-6 py-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-16 w-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <FaCalendarAlt className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <div className="ml-6">
                    <h1 className="text-3xl font-bold text-gray-900 leading-tight">
                      Events Management
                    </h1>
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <FaClipboardList className="mr-2" />
                      <span className="font-medium">Manage and track all system events</span>
                      <span className="mx-2">•</span>
                      <span>Create, edit, and monitor events</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="mt-6 lg:mt-0 lg:ml-6">
                <button
                  onClick={() => {
                    setNewEvent({
                      customer_id: null,
                      customer_name: null,
                      customer_address: null,
                      event_type: null,
                      description: "",
                      scheduled_date: "",
                      user_ids: [],
                      user_id: null
                    });
                    setEditingEvent(null);
                    setShowModal(true);
                  }}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200"
                >
                  <FaPlus className="mr-2 h-4 w-4" />
                  Create Event
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Pending Events Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaClock className="text-orange-500 mr-2 h-5 w-5" />
              <h2 className="text-lg font-semibold text-gray-900">Pending Events</h2>
            </div>
            <button
              onClick={async () => {
                await fetchPendingEvents(pendingCurrentPage, pendingItemsPerPage, true);
              }}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
              title="Refresh pending events"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>

          {pendingLoading ? (
            <LoadingSpinner message="Loading pending events..." />
          ) : (
            <>
              {renderPendingEventTable(pendingEvents, "Pending Events", <FaClock className="h-5 w-5 text-orange-500" />, "orange")}
              <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-4">
                <Pagination
                  currentPage={pendingCurrentPage}
                  totalPages={Math.ceil(pendingTotalItems / pendingItemsPerPage)}
                  onPageChange={handlePendingPageChange}
                  totalItems={pendingTotalItems}
                  itemsPerPage={pendingItemsPerPage}
                  onItemsPerPageChange={handlePendingItemsPerPageChange}
                />
              </div>
            </>
          )}
        </div>

        {/* Completed Events Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaCheckCircle className="text-green-500 mr-2 h-5 w-5" />
              <h2 className="text-lg font-semibold text-gray-900">Completed Events</h2>
            </div>
            <button
              onClick={async () => {
                await fetchCompletedEvents(completedCurrentPage, completedItemsPerPage);
              }}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
              title="Refresh completed events"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>

          {completedLoading ? (
            <LoadingSpinner message="Loading completed events..." />
          ) : (
            <>
              {renderCompletedEventTable(completedEvents, "Completed Events", <FaCheckCircle className="h-5 w-5 text-green-500" />, "green")}
              <div className="bg-white shadow-sm border border-gray-200 rounded-lg p-4">
                <Pagination
                  currentPage={completedCurrentPage}
                  totalPages={Math.ceil(completedTotalItems / completedItemsPerPage)}
                  onPageChange={handleCompletedPageChange}
                  totalItems={completedTotalItems}
                  itemsPerPage={completedItemsPerPage}
                  onItemsPerPageChange={handleCompletedItemsPerPageChange}
                />
              </div>
            </>
          )}
        </div>

        {/* Event Modal */}
        {showModal && (
          <EventModal
            event={newEvent}
            onClose={handleModalClose}
            onSubmit={editingEvent ? handleUpdate : handleCreate}
            setEvent={setNewEvent}
            isEditing={!!editingEvent}
            submitting={submitting}
            customers={customers}
            users={users}
          />
        )}
      </div>
    </div>
  );
};

export default Events;