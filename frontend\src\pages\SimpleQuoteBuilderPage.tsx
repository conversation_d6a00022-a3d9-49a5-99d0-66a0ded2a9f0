import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaArrowLeft, FaPlus, FaCalculator, FaEye, FaCheck, FaVideo } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import {
  MobileContainer,
  MobilePageHeader,
  ProgressSteps,
  CustomerSummary,
  InfoCard,
  ProductItemCard,
  PriceSummary,
  EnhancedButton,
  MobileSelect
} from '../components/common/MobileUtils';
import LoadingSpinner from '../components/LoadingSpinner';
import simpleQuoteService, { Product, QuoteCalculation } from '../services/simpleQuoteService';

interface CustomerData {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_address: string;
  customer_city: string;
  customer_postal_code: string;
}

const SimpleQuoteBuilderPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get customer data and category from previous page
  const { customerData, category } = location.state || {};

  // Redirect if no data
  if (!customerData || !category) {
    navigate('/simple-quotes');
    return null;
  }

  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Record<string, Product>>({});
  const [calculations, setCalculations] = useState<QuoteCalculation | null>(null);
  
  // Product quantities - dynamic state to handle any product keys
  const [extraProducts, setExtraProducts] = useState<Record<string, number>>({});

  // Quote settings
  const [paymentTerms, setPaymentTerms] = useState('1_term');
  const [discountPercentage, setDiscountPercentage] = useState(0);
  const [videodoorbellFree, setVideodoorbellFree] = useState(false);
  const [videodoorbellPaid, setVideodoorbellPaid] = useState(false);

  // Discount limits based on payment terms
  const getMaxDiscountForTerms = (terms: string) => {
    switch (terms) {
      case '1_term': return 25;
      case '2_terms': return 15;
      case '3_terms': return 10;
      default: return 0;
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [category]);

  useEffect(() => {
    // Always calculate quote when dependencies change
    calculateQuote();
  }, [extraProducts, paymentTerms, discountPercentage, videodoorbellFree, videodoorbellPaid]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await simpleQuoteService.getProducts(category);
      console.log('Products data:', data); // Debug log

      // Voor alarm en alarm_cameras: toon alle extra producten
      let extraProducts = {};
      if (data.products) {
        if (data.products.extra_products) {
          // Gebruik de extra_products direct
          extraProducts = data.products.extra_products;
        } else {
          // Fallback: zoek naar de gewenste producten
          const requiredProducts = ['magneetcontact', 'shock_sensor', 'pir_normaal', 'rookmelder', 'pircam', 'sirene', 'bediendeel', 'videodoorbell'];

          Object.keys(data.products).forEach(key => {
            // Voeg alle gewenste extra producten toe
            if (requiredProducts.includes(key) || key.startsWith('extra_')) {
              extraProducts[key] = data.products[key];
            }
          });
        }
      }

      console.log('Extra products:', extraProducts); // Debug log
      setProducts(extraProducts);

      // Always calculate quote, even with no extra products
      calculateQuote();
    } catch (error) {
      console.error('Error fetching products:', error);
      // Still calculate quote with base package
      calculateQuote();
    } finally {
      setLoading(false);
    }
  };

  const calculateQuote = async () => {
    try {
      const data = await simpleQuoteService.calculateQuote({
        extra_products: extraProducts,
        payment_terms: paymentTerms,
        discount_percentage: discountPercentage,
        videodoorbell_free: videodoorbellFree,
        videodoorbell_paid: videodoorbellPaid
      });

      setCalculations(data.calculations);
    } catch (error) {
      console.error('Error calculating quote:', error);
    }
  };

  const updateProductQuantity = (productKey: string, change: number) => {
    setExtraProducts(prev => ({
      ...prev,
      [productKey]: Math.max(0, (prev[productKey] || 0) + change)
    }));
    // The useEffect will handle recalculation when extraProducts changes
  };

  const handlePaymentTermsChange = (terms: string) => {
    setPaymentTerms(terms);
    // Reset discount when payment terms change
    const maxDiscount = getMaxDiscountForTerms(terms);
    setDiscountPercentage(Math.min(discountPercentage, maxDiscount));
  };

  const handleDiscountChange = (value: number) => {
    const maxDiscount = getMaxDiscountForTerms(paymentTerms);
    setDiscountPercentage(Math.min(value, maxDiscount));
  };

  const getPaymentTermsLabel = (terms: string) => {
    switch (terms) {
      case '1_term': return '1 termijn';
      case '2_terms': return '2 termijnen';
      case '3_terms': return '3 termijnen';
      default: return terms;
    }
  };

  const convertToBackendFormat = (category: string, paymentTerms: string) => {
    const categoryMap: Record<string, string> = {
      'alarm': 'ALARM',
      'cameras': 'CAMERAS',
      'alarm_cameras': 'ALARM_CAMERAS'
    };

    const paymentMap: Record<string, string> = {
      '1_term': 'ONE_TERM',
      '2_terms': 'TWO_TERMS',
      '3_terms': 'THREE_TERMS'
    };

    return {
      category: categoryMap[category] || category,
      payment_terms: paymentMap[paymentTerms] || paymentTerms
    };
  };

  const getCategoryTitle = () => {
    switch (category) {
      case 'alarm': return 'ALARM';
      case 'cameras': return 'CAMERAS';
      case 'alarm_cameras': return 'ALARM + CAMERAS';
      default: return 'Offerte';
    }
  };

  const handleContinue = () => {
    // Convert to backend format
    const backendFormat = convertToBackendFormat(category, paymentTerms);

    // Navigate to signature page with all data
    const config = {
      customerData,
      category: backendFormat.category,
      extraProducts,
      paymentTerms: backendFormat.payment_terms,
      discountPercentage,
      videodoorbellFree,
      videodoorbellPaid,
      calculations
    };

    navigate('/simple-quotes/signature', {
      state: { quoteConfig: config }
    });
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <MobileContainer>
      <Breadcrumbs />

      <MobilePageHeader
        title={`${getCategoryTitle()} Configuratie`}
        subtitle={`Personaliseer uw offerte, ${customerData.customer_name}`}
      />

      <div className="space-y-6">
        {/* Header with back button */}
        <div className="flex items-center justify-between">
          <EnhancedButton
            variant="outline"
            size="sm"
            onClick={() => navigate('/simple-quotes')}
            icon={<FaArrowLeft />}
          >
            Terug
          </EnhancedButton>
        </div>

        {/* Progress indicator */}
        <ProgressSteps steps={[
          { id: 'customer', label: 'Klantgegevens', status: 'completed' },
          { id: 'system', label: 'Systeem kiezen', status: 'completed' },
          { id: 'config', label: 'Configureren', status: 'current' }
        ]} />

        {/* Customer summary card */}
        <CustomerSummary
          name={customerData.customer_name}
          email={customerData.customer_email}
          phone={customerData.customer_phone}
          address={customerData.customer_address}
          city={customerData.customer_city}
          systemType={getCategoryTitle()}
        />

        {/* Base package info */}
        <InfoCard
          title={`${getCategoryTitle()} - Basis Pakket (inbegrepen)`}
          icon={<FaCheck />}
          variant="success"
        >
          {category === 'alarm' && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {[
                '1x Centrale Hub',
                '1x Bediendeel',
                '1x Sirene',
                '2x PIR Camera',
                '2x Shock Sensor',
                '1x Magneetcontact',
                '2x Brandmelder'
              ].map((item, index) => (
                <div key={index} className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2 flex-shrink-0"></span>
                  <span className="text-sm">{item}</span>
                </div>
              ))}
            </div>
          )}
          {category === 'cameras' && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {[
                'IP Camera\'s binnen/buiten',
                'Network Video Recorder',
                'Mobiele app toegang',
                'Cloud opslag optie',
                'Nachtzicht functie'
              ].map((item, index) => (
                <div key={index} className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2 flex-shrink-0"></span>
                  <span className="text-sm">{item}</span>
                </div>
              ))}
            </div>
          )}
          {category === 'alarm_cameras' && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              {[
                'Volledige alarmsysteem',
                'IP Camera systeem',
                'Geïntegreerde bediening',
                'Mobiele app controle',
                'Professionele monitoring'
              ].map((item, index) => (
                <div key={index} className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2 flex-shrink-0"></span>
                  <span className="text-sm">{item}</span>
                </div>
              ))}
            </div>
          )}
        </InfoCard>

        {/* Extra products */}
        <div className="bg-white dark:bg-dark-secondary rounded-xl shadow-sm border border-gray-200 dark:border-dark-border p-4 sm:p-6">
          <h3 className="font-semibold text-gray-900 dark:text-dark-text mb-6 flex items-center text-base sm:text-lg">
            <FaPlus className="mr-3 text-blue-600" />
            Extra Producten Toevoegen
          </h3>

          {Object.keys(products).length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-dark-text-light">
              <FaEye className="mx-auto text-3xl mb-3" />
              <p>Geen extra producten beschikbaar voor dit systeem</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {Object.entries(products).map(([key, product]) => (
                <ProductItemCard
                  key={key}
                  name={product.name}
                  description={product.description}
                  price={(product as any).price_with_margin || product.price_incl_vat}
                  image={simpleQuoteService.getProductImageUrl(product.image)}
                  quantity={extraProducts[key] || 0}
                  onQuantityChange={(quantity) => setExtraProducts(prev => ({ ...prev, [key]: quantity }))}
                />
              ))}
            </div>
          )}
        </div>

        {/* Video doorbell - Beschikbaar bij alle categorieën */}
        <InfoCard
          title="VIDEO DEURBEL"
          icon={<FaVideo />}
          variant="info"
        >
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
                Bij alle categorieën
              </span>
            </div>
            <p className="text-sm">
              Slimme video deurbel - Normaal €249,99, kan gratis gemaakt worden!
            </p>
          </div>

          <div className="space-y-3">
            <label className="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-50 dark:hover:bg-dark-tertiary transition-colors cursor-pointer mobile-touch-target">
              <input
                type="radio"
                name="videodoorbell"
                checked={!videodoorbellFree && !videodoorbellPaid}
                onChange={() => {
                  setVideodoorbellFree(false);
                  setVideodoorbellPaid(false);
                }}
                className="mr-3 text-blue-600"
              />
              <span>Geen video deurbel</span>
            </label>

            <label className="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-50 dark:hover:bg-dark-tertiary transition-colors cursor-pointer border-2 border-green-200 bg-green-50 dark:bg-green-900/20 mobile-touch-target">
              <input
                type="radio"
                name="videodoorbell"
                checked={videodoorbellFree}
                onChange={() => {
                  setVideodoorbellFree(true);
                  setVideodoorbellPaid(false);
                }}
                className="mr-3 text-green-600"
              />
              <div className="flex-1">
                <span className="font-semibold flex items-center">
                  🎁 GRATIS Video Deurbel
                  <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">AANBEVOLEN</span>
                </span>
                <div className="text-sm mt-1">
                  Normale waarde €249,99 → €0,00
                </div>
              </div>
            </label>

            <label className="flex items-center p-3 rounded-lg hover:bg-white hover:bg-opacity-50 dark:hover:bg-dark-tertiary transition-colors cursor-pointer mobile-touch-target">
              <input
                type="radio"
                name="videodoorbell"
                checked={videodoorbellPaid}
                onChange={() => {
                  setVideodoorbellFree(false);
                  setVideodoorbellPaid(true);
                }}
                className="mr-3 text-blue-600"
              />
              <div className="flex-1">
                <span>Video Deurbel</span>
                <div className="text-sm mt-1">€249,99 incl. BTW</div>
              </div>
            </label>
          </div>
        </InfoCard>

        {/* Payment terms */}
        <div className="bg-white dark:bg-dark-secondary rounded-xl shadow-sm border border-gray-200 dark:border-dark-border p-4 sm:p-6">
          <h3 className="font-semibold text-gray-900 dark:text-dark-text mb-4 text-base sm:text-lg">Betalingsvoorwaarden</h3>
          <p className="text-sm text-gray-600 dark:text-dark-text-light mb-4">
            Kies hoe u de installatiekosten wilt betalen. Meer termijnen = lagere maximale korting.
          </p>

          <div className="space-y-3">
            {[
              { value: '1_term', label: 'Eenmalig (1 termijn)', discount: '25%', recommended: true },
              { value: '2_terms', label: 'In 2 termijnen', discount: '15%', recommended: false },
              { value: '3_terms', label: 'In 3 termijnen', discount: '10%', recommended: false }
            ].map((option) => (
              <label key={option.value} className="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-tertiary transition-colors cursor-pointer mobile-touch-target border border-gray-200 dark:border-dark-border">
                <input
                  type="radio"
                  name="paymentTerms"
                  value={option.value}
                  checked={paymentTerms === option.value}
                  onChange={(e) => handlePaymentTermsChange(e.target.value)}
                  className="mr-3 text-blue-600"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{option.label}</span>
                    {option.recommended && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">AANBEVOLEN</span>
                    )}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-dark-text-light mt-1">
                    Max korting: {option.discount}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Discount slider */}
        <div className="bg-white dark:bg-dark-secondary rounded-xl shadow-sm border border-gray-200 dark:border-dark-border p-4 sm:p-6">
          <h3 className="font-semibold text-gray-900 dark:text-dark-text mb-6 text-base sm:text-lg">Korting op installatiekosten</h3>

          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-dark-text-light">Korting niveau:</span>
              <span className="font-semibold text-lg text-blue-600 dark:text-blue-400">
                {discountPercentage}%
              </span>
            </div>

            <div className="relative">
              <input
                type="range"
                min="0"
                max={getMaxDiscountForTerms(paymentTerms)}
                step="0.5"
                value={discountPercentage}
                onChange={(e) => handleDiscountChange(parseFloat(e.target.value))}
                className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer mobile-touch-target"
                style={{
                  background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${(discountPercentage / getMaxDiscountForTerms(paymentTerms)) * 100}%, #e5e7eb ${(discountPercentage / getMaxDiscountForTerms(paymentTerms)) * 100}%, #e5e7eb 100%)`
                }}
              />
              <div className="flex justify-between text-xs text-gray-500 dark:text-dark-text-light mt-2">
                <span>0%</span>
                <span>{Math.round(getMaxDiscountForTerms(paymentTerms) * 0.33)}%</span>
                <span>{Math.round(getMaxDiscountForTerms(paymentTerms) * 0.67)}%</span>
                <span>{getMaxDiscountForTerms(paymentTerms)}%</span>
              </div>
            </div>

            <InfoCard
              title={`Betalingsvoorwaarden: ${getPaymentTermsLabel(paymentTerms)}`}
              variant="info"
            >
              <div className="text-sm">
                {paymentTerms === '1_term' && 'Hoogste korting mogelijk - Betaal alles in één keer en krijg maximaal 25% korting'}
                {paymentTerms === '2_terms' && 'Gemiddelde korting mogelijk - Betaal in 2 termijnen en krijg maximaal 15% korting'}
                {paymentTerms === '3_terms' && 'Beperkte korting mogelijk - Betaal in 3 termijnen en krijg maximaal 10% korting'}
              </div>
            </InfoCard>
          </div>
        </div>

        {/* Dynamic Price Display - Always visible */}
        {calculations ? (
          <div className="sticky top-4 z-10">
            <PriceSummary
              items={[
                { label: 'Basis pakket', value: 999.99 },
                ...(calculations.selected_extra_products?.map((product: any) => ({
                  label: `${product.quantity}x ${product.name}`,
                  value: product.total_price
                })) || []),
                ...(calculations.discount_amount > 0 ? [{
                  label: 'Korting',
                  value: -calculations.discount_amount,
                  highlight: true
                }] : []),
                { label: 'Apparatuur (maandelijks)', value: calculations.monthly_equipment_cost, subtext: 'Per maand' },
                { label: 'Meldkamer (maandelijks)', value: calculations.monthly_monitoring_cost, subtext: 'Per maand' },
                { label: 'Onderhoud (maandelijks)', value: calculations.monthly_maintenance_cost, subtext: 'Per maand' }
              ]}
              total={{
                label: 'Totaal overzicht',
                value: `€${calculations.final_installation_cost.toFixed(2)} + €${calculations.total_monthly_cost.toFixed(2)}/maand`,
                subtext: `${getCategoryTitle()} - ${getPaymentTermsLabel(paymentTerms)}`
              }}
              className="shadow-lg"
            />
          </div>
        ) : (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-center justify-center">
              <FaCalculator className="mr-3 text-blue-600 animate-pulse" />
              <span className="text-blue-900 font-medium">Prijzen berekenen...</span>
            </div>
          </div>
        )}

        {/* Continue button */}
        <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3 bg-white dark:bg-dark-secondary rounded-xl shadow-sm border border-gray-200 dark:border-dark-border p-4">
          <EnhancedButton
            variant="outline"
            size="md"
            onClick={() => navigate('/simple-quotes')}
            icon={<FaArrowLeft />}
            className="order-2 sm:order-1"
          >
            Terug naar systeem keuze
          </EnhancedButton>

          <EnhancedButton
            variant="primary"
            size="lg"
            onClick={handleContinue}
            loading={loading}
            icon={loading ? undefined : <FaArrowLeft className="rotate-180" />}
            className="order-1 sm:order-2"
            fullWidth={true}
          >
            {loading ? 'Bezig met berekenen...' : 'Verder naar ondertekening'}
          </EnhancedButton>
        </div>
      </div>
    </MobileContainer>
  );
};

export default SimpleQuoteBuilderPage;
