from app import db
from app.models.event import Event
from app.models.user import User
from typing import List, Optional
from datetime import datetime, timezone

class EventRepository:
    def get_all(self, page: int = 1, per_page: int = 20) -> tuple[List[Event], int]:
        events = Event.query.paginate(page=page, per_page=per_page, error_out=False)
        return events.items, events.total

    def get_by_status(self, status: str, page: int = 1, per_page: int = 20) -> tuple[List[Event], int]:
        """Get events filtered by status with pagination."""
        query = Event.query.filter(Event.status == status).order_by(Event.scheduled_date.desc())
        events = query.paginate(page=page, per_page=per_page, error_out=False)
        return events.items, events.total

    def get_by_id(self, event_id: int) -> Optional[Event]:
        return Event.query.get(event_id)

    def get_by_user_id(self, user_id: int, page: int = 1, per_page: int = 20) -> tuple[List[Event], int]:
        """
        Get all events assigned to a specific user.
        This ensures that users only see events that are specifically assigned to them.

        Args:
            user_id: The ID of the user
            page: The page number for pagination
            per_page: The number of items per page

        Returns:
            tuple[List[Event], int]: A tuple containing the list of events and the total count
        """
        # Filter by events that have this user assigned to them
        query = Event.query.join(Event.users).filter(User.id == user_id)
        events = query.paginate(page=page, per_page=per_page, error_out=False)
        return events.items, events.total

    def create(self, customer_id: Optional[int], event_type: str, description: str, scheduled_date: datetime, user_ids: Optional[List[int]] = None, document_id: Optional[int] = None) -> Event:
        event = Event(
            customer_id=customer_id,
            document_id=document_id,
            event_type=event_type,
            description=description,
            scheduled_date=scheduled_date,
            status="pending"
        )

        # Add users to the event if provided
        if user_ids:
            users = User.query.filter(User.id.in_(user_ids)).all()
            event.users.extend(users)

        db.session.add(event)
        db.session.commit()
        return event

    # Legacy method for backward compatibility
    def create_legacy(self, customer_id: Optional[int], event_type: str, description: str, scheduled_date: datetime, user_id: Optional[int] = None, document_id: Optional[int] = None) -> Event:
        user_ids = [user_id] if user_id else None
        return self.create(customer_id, event_type, description, scheduled_date, user_ids, document_id)

    def update(self, event: Event, customer_id: Optional[int], event_type: str, description: str, scheduled_date: datetime, user_ids: Optional[List[int]] = None, document_id: Optional[int] = None) -> Event:
        event.customer_id = customer_id
        event.document_id = document_id
        event.event_type = event_type
        event.description = description
        event.scheduled_date = scheduled_date

        # Update users assigned to the event
        if user_ids is not None:
            # Clear existing users
            event.users.clear()
            # Add new users
            if user_ids:
                users = User.query.filter(User.id.in_(user_ids)).all()
                event.users.extend(users)

        db.session.commit()
        return event

    # Legacy method for backward compatibility
    def update_legacy(self, event: Event, customer_id: Optional[int], event_type: str, description: str, scheduled_date: datetime, user_id: Optional[int] = None, document_id: Optional[int] = None) -> Event:
        user_ids = [user_id] if user_id else []
        return self.update(event, customer_id, event_type, description, scheduled_date, user_ids, document_id)

    def complete(self, event: Event, completed_by_user_id: int = None) -> Event:
        event.status = "completed"
        event.completed_at = datetime.now(timezone.utc)
        if completed_by_user_id:
            event.completed_by = completed_by_user_id
        db.session.commit()
        return event

    def delete(self, event_id: int) -> bool:
        event = self.get_by_id(event_id)
        if not event:
            return False
        db.session.delete(event)
        db.session.commit()
        return True

    def get_active_events_by_user_customer_type(self, user_id: int, customer_id: int, event_type: str) -> List[Event]:
        """
        Get all active (pending) events for a specific user, customer, and event type.
        This is used to determine if a user has temporary view permission for a document type.

        Args:
            user_id: The ID of the user
            customer_id: The ID of the customer
            event_type: The type of event/document

        Returns:
            List[Event]: A list of active events matching the criteria
        """
        return Event.query.join(Event.users).filter(
            User.id == user_id,
            Event.customer_id == customer_id,
            Event.event_type == event_type,
            Event.status == "pending"
        ).all()

    def get_pending_events_by_user_and_customer(self, user_id: int, customer_id: int) -> List[Event]:
        """
        Get all pending events for a specific user and customer.
        This is used to determine if a user has access to a customer's details.

        Args:
            user_id: The ID of the user
            customer_id: The ID of the customer

        Returns:
            List[Event]: A list of pending events matching the criteria
        """
        return Event.query.join(Event.users).filter(
            User.id == user_id,
            Event.customer_id == customer_id,
            Event.status == "pending"
        ).all()

    def get_events_by_customer_and_type(self, customer_id: int, event_type: str) -> List[Event]:
        """
        Get all events for a specific customer and event type.
        This is used to find installation events for a quotation.

        Args:
            customer_id: The ID of the customer
            event_type: The type of event

        Returns:
            List[Event]: A list of events matching the criteria
        """
        return Event.query.filter(
            Event.customer_id == customer_id,
            Event.event_type == event_type
        ).all()
