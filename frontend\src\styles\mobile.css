/* Mobile-specific enhancements for AMSPM Customer Management */

/* Mobile viewport and safe areas */
@supports (padding: max(0px)) {
  .mobile-safe-area {
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
    padding-bottom: max(12px, env(safe-area-inset-bottom));
  }
}

/* Mobile-first responsive breakpoints */
@media (max-width: 640px) {
  /* Enhanced mobile containers */
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  
  /* Mobile-friendly headers */
  .mobile-page-header {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid var(--amspm-light-gray);
  }
  
  .dark .mobile-page-header {
    border-bottom-color: var(--dark-border);
  }
  
  /* Mobile navigation improvements */
  .mobile-nav-toggle {
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .mobile-nav-toggle:hover {
    background-color: var(--amspm-light-gray);
  }
  
  .dark .mobile-nav-toggle:hover {
    background-color: var(--dark-hover);
  }
  
  /* Mobile table improvements */
  .mobile-table-container {
    margin: 0 -0.75rem;
    padding: 0 0.75rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Mobile card enhancements */
  .mobile-card-enhanced {
    margin-bottom: 0.75rem;
    padding: 0.875rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }
  
  .mobile-card-enhanced:last-child {
    margin-bottom: 0;
  }
  
  /* Mobile form improvements */
  .mobile-form-group {
    margin-bottom: 1rem;
  }

  .mobile-form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.375rem;
    display: block;
    color: var(--amspm-primary);
  }

  .dark .mobile-form-group label {
    color: var(--dark-text-light);
  }

  .mobile-form-input {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    border: 1px solid var(--amspm-light-gray);
    background-color: white;
    min-height: 44px;
    -webkit-appearance: none;
    appearance: none;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    touch-action: manipulation;
  }

  .dark .mobile-form-input {
    background-color: var(--dark-input);
    border-color: var(--dark-border);
    color: var(--dark-text);
  }

  .mobile-form-input:focus {
    outline: none;
    border-color: var(--amspm-primary);
    box-shadow: 0 0 0 3px rgba(0, 48, 135, 0.1);
  }

  .dark .mobile-form-input:focus {
    border-color: var(--dark-accent);
    box-shadow: 0 0 0 3px rgba(26, 117, 255, 0.2);
  }

  /* Mobile form grid layouts */
  .mobile-form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .mobile-form-row {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  /* Mobile form actions */
  .mobile-form-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--amspm-light-gray);
  }

  .dark .mobile-form-actions {
    border-top-color: var(--dark-border);
  }

  /* Mobile form validation */
  .mobile-form-error {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }

  .dark .mobile-form-error {
    color: #fca5a5;
  }

  .mobile-form-input.error {
    border-color: #dc2626;
  }

  .dark .mobile-form-input.error {
    border-color: #ef4444;
  }

  /* Mobile textarea */
  .mobile-form-textarea {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    border: 1px solid var(--amspm-light-gray);
    background-color: white;
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  .dark .mobile-form-textarea {
    background-color: var(--dark-input);
    border-color: var(--dark-border);
    color: var(--dark-text);
  }

  .mobile-form-textarea:focus {
    outline: none;
    border-color: var(--amspm-primary);
    box-shadow: 0 0 0 3px rgba(0, 48, 135, 0.1);
  }

  .dark .mobile-form-textarea:focus {
    border-color: var(--dark-accent);
    box-shadow: 0 0 0 3px rgba(26, 117, 255, 0.2);
  }

  /* Mobile select */
  .mobile-form-select {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    border: 1px solid var(--amspm-light-gray);
    background-color: white;
    min-height: 44px;
    -webkit-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23666666'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem 1rem;
    padding-right: 2.5rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  .dark .mobile-form-select {
    background-color: var(--dark-input);
    border-color: var(--dark-border);
    color: var(--dark-text);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23b3b3b3'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  }

  .mobile-form-select:focus {
    outline: none;
    border-color: var(--amspm-primary);
    box-shadow: 0 0 0 3px rgba(0, 48, 135, 0.1);
  }

  .dark .mobile-form-select:focus {
    border-color: var(--dark-accent);
    box-shadow: 0 0 0 3px rgba(26, 117, 255, 0.2);
  }
  
  /* Mobile button improvements */
  .mobile-btn-stack {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }
  
  .mobile-btn-stack .btn {
    width: 100%;
    justify-content: center;
  }
  
  /* Mobile modal improvements */
  .mobile-modal-overlay {
    padding: 0;
    align-items: flex-end;
  }

  .mobile-modal-content {
    width: 100%;
    max-width: none;
    margin: 0;
    border-radius: 1rem 1rem 0 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 90vh;
    animation: slideUpIn 0.3s ease-out;
    padding: 1rem;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Handle for mobile modal */
  .mobile-modal-content::before {
    content: '';
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2rem;
    height: 0.25rem;
    background-color: var(--amspm-light-gray);
    border-radius: 0.125rem;
  }

  .dark .mobile-modal-content::before {
    background-color: var(--dark-border);
  }

  @keyframes slideUpIn {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  /* Mobile modal form improvements */
  .mobile-modal-content .form-actions {
    position: sticky;
    bottom: -1rem;
    left: -1rem;
    right: -1rem;
    background: white;
    border-top: 1px solid var(--amspm-light-gray);
    padding: 1rem;
    margin: 1rem -1rem -1rem -1rem;
    border-radius: 0;
  }

  .dark .mobile-modal-content .form-actions {
    background: var(--dark-secondary);
    border-top-color: var(--dark-border);
  }
  
  /* Mobile search improvements */
  .mobile-search-container {
    position: relative;
    margin-bottom: 1rem;
  }
  
  .mobile-search-input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--amspm-light-gray);
    background-color: white;
  }
  
  .dark .mobile-search-input {
    background-color: var(--dark-input);
    border-color: var(--dark-border);
    color: var(--dark-text);
  }
  
  .mobile-search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--amspm-text-light);
  }
  
  .dark .mobile-search-icon {
    color: var(--dark-text-light);
  }
  
  /* Mobile pagination improvements */
  .mobile-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 0;
  }
  
  .mobile-pagination-btn {
    padding: 0.5rem 0.75rem;
    min-width: 44px;
    min-height: 44px;
    border-radius: 0.375rem;
    border: 1px solid var(--amspm-light-gray);
    background-color: white;
    color: var(--amspm-text);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .dark .mobile-pagination-btn {
    background-color: var(--dark-secondary);
    border-color: var(--dark-border);
    color: var(--dark-text);
  }
  
  .mobile-pagination-btn:hover {
    background-color: var(--amspm-light-gray);
  }
  
  .dark .mobile-pagination-btn:hover {
    background-color: var(--dark-hover);
  }
  
  .mobile-pagination-btn.active {
    background-color: var(--amspm-primary);
    color: white;
    border-color: var(--amspm-primary);
  }
  
  .dark .mobile-pagination-btn.active {
    background-color: var(--dark-accent);
    border-color: var(--dark-accent);
  }
  
  /* Mobile loading improvements */
  .mobile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
  }
  
  .mobile-loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--amspm-light-gray);
    border-top: 2px solid var(--amspm-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  .dark .mobile-loading-spinner {
    border-color: var(--dark-border);
    border-top-color: var(--dark-accent);
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Mobile error/success messages */
  .mobile-message {
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }
  
  .mobile-message-error {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }
  
  .dark .mobile-message-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #fca5a5;
    border-color: rgba(239, 68, 68, 0.3);
  }
  
  .mobile-message-success {
    background-color: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
  }
  
  .dark .mobile-message-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #6ee7b7;
    border-color: rgba(16, 185, 129, 0.3);
  }
  
  /* Mobile accessibility improvements */
  .mobile-focus-visible:focus-visible {
    outline: 2px solid var(--amspm-primary);
    outline-offset: 2px;
  }
  
  .dark .mobile-focus-visible:focus-visible {
    outline-color: var(--dark-accent);
  }
  
  /* Mobile touch improvements */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Mobile scroll improvements */
  .mobile-scroll-container {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }
  
  .mobile-scroll-container::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .mobile-scroll-container::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .mobile-scroll-container::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }
  
  .dark .mobile-scroll-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

/* Tablet-specific improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .tablet-modal-content {
    max-width: 90%;
    margin: 2rem auto;
  }
}

/* Additional mobile utilities */
@media (max-width: 640px) {
  /* Mobile viewport fixes */
  .mobile-viewport-fix {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }

  /* Mobile safe area support */
  .mobile-safe-area {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Mobile overflow fixes */
  .mobile-overflow-hidden {
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-overflow-scroll {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile text utilities */
  .mobile-text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .mobile-text-wrap {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Mobile spacing utilities */
  .mobile-spacing-xs { margin: 0.25rem; }
  .mobile-spacing-sm { margin: 0.5rem; }
  .mobile-spacing-md { margin: 1rem; }
  .mobile-spacing-lg { margin: 1.5rem; }
  .mobile-spacing-xl { margin: 2rem; }

  .mobile-padding-xs { padding: 0.25rem; }
  .mobile-padding-sm { padding: 0.5rem; }
  .mobile-padding-md { padding: 1rem; }
  .mobile-padding-lg { padding: 1.5rem; }
  .mobile-padding-xl { padding: 2rem; }

  /* Mobile flex utilities */
  .mobile-flex-col {
    display: flex;
    flex-direction: column;
  }

  .mobile-flex-row {
    display: flex;
    flex-direction: row;
  }

  .mobile-flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .mobile-flex-wrap {
    flex-wrap: wrap;
  }

  .mobile-flex-nowrap {
    flex-wrap: nowrap;
  }

  /* Mobile grid utilities */
  .mobile-grid-1 { grid-template-columns: 1fr; }
  .mobile-grid-2 { grid-template-columns: repeat(2, 1fr); }
  .mobile-grid-auto { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }

  /* Mobile animation utilities */
  .mobile-transition {
    transition: all 0.3s ease;
  }

  .mobile-transition-fast {
    transition: all 0.15s ease;
  }

  .mobile-transition-slow {
    transition: all 0.5s ease;
  }

  /* Mobile performance optimizations */
  .mobile-will-change {
    will-change: transform;
  }

  .mobile-gpu-accelerated {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  /* Mobile print styles */
  @media print {
    .mobile-no-print {
      display: none !important;
    }
  }

  /* Enhanced animations for Offerte system */
  @keyframes mobile-slide-up {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes mobile-fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes mobile-bounce-in {
    0% {
      transform: scale(0.3);
      opacity: 0;
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes mobile-pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes mobile-shake {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
      transform: translateX(2px);
    }
  }

  .mobile-slide-up {
    animation: mobile-slide-up 0.3s ease-out;
  }

  .mobile-fade-in {
    animation: mobile-fade-in 0.4s ease-out;
  }

  .mobile-bounce-in {
    animation: mobile-bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .mobile-pulse {
    animation: mobile-pulse 2s infinite;
  }

  .mobile-shake {
    animation: mobile-shake 0.5s ease-in-out;
  }

  /* Enhanced Offerte-specific mobile styles */
  .offerte-progress-step {
    transition: all 0.3s ease;
    transform: scale(1);
  }

  .offerte-progress-step.active {
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
  }

  .offerte-progress-step.completed {
    animation: mobile-bounce-in 0.5s ease-out;
  }

  /* Enhanced product cards for mobile */
  .offerte-product-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .offerte-product-card:hover,
  .offerte-product-card:focus {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .offerte-product-card:active {
    transform: translateY(-2px);
    transition: all 0.1s ease;
  }

  /* Enhanced quantity selector */
  .offerte-quantity-btn {
    transition: all 0.2s ease;
    transform: scale(1);
  }

  .offerte-quantity-btn:active {
    transform: scale(0.95);
  }

  .offerte-quantity-btn:hover {
    transform: scale(1.05);
  }

  /* Enhanced signature pad for mobile */
  .offerte-signature-canvas {
    touch-action: none;
    border: 2px dashed #e5e7eb;
    border-radius: 0.5rem;
    background: linear-gradient(45deg, #f9fafb 25%, transparent 25%),
                linear-gradient(-45deg, #f9fafb 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f9fafb 75%),
                linear-gradient(-45deg, transparent 75%, #f9fafb 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  }

  .dark .offerte-signature-canvas {
    border-color: #374151;
    background: linear-gradient(45deg, #1f2937 25%, transparent 25%),
                linear-gradient(-45deg, #1f2937 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #1f2937 75%),
                linear-gradient(-45deg, transparent 75%, #1f2937 75%);
  }

  /* Enhanced price summary animations */
  .offerte-price-item {
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(-20px);
    animation: mobile-fade-in 0.5s ease-out forwards;
  }

  .offerte-price-item:nth-child(1) { animation-delay: 0.1s; }
  .offerte-price-item:nth-child(2) { animation-delay: 0.2s; }
  .offerte-price-item:nth-child(3) { animation-delay: 0.3s; }
  .offerte-price-item:nth-child(4) { animation-delay: 0.4s; }
  .offerte-price-item:nth-child(5) { animation-delay: 0.5s; }

  .offerte-price-total {
    animation: mobile-bounce-in 0.8s ease-out 0.6s both;
  }

  /* Enhanced success page animations */
  .offerte-success-checkmark {
    animation: mobile-bounce-in 1s ease-out 0.3s both;
  }

  .offerte-success-content {
    animation: mobile-fade-in 0.8s ease-out 0.8s both;
  }

  .offerte-success-actions {
    animation: mobile-slide-up 0.6s ease-out 1.2s both;
  }

  /* Enhanced mobile form validation */
  .offerte-form-field.error {
    animation: mobile-shake 0.5s ease-in-out;
  }

  .offerte-form-field.success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  /* Enhanced mobile loading states */
  .offerte-loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
  }

  .dark .offerte-loading-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }

  @keyframes loading-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Enhanced mobile sticky elements */
  .offerte-sticky-summary {
    position: sticky;
    top: 1rem;
    z-index: 10;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* Enhanced mobile swipe gestures */
  .offerte-swipeable {
    touch-action: pan-x;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Enhanced mobile accessibility */
  .offerte-screen-reader-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Enhanced mobile focus indicators */
  .offerte-focus-ring:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 0.375rem;
  }

  .dark .offerte-focus-ring:focus-visible {
    outline-color: #60a5fa;
  }

  /* Mobile Calendar Specific Styles */
  .mobile-calendar {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    padding-bottom: 120px; /* Account for fixed bottom navigation */
  }

  .mobile-calendar-status-bar {
    background: linear-gradient(to right, #f8fafc, #e2e8f0);
    border-bottom: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .mobile-calendar-battery {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .mobile-calendar-battery-icon {
    width: 1.5rem;
    height: 0.75rem;
    background-color: #d1d5db;
    border-radius: 0.125rem;
    border: 1px solid #9ca3af;
  }

  .mobile-calendar-header {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1rem;
  }

  .mobile-calendar-header h1 {
    font-size: 1.125rem;
    font-weight: 700;
    color: #111827;
    letter-spacing: 0.05em;
  }

  .mobile-calendar-nav {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 1rem;
    border-left: none;
    border-right: none;
  }

  .mobile-calendar-nav-button {
    background: #374151;
    color: #ffffff;
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    min-width: 44px;
  }

  .mobile-calendar-nav-button:hover {
    background: #4b5563;
    transform: translateY(-1px);
  }

  .mobile-calendar-nav-button:active {
    transform: translateY(0);
  }

  .mobile-calendar-week-range {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    text-align: center;
    line-height: 1.2;
    letter-spacing: -0.025em;
  }

  .mobile-calendar-day-header {
    background: #f9fafb;
    border-bottom: 1px solid #f3f4f6;
    padding: 0.75rem 1rem;
    border-left: none;
    border-right: none;
  }

  .mobile-calendar-day-name {
    font-weight: 700;
    color: #111827;
    font-size: 1rem;
    text-align: left;
    letter-spacing: -0.025em;
  }

  .mobile-calendar-day-date {
    font-weight: 700;
    color: #111827;
    font-size: 1rem;
    text-align: right;
    letter-spacing: -0.025em;
  }

  .mobile-calendar-event {
    background: #ffffff;
    border-bottom: 1px solid #f3f4f6;
    padding: 0.75rem 1rem;
    transition: background-color 0.2s ease;
    border-left: none;
    border-right: none;
    position: relative;
  }

  .mobile-calendar-event:hover {
    background: #f9fafb;
  }

  .mobile-calendar-event-time {
    color: #111827;
    font-weight: 500;
    font-size: 0.875rem;
    min-width: 2.5rem;
    letter-spacing: -0.025em;
    text-align: left;
  }

  .mobile-calendar-status-dot {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-top: 0.375rem;
    flex-shrink: 0;
  }

  .mobile-calendar-status-dot.pending {
    background-color: #3b82f6;
  }

  .mobile-calendar-status-dot.completed {
    background-color: #10b981;
  }

  .mobile-calendar-status-dot.cancelled {
    background-color: #ef4444;
  }

  .mobile-calendar-status-dot.werkbon {
    background-color: #f59e0b;
  }

  .mobile-calendar-status-dot.onderhoudsbon {
    background-color: #8b5cf6;
  }

  .mobile-calendar-status-dot.offerte {
    background-color: #3b82f6;
  }

  .mobile-calendar-status-dot.factuur {
    background-color: #ef4444;
  }

  .mobile-calendar-status-dot.general {
    background-color: #6b7280;
  }

  .mobile-calendar-event-type {
    color: #111827;
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    letter-spacing: -0.025em;
  }

  .mobile-calendar-customer-name {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    letter-spacing: -0.025em;
  }

  .mobile-calendar-assigned-users {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    letter-spacing: -0.025em;
  }

  .mobile-calendar-assigned-users-icon {
    width: 0.75rem;
    height: 0.75rem;
    margin-right: 0.25rem;
    flex-shrink: 0;
  }

  .mobile-calendar-status-badge {
    display: flex;
    align-items: center;
    color: #ea580c;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.25rem;
    padding-left: 0.5rem;
    letter-spacing: -0.025em;
  }

  .mobile-calendar-status-badge-icon {
    width: 0.75rem;
    height: 0.75rem;
    margin-right: 0.25rem;
  }

  .mobile-calendar-bottom-nav {
    background: #374151;
    padding: 0.75rem 1rem;
    border-top: 1px solid #4b5563;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
  }

  .mobile-calendar-url {
    color: #ffffff;
    font-size: 0.875rem;
    text-align: center;
    margin-bottom: 0.5rem;
    font-weight: 500;
    letter-spacing: -0.025em;
  }

  .mobile-calendar-nav-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    align-items: center;
  }

  .mobile-calendar-nav-button-small {
    padding: 0.5rem;
    color: #ffffff;
    background: transparent;
    border: none;
    cursor: pointer;
    transition: opacity 0.2s ease;
  }

  .mobile-calendar-nav-button-small:hover {
    opacity: 0.8;
  }

  .mobile-calendar-nav-button-small:active {
    opacity: 0.6;
  }

  .mobile-calendar-empty-state {
    padding: 1.5rem 1rem;
    text-align: center;
    color: #9ca3af;
    font-size: 0.875rem;
  }

  /* FullCalendar List View Mobile Improvements */
  .fc-list-event {
    padding: 0.5rem !important;
    border-bottom: 1px solid #e5e7eb !important;
  }

  .fc-list-event:hover {
    background-color: #f9fafb !important;
  }

  .fc-list-event-time {
    padding: 0.5rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    font-size: 0.875rem !important;
    min-width: 60px !important;
  }

  .fc-list-event-title {
    padding: 0.5rem !important;
  }

  .fc-list-day-cushion {
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    background-color: #f3f4f6 !important;
    border-bottom: 1px solid #e5e7eb !important;
    font-size: 0.875rem !important;
  }

  .fc-list-table {
    border: none !important;
  }

  .fc-list-day-side {
    padding: 0.75rem 1rem !important;
    background-color: #f9fafb !important;
    border-right: 1px solid #e5e7eb !important;
    font-weight: 500 !important;
    color: #6b7280 !important;
    font-size: 0.75rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
  }

  .fc-list-day-text {
    font-weight: 600 !important;
    color: #374151 !important;
    font-size: 0.875rem !important;
  }
}

/* General FullCalendar List View Improvements (all screen sizes) */
.fc-list-event {
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.fc-list-event:hover {
  background-color: #f8fafc !important;
}

.fc-list-event-time {
  font-weight: 600 !important;
  color: #4b5563 !important;
  vertical-align: top !important;
}

.fc-list-event-title {
  line-height: 1.4 !important;
}

.fc-list-day-cushion {
  background-color: #f8fafc !important;
  border-bottom: 2px solid #e2e8f0 !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
}

.fc-list-table td {
  border-top: none !important;
  vertical-align: top !important;
}

.fc-list-table .fc-list-event-time {
  width: 80px !important;
  padding-right: 1rem !important;
}

/* Event content custom styling */
.event-content-list {
  max-width: 100% !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: normal !important;
}

/* Better line length control for event descriptions */
.event-content-list .font-semibold {
  min-width: 0 !important;
  max-width: 100% !important;
  /* Ensure reasonable line length - not too short, not too long */
  min-width: 200px !important;
  max-width: 400px !important;
}

/* Ensure text wrapping in list view events */
.fc-list-event-title {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: normal !important;
  white-space: normal !important;
  line-height: 1.4 !important;
}

/* Better text handling for event descriptions */
.event-content-list .font-semibold {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: normal !important;
  white-space: normal !important;
  line-height: 1.4 !important;
  max-width: 100% !important;
}

/* Force text wrapping in FullCalendar list view */
.fc-list-event-title {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: normal !important;
  white-space: normal !important;
  line-height: 1.4 !important;
  max-width: 100% !important;
}

/* Ensure event content doesn't overflow */
.fc-list-event {
  overflow: hidden !important;
}

.fc-list-event td {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: normal !important;
  white-space: normal !important;
}

/* Additional FullCalendar list view improvements */
.fc-list-table {
  table-layout: fixed !important;
}

.fc-list-event-time {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
}

.fc-list-event-title {
  width: auto !important;
  min-width: 0 !important;
  max-width: none !important;
}

/* Ensure text wrapping in all event content */
.fc-list-event .event-content-list,
.fc-list-event .font-semibold,
.fc-list-event p,
.fc-list-event div {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: normal !important;
  white-space: normal !important;
  line-height: 1.4 !important;
  max-width: 100% !important;
}

/* Event Details Modal Mobile Improvements */
@media (max-width: 640px) {
  /* Modal container adjustments for mobile */
  .fixed.inset-0 {
    padding: 0.5rem !important;
  }

  /* Ensure modal doesn't get too small on mobile */
  .max-h-\[95vh\] {
    max-height: 100vh !important;
  }

  /* Better touch targets for mobile */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  .text-xs {
    font-size: 0.75rem !important;
  }

  .text-sm {
    font-size: 0.875rem !important;
  }

  /* Better spacing for mobile cards */
  .bg-gray-50.rounded-lg {
    margin-bottom: 0.5rem;
  }

  /* Ensure proper spacing in modal sections */
  .space-y-4 > * + * {
    margin-top: 0.75rem !important;
  }

  .space-y-3 > * + * {
    margin-top: 0.5rem !important;
  }
}
