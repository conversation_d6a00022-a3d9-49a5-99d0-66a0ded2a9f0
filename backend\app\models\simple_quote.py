"""
Simple Quote system models.
This module defines models for the new simplified quote system with hardcoded products.
"""
from app import db
from datetime import datetime
from sqlalchemy import Enum
import enum

class QuoteCategory(enum.Enum):
    """Quote category enumeration - Database heeft UPPERCASE waarden."""
    ALARM = "ALARM"
    CAMERAS = "CAMERAS"
    ALARM_CAMERAS = "ALARM_CAMERAS"

class PaymentTerms(enum.Enum):
    """Payment terms enumeration - Database heeft UPPERCASE waarden."""
    ONE_TERM = "ONE_TERM"
    TWO_TERMS = "TWO_TERMS"
    THREE_TERMS = "THREE_TERMS"

class QuoteStatus(enum.Enum):
    """Quote status enumeration - Database heeft UPPERCASE waarden."""
    DRAFT = "DRAFT"
    SENT = "SENT"
    SIGNED = "SIGNED"
    CANCELLED = "CANCELLED"

class SimpleQuote(db.Model):
    """Model for simple quotes with hardcoded products."""
    __tablename__ = "simple_quotes"

    id = db.Column(db.Integer, primary_key=True)
    quote_number = db.Column(db.String(50), nullable=True, unique=True)
    customer_id = db.Column(db.Integer, db.ForeignKey("customers.id"), nullable=True)  # Nullable until signed
    created_by = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    
    # Quote details
    category = db.Column(Enum(QuoteCategory), nullable=False)
    payment_terms = db.Column(Enum(PaymentTerms), nullable=False, default='ONE_TERM')
    discount_percentage = db.Column(db.Float, default=0)  # Discount on total installation cost
    
    # Customer information (stored here until customer is created)
    customer_name = db.Column(db.String(255), nullable=False)
    customer_email = db.Column(db.String(255), nullable=True)
    customer_phone = db.Column(db.String(50), nullable=True)
    customer_address = db.Column(db.Text, nullable=True)
    customer_city = db.Column(db.String(100), nullable=True)
    customer_postal_code = db.Column(db.String(20), nullable=True)
    
    # Hardcoded product quantities (extra items beyond base package)
    extra_magneetcontact = db.Column(db.Integer, default=0)
    extra_shock_sensor = db.Column(db.Integer, default=0)
    extra_pir_normaal = db.Column(db.Integer, default=0)
    extra_rookmelder = db.Column(db.Integer, default=0)
    extra_pircam = db.Column(db.Integer, default=0)
    extra_bediendeel = db.Column(db.Integer, default=0)
    extra_sirene = db.Column(db.Integer, default=0)
    videodoorbell_free = db.Column(db.Boolean, default=False)
    videodoorbell_paid = db.Column(db.Boolean, default=False)
    
    # Calculated pricing (will be computed)
    base_installation_cost = db.Column(db.Float, nullable=False, default=999.99)
    total_product_cost_excl_vat = db.Column(db.Float, nullable=False, default=554.48)  # Base package excl VAT
    total_installation_cost = db.Column(db.Float, nullable=False, default=999.99)
    monthly_equipment_cost = db.Column(db.Float, nullable=False, default=32.99)
    monthly_monitoring_cost = db.Column(db.Float, nullable=False, default=8.50)
    monthly_maintenance_cost = db.Column(db.Float, nullable=False, default=8.50)
    
    # Status and timestamps
    status = db.Column(Enum(QuoteStatus), nullable=False, default='DRAFT')
    signature_data = db.Column(db.Text, nullable=True)  # Base64 signature
    signed_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Document reference
    document_id = db.Column(db.Integer, db.ForeignKey("documents.id"), nullable=True)

    # Relationships
    customer = db.relationship("Customer", backref=db.backref("simple_quotes", lazy="dynamic"))
    user = db.relationship("User", backref=db.backref("created_simple_quotes", lazy="dynamic"))
    document = db.relationship("Document", backref=db.backref("simple_quote", uselist=False))

    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'quote_number': self.quote_number,
            'customer_id': self.customer_id,
            'created_by': self.created_by,
            'category': self.category.lower() if self.category else None,
            'payment_terms': self.payment_terms.lower() if self.payment_terms else None,
            'discount_percentage': self.discount_percentage,
            'customer_name': self.customer_name,
            'customer_email': self.customer_email,
            'customer_phone': self.customer_phone,
            'customer_address': self.customer_address,
            'customer_city': self.customer_city,
            'customer_postal_code': self.customer_postal_code,
            'extra_magneetcontact': self.extra_magneetcontact,
            'extra_shock_sensor': self.extra_shock_sensor,
            'extra_pir_normaal': self.extra_pir_normaal,
            'extra_rookmelder': self.extra_rookmelder,
            'extra_pircam': self.extra_pircam,
            'extra_bediendeel': self.extra_bediendeel,
            'extra_sirene': self.extra_sirene,
            'videodoorbell_free': self.videodoorbell_free,
            'videodoorbell_paid': self.videodoorbell_paid,
            'base_installation_cost': self.base_installation_cost,
            'total_product_cost_excl_vat': self.total_product_cost_excl_vat,
            'total_installation_cost': self.total_installation_cost,
            'monthly_equipment_cost': self.monthly_equipment_cost,
            'monthly_monitoring_cost': self.monthly_monitoring_cost,
            'monthly_maintenance_cost': self.monthly_maintenance_cost,
            'status': self.status.lower() if self.status else None,
            'signature_data': self.signature_data,
            'signed_at': self.signed_at.isoformat() if self.signed_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'document_id': self.document_id
        }

    def calculate_totals(self):
        """
        Calculate all totals based on AMSPM lease system logic.

        LEASE SYSTEEM:
        - Basis installatiekosten: €999,99 incl BTW (zodat korting gegeven kan worden)
        - Maandelijkse kosten: €49,99 minimum (€32,99 apparatuur + €17,00 service)
        - Videodeurbel: €249,99 → kan gratis gemaakt worden
        """
        from app.utils.simple_quote_products import PRICING_CONFIG, calculate_monthly_increase, calculate_installation_cost

        # Product prijzen (INCL BTW) - particulieren systeem
        PRODUCT_PRICES_INCL_VAT = {
            'magneetcontact': 19.48,
            'shock_sensor': 26.72,
            'pir_normaal': 35.62,
            'rookmelder': 41.19,
            'pircam': 89.06,
            'bediendeel': 55.66,
            'sirene': 32.28,
            'videodoorbell': 249.99  # €249,99 incl BTW
        }

        # Bereken extra product kosten (INCL BTW)
        extra_cost_incl_vat = 0
        extra_items_count = 0

        # Tel alle extra producten op
        extra_cost_incl_vat += self.extra_magneetcontact * PRODUCT_PRICES_INCL_VAT['magneetcontact']
        extra_cost_incl_vat += self.extra_shock_sensor * PRODUCT_PRICES_INCL_VAT['shock_sensor']
        extra_cost_incl_vat += self.extra_pir_normaal * PRODUCT_PRICES_INCL_VAT['pir_normaal']
        extra_cost_incl_vat += self.extra_rookmelder * PRODUCT_PRICES_INCL_VAT['rookmelder']
        extra_cost_incl_vat += self.extra_pircam * PRODUCT_PRICES_INCL_VAT['pircam']
        extra_cost_incl_vat += self.extra_bediendeel * PRODUCT_PRICES_INCL_VAT['bediendeel']
        extra_cost_incl_vat += self.extra_sirene * PRODUCT_PRICES_INCL_VAT['sirene']

        # Tel extra items voor arbeid berekening
        extra_items_count = (self.extra_magneetcontact + self.extra_shock_sensor +
                           self.extra_pir_normaal + self.extra_rookmelder +
                           self.extra_pircam + self.extra_bediendeel + self.extra_sirene)

        # Videodeurbel logica (INCL BTW)
        if self.videodoorbell_paid and not self.videodoorbell_free:
            # Betaalde videodeurbel
            extra_cost_incl_vat += PRODUCT_PRICES_INCL_VAT['videodoorbell']
            extra_items_count += 1
        elif self.videodoorbell_free:
            # Gratis videodeurbel - voeg toe aan items maar niet aan kosten
            extra_items_count += 1

        # Bereken totale product kosten (basis + extra) - INCL BTW
        base_cost_incl_vat = PRICING_CONFIG['base_installation_cost_incl_vat']  # €999,99
        self.total_product_cost_excl_vat = base_cost_incl_vat + extra_cost_incl_vat

        # Bereken installatiekosten met correcte functie
        self.total_installation_cost = calculate_installation_cost(
            extra_cost_incl_vat,
            extra_items_count,
            self.videodoorbell_free,
            self.videodoorbell_paid
        )

        # Bereken maandelijkse apparatuur kosten
        self.monthly_equipment_cost = calculate_monthly_increase(extra_cost_incl_vat)

        # Vaste maandelijkse kosten
        self.monthly_monitoring_cost = PRICING_CONFIG['monthly_monitoring']
        self.monthly_maintenance_cost = PRICING_CONFIG['monthly_maintenance']

    def get_max_discount(self):
        """
        Get maximum allowed discount based on payment terms.

        LEASE SYSTEEM KORTING:
        - 1 termijn: 25% maximum korting (€999,99 → €749,99 minimum)
        - 2 termijnen: 15% maximum korting
        - 3 termijnen: 10% maximum korting
        """
        if self.payment_terms == 'ONE_TERM':
            return 25.0  # Maximum korting voor lease systeem
        elif self.payment_terms == 'TWO_TERMS':
            return 15.0
        elif self.payment_terms == 'THREE_TERMS':
            return 10.0
        return 0.0

    def get_final_installation_cost(self):
        """Get final installation cost after discount (lease payment)."""
        discount_amount = self.total_installation_cost * (self.discount_percentage / 100)
        final_cost = self.total_installation_cost - discount_amount

        # Minimum check - basis €999,99 met max 25% korting = €749,99 minimum
        from app.utils.simple_quote_products import PRICING_CONFIG
        minimum_cost = PRICING_CONFIG['base_installation_cost_incl_vat'] * 0.75  # €749,99

        return max(final_cost, minimum_cost)

    def get_total_monthly_cost(self):
        """
        Get total monthly cost (MINIMUM €49,99).

        Samenstelling:
        - Apparatuur lease: variabel (basis €32,99)
        - Meldkamer: €8,50
        - Onderhoud: €8,50
        """
        total = self.monthly_equipment_cost + self.monthly_monitoring_cost + self.monthly_maintenance_cost

        # Minimum maandelijkse kosten
        from app.utils.simple_quote_products import PRICING_CONFIG
        minimum_monthly = PRICING_CONFIG['base_monthly_total']  # €49,99

        return max(total, minimum_monthly)
